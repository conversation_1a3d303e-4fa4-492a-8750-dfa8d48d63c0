<template>
	<div id="app">
		<router-view />
	</div>
</template>

<script>
	import {
		mapActions,
		mapState,
	} from 'vuex';

	const webSocketEvents = new Map()

	export default {
		name: 'App',
		onLaunch: async function () {

		},
		provide: {
			bind(eventName, cb) {
				webSocketEvents.set(eventName, cb)
			},
			unbind(eventName) {
				webSocketEvents.delete(eventName)
			},
		},
		computed: {
			...mapState({
				userInfo: (state) => state.user,
				websocket: state => state.websocket.socketTask,
			}),
		},
		watch:{
			userInfo:{
				deep:true,
				immediate:true,
				handler(){
					this.webSocketContent()
				}
			}
		},
		created: async function () {
			// console.log('app.vue created')
		},
		mounted(){
			
		},
		methods: {
			...mapActions(['socketInit']),
			webSocketContent(){
				if(!this.userInfo) return
				if(!this.userInfo.id) return
				if(this.websocket) return

				this.socketInit('/ws/ChatHub')
				if (this.userInfo) {
					this.websocket.on('connected', ()=> {
						let param = {
							id: this.userInfo.id,
							nickname: this.userInfo.nickname,
							phone: this.userInfo.phone,
							headimg: this.userInfo.headimgurl,
							gender: this.userInfo.gender,
						}
						this.websocket.invoke('setMyInfo', param)
					})
				}
				// //接收信息绑定
				this.websocket.on('message', (res) => {
					switch (res.type) {
						case 'notify':
							if(res.context.type.indexOf('web')>=0)	this.$baseMessage(res.context.title+': '+res.context.content, 'success', 'ut-hey-message-success')
							break
						case 'notify_audio':
							// this.$audio.play(res.context.url)
							break
						default:
							this.dispatchEvent(res.type,res)
							break
					}
				})
				this.websocket.on('logMessage', res=> {
					switch (res.type) {
						case 'setMyInfo':
							this.websocket.invoke('joinRoom', 'room')
							break;
						default:
							this.dispatchEvent(res.type,res)
							break
					}
				})
			},
			dispatchEvent(eventName,content) {
				if (webSocketEvents.has(eventName)) {
					const cb = webSocketEvents.get(eventName)
					cb(content)
				}
			}
		}
	}
</script>
