{"请选择字段": "Veuillez sélectionner un champ", "计数": "<PERSON><PERSON>er", "合计": "Somme", "平均值": "<PERSON><PERSON><PERSON>", "最小值": "Minimum", "最大值": "Maximum", "此格式不支持该文本": "Ce format ne prend pas en charge ce texte", "二维码生成失败": "Échec de la génération de code QR", "字体行高": "Hauteur de police", "默认": "Défaut", "字体": "Police", "宋体": "Sim<PERSON>un", "微软雅黑": "Microsoft YaHei", "字体大小": "Taille de police", "字体粗细": "Épaisseur de police", "更细": "Plus léger", "粗体": "Gras", "粗体+": "Plus gras", "字间距": "Espacement des lettres", "左右对齐": "<PERSON><PERSON><PERSON><PERSON>", "居左": "G<PERSON><PERSON>", "居中": "Centré", "居右": "<PERSON><PERSON><PERSON>", "两端对齐": "<PERSON><PERSON><PERSON><PERSON>", "标题显示隐藏": "Affichage du titre", "显示": "<PERSON><PERSON><PERSON><PERSON>", "隐藏": "Masquer", "表格边框": "<PERSON><PERSON><PERSON> du tableau", "有边框": "O<PERSON>", "无边框": "Non", "表头边框": "Bordure d'en-tête", "左边框": "G<PERSON><PERSON>", "右边框": "<PERSON><PERSON><PERSON>", "左右边框": "Gauche et droite", "上边框": "<PERSON><PERSON>", "下边框": "Bas", "上下边框": "Haut et bas", "表头单元格边框": "Bordure de cellule d'en-tête", "表尾边框": "Bordure de pied de page", "表尾单元格边框": "Bordure de cellule de pied de page", "表头行高": "<PERSON><PERSON> de ligne d'en-tête", "表头字体大小": "Taille de police d'en-tête", "表头字体粗细": "Épaisseur de police d'en-tête", "表体单元格边框": "Bordure de cellule de corps", "表体行高": "<PERSON>ur de ligne de corps", "表头背景": "Arrière-plan de l'en-tête", "线宽": "<PERSON>ur de <PERSON>", "边框大小": "<PERSON><PERSON>", "条形码格式": "Type de code à barres", "商品条码": "Code à barres de produit", "条形码": "Code à barres", "物流": "Logistique", "邮政和快递编码": "Code postal et messagerie", "医疗产品编码": "Code de produit médical", "不常用编码": "Code inhabituel", "附加组件": "Composant supplémentaire", "实验编码": "Code expérimental", "条码类型": "Type de code à barres", "二维码类型": "Type de QR code", "二维码容错率": "Niveau de correction d'erreur de QR code", "字体颜色": "Couleur de police", "文本修饰": "Décoration de texte", "下划线": "<PERSON><PERSON><PERSON>", "上划线": "<PERSON><PERSON>", "穿梭线": "<PERSON><PERSON>", "字段名": "Nom de champ", "请输入字段名": "Veuillez saisir le nom du champ", "标题": "Titre", "请输入标题": "Veuillez saisir le titre", "测试数据": "Donn<PERSON> de test", "仅字段名称存在时有效": "Valide uniquement lorsque le nom de champ existe", "位置坐标": "Coordonnée de position", "X位置(左)": "Position X (gauche)", "Y位置(上)": "Position Y (haut)", "同步": "Synchroniser", "不同步": "Non synchronisé", "宽高大小": "Largeur et hauteur", "宽": "<PERSON><PERSON>", "高": "<PERSON><PERSON>", "图片地址": "URL de l'image", "请输入图片地址": "Veuillez saisir l'URL de l'image", "选择": "Choi<PERSON>", "图片缩放": "Mise à l'échelle de l'image", "等比": "Proportionnel", "裁切": "<PERSON><PERSON><PERSON>", "填充": "<PERSON><PERSON><PERSON><PERSON>", "原始尺寸": "<PERSON>lle d'origine", "颜色": "<PERSON><PERSON><PERSON>", "边框颜色": "<PERSON><PERSON><PERSON> de bordure", "水印功能": "Filigrane", "水印内容": "Contenu du filigrane", "旋转角度": "Angle de rotation", "水平密度": "Densité horizontale", "垂直密度": "Densité verticale", "水印时间": "Heure du filigrane", "时间格式": "Format de l'heure", "页码格式": "Format du numéro de page", "显示页码": "<PERSON><PERSON><PERSON><PERSON> le numéro de page", "页码续排": "Con<PERSON>uer le numéro de page", "续排": "<PERSON><PERSON><PERSON>", "重排": "Réinitialiser", "每行缩进": "Retrait", "显示规则": "<PERSON><PERSON><PERSON> d'affichage", "始终隐藏": "Toujours masquer", "首页": "Première page", "奇数页": "Pages impaires", "偶数页": "Pages paires", "尾页": "Dernière page", "强制分页": "Forcer saut de page", "是": "O<PERSON>", "否": "Non", "打印规则": "R<PERSON><PERSON> d'impression", "保持奇数": "Garder impaires", "保持偶数": "Gard<PERSON> paires", "分页规则": "<PERSON><PERSON><PERSON> de saut de page", "不分页": "Pas de saut de page", "移除段落左侧空白": "Supprimer l'espace vide à gauche", "移除": "<PERSON><PERSON><PERSON><PERSON>", "不移除": "Ne pas supprimer", "首页页尾": "Pied de page première page", "尾页页尾": "Pied de page dernière page", "偶数页页尾": "Pied de page pages paires", "奇数页页尾": "Pied de page pages impaires", "位置固定": "Position fixe", "拖动方向": "Direction de glissement", "横向": "Horizontal", "竖向": "Vertical", "左偏移": "Décalage à gauche", "偏移量": "Décalage", "最低高度": "Hauteur minimum", "文本过短或为空时的高度": "Hauteur lorsque le texte est trop court ou vide", "隐藏规则": "<PERSON><PERSON><PERSON> de masqua<PERSON>", "表体行边框": "Bordure de ligne du corps", "元素层级": "Index Z", "边框设置": "Paramètres de bordure", "实线": "<PERSON><PERSON>", "虚线": "<PERSON><PERSON><PERSON>", "左内边距": "Marge intérieure gauche", "上内边距": "Marge intérieure haut", "右内边距": "Marge intérieure droite", "下内边距": "Marge intérieure bas", "样式": "Style", "边框样式": "Style de bordure", "长虚线": "<PERSON><PERSON><PERSON> long", "短虚线": "Pointillé court", "背景颜色": "<PERSON><PERSON><PERSON> de fond", "纸张方向(仅自定义纸质有效)": "Orientation du papier (uniquement papier personnalisé)", "纵向": "Portrait", "上下对齐": "Alignement vertical", "垂直居中": "Centré verticalement", "底部": "Bas", "文本换行": "Césure de texte", "不换行": "Pas de c<PERSON>", "不换行&隐藏": "Pas de césure et masquer", "不换行&省略": "Pas de césure et points de suspension", "打印类型": "Type d'impression", "文本": "Texte", "二维码": "QR code", "字段类型": "Type de champ", "默认(文本)": "<PERSON><PERSON><PERSON><PERSON> (texte)", "序号": "Index", "图片": "Image", "单元格高度": "Hauteur de cellule", "条形码、二维码以及图片有效": "Code à barres, QR code et image sont valides", "底部聚合标题": "Titre du récapitulatif", "底部聚合文本": "Texte du récapitulatif", "聚合类型": "Type de récapitulatif", "底部聚合合并列数": "Colonnes fusionnées du récapitulatif", "合并列数": "Colonnes fusionnées", "底部聚合类型左右对齐": "Alignement du récapitulatif", "整数": "<PERSON><PERSON>", "保留%s位": "Garder %s décimales", "底部聚合小数": "Décimales du récapitulatif", "转大小写": "Majuscules ou minuscules", "底部聚合类型": "Type de récapitulatif", "不聚合": "Pas de récapitula<PERSON><PERSON>", "仅文本": "Texte uniquement", "顶部偏移": "Décalage supérieur", "一行多组": "Plusieurs groupes sur une ligne", "一行二列": "Une ligne deux colonnes", "一行三列": "Une ligne trois colonnes", "一行四列": "Une ligne quatre colonnes", "一行多组间隔": "Espacement de plusieurs groupes sur une ligne", "表格头显示": "Affichage de l'en-tête du tableau", "每页显示": "Afficher par page", "首页显示": "Afficher sur la première page", "不显示": "Ne pas afficher", "数据类型": "Type de données", "日期时间": "<PERSON><PERSON><PERSON><PERSON>", "布尔": "Booléen", "格式": "Format", "格式化函数": "Fonction de mise en forme", "样式函数": "Fonction de style", "行/列合并函数": "Fusionner ligne/colonne", "跨页合并是否清除": "Annuler fusion sur saut de page", "表格脚函数": "Fonction pied de tableau", "分组字段函数": "Fonction Champ de regroupement", "分组头格式化函数": "Fonction de mise en forme en-tête de groupe", "分组头信息": "Informations en-tête de groupe", "分组脚格式化函数": "Fonction de mise en forme pied de groupe", "分组脚信息": "Informations pied de groupe", "多组表格脚函数": "Fonction pied de tableau multi-groupe", "行样式函数": "Fonction style de ligne", "单元格左右对齐": "Alignement des cellules", "单元格上下对齐": "Alignement vertical des cellules", "上": "<PERSON><PERSON>", "中": "Milieu", "表格头单元格左右对齐": "Alignement des cellules d'en-tête", "单元格样式函数": "Fonction style de cellule", "表格头样式函数": "Fonction style d'en-tête", "单元格格式化函数": "Fonction de mise en forme de cellule", "单元格渲染函数": "Fonction d'affichage de cellule", "自动补全": "Saisie automatique", "每页最大行数": "Lignes max par page", "表格脚显示": "Affichage pied de tableau", "最后显示": "Afficher sur dernière page", "没有足够空间进行表格分页，请调整页眉/页脚线": "Pas assez d'espace pour la pagination du tableau, veuillez ajuster la ligne d'entête/pied de page", "没有足够空间,显示下方内容, 可分页高度": "Pas assez de place, afficher le contenu ci-dessous, la hauteur peut être paginée :", "列属性": "Propriété de colonne", "在上方插入行": "Ins<PERSON>rer ligne au-dessus", "在下方插入行": "Insérer ligne en dessous", "向左方插入列": "Insérer colonne à gauche", "向右方插入列": "Insérer colonne à droite", "删除行": "Supp<PERSON>er ligne", "删除列": "Supprimer colonne", "对齐": "<PERSON><PERSON><PERSON>", "左": "G<PERSON><PERSON>", "左右居中": "Centrer", "右": "<PERSON><PERSON><PERSON>", "下": "Bas", "合并单元格": "Fusionner cellule", "解开单元格": "Défusionner cellule", "条形码生成失败": "Échec de la génération de code à barres", "请检查 hiprint.init 的 provider 是否配置了": "Veuillez vérifier si le fournisseur de hiprint.init est configuré", "已移除'tableCustom',请替换使用'table'详情见更新记录": "'TableCustom' a été supprimé, veuillez le remplacer par 'table', voir le journal de mise à jour pour plus de détails", "确定": "Confirmer", "删除": "<PERSON><PERSON><PERSON><PERSON>", "连接客户端失败": "Échec de connexion au client", "基础": "De base", "边框": "Bordure", "列": "Colonne", "高级": "<PERSON><PERSON><PERSON>"}