{"请选择字段": "請選擇欄位", "计数": "計數", "合计": "合計", "平均值": "平均值", "最小值": "最小值", "最大值": "最大值", "此格式不支持该文本": "此格式不支援該文字", "二维码生成失败": "二維碼產生失敗", "字体行高": "字體行高", "默认": "預設", "字体": "字型", "宋体": "宋體", "微软雅黑": "微軟雅黑", "字体大小": "字型大小", "字体粗细": "字型粗細", "更细": "更細", "粗体": "粗體", "粗体+": "粗體+", "字间距": "字間距", "左右对齐": "左右對齊", "居左": "靠左", "居中": "置中", "居右": "靠右", "两端对齐": "兩端對齊", "标题显示隐藏": "標題顯示隱藏", "显示": "顯示", "隐藏": "隱藏", "表格边框": "表格邊框", "有边框": "有邊框", "无边框": "無邊框", "表头边框": "表頭邊框", "左边框": "左邊框", "右边框": "右邊框", "左右边框": "左右邊框", "上边框": "上邊框", "下边框": "下邊框", "上下边框": "上下邊框", "表头单元格边框": "表頭儲存格邊框", "表尾边框": "表尾邊框", "表尾单元格边框": "表尾儲存格邊框", "表头行高": "表頭列高", "表头字体大小": "表頭字型大小", "表头字体粗细": "表頭字型粗細", "表体单元格边框": "表體儲存格邊框", "表体行高": "表體列高", "表头背景": "表頭背景", "线宽": "線寬", "边框大小": "邊框大小", "条形码格式": "條碼格式", "商品条码": "商品條碼", "条形码": "條碼", "物流": "物流", "邮政和快递编码": "郵政和快遞編碼", "医疗产品编码": "醫療產品編碼", "不常用编码": "不常用編碼", "附加组件": "附加元件", "实验编码": "實驗編碼", "条码类型": "條碼類型", "二维码类型": "二維碼類型", "二维码容错率": "二維碼容錯率", "字体颜色": "字型顏色", "文本修饰": "文字修飾", "下划线": "底線", "上划线": "上線", "穿梭线": "穿梭線", "字段名": "欄位名稱", "请输入字段名": "請輸入欄位名稱", "标题": "標題", "请输入标题": "請輸入標題", "测试数据": "測試資料", "仅字段名称存在时有效": "僅欄位名稱存在時有效", "位置坐标": "位置座標", "X位置(左)": "X位置(左)", "Y位置(上)": "Y位置(上)", "同步": "同步", "不同步": "不同步", "宽高大小": "寬高大小", "宽": "寬", "高": "高", "图片地址": "圖片網址", "请输入图片地址": "請輸入圖片網址", "选择": "選擇", "图片缩放": "圖片縮放", "等比": "等比", "裁切": "裁切", "填充": "填充", "原始尺寸": "原始尺寸", "颜色": "顏色", "边框颜色": "邊框顏色", "水印功能": "浮水印功能", "水印内容": "浮水印內容", "旋转角度": "旋轉角度", "水平密度": "水平密度", "垂直密度": "垂直密度", "水印时间": "浮水印時間", "时间格式": "時間格式", "页码格式": "頁碼格式", "显示页码": "顯示頁碼", "页码续排": "頁碼續排", "续排": "續排", "重排": "重排", "每行缩进": "每行縮排", "显示规则": "顯示規則", "始终隐藏": "始終隱藏", "首页": "首頁", "奇数页": "奇數頁", "偶数页": "偶數頁", "尾页": "尾頁", "强制分页": "強制分頁", "是": "是", "否": "否", "打印规则": "列印規則", "保持奇数": "保持奇數", "保持偶数": "保持偶數", "分页规则": "分頁規則", "不分页": "不分頁", "移除段落左侧空白": "移除段落左側空白", "移除": "移除", "不移除": "不移除", "首页页尾": "首頁頁尾", "尾页页尾": "尾頁頁尾", "偶数页页尾": "偶數頁頁尾", "奇数页页尾": "奇數頁頁尾", "位置固定": "位置固定", "拖动方向": "拖動方向", "横向": "橫向", "竖向": "豎向", "左偏移": "左偏移", "偏移量": "偏移量", "最低高度": "最低高度", "文本过短或为空时的高度": "文本過短或為空時的高度", "隐藏规则": "隱藏規則", "表体行边框": "表體列邊框", "元素层级": "元素層級", "边框设置": "邊框設置", "实线": "實線", "虚线": "虛線", "左内边距": "左內邊距", "上内边距": "上內邊距", "右内边距": "右內邊距", "下内边距": "下內邊距", "样式": "樣式", "边框样式": "邊框樣式", "长虚线": "長虛線", "短虚线": "短虛線", "背景颜色": "背景顏色", "纸张方向(仅自定义纸质有效)": "紙張方向(僅自訂紙質有效)", "纵向": "縱向", "上下对齐": "上下對齊", "垂直居中": "垂直置中", "底部": "底部", "文本换行": "文字換行", "不换行": "不換行", "不换行&隐藏": "不換行&隱藏", "不换行&省略": "不換行&省略", "打印类型": "列印類型", "文本": "文字", "二维码": "二維碼", "字段类型": "欄位類型", "默认(文本)": "預設(文字)", "序号": "序號", "图片": "圖片", "单元格高度": "儲存格高度", "条形码、二维码以及图片有效": "條碼、二維碼以及圖片有效", "底部聚合标题": "底部彙總標題", "底部聚合文本": "底部彙總文字", "聚合类型": "彙總類型", "底部聚合合并列数": "底部彙總合併列數", "合并列数": "合併列數", "底部聚合类型左右对齐": "底部彙總類型左右對齊", "整数": "整數", "保留%s位": "保留%s位", "底部聚合小数": "底部彙總小數", "转大小写": "轉大小寫", "底部聚合类型": "底部彙總類型", "不聚合": "不彙總", "仅文本": "僅文字", "顶部偏移": "頂部偏移", "一行多组": "一行多組", "一行二列": "一行二欄", "一行三列": "一行三欄", "一行四列": "一行四欄", "一行多组间隔": "一行多組間隔", "表格头显示": "表格頭顯示", "每页显示": "每頁顯示", "首页显示": "首頁顯示", "不显示": "不顯示", "数据类型": "資料類型", "日期时间": "日期時間", "布尔": "布爾", "格式": "格式", "格式化函数": "格式化函數", "样式函数": "樣式函數", "行/列合并函数": "行/列合併函數", "跨页合并是否清除": "跨頁合併是否清除", "表格脚函数": "表格腳函數", "分组字段函数": "分組欄位函數", "分组头格式化函数": "分組頭格式化函數", "分组头信息": "分組頭資訊", "分组脚格式化函数": "分組腳格式化函數", "分组脚信息": "分組腳資訊", "多组表格脚函数": "多組表格腳函數", "行样式函数": "行樣式函數", "单元格左右对齐": "儲存格左右對齊", "单元格上下对齐": "儲存格上下對齐", "上": "上", "中": "中", "表格头单元格左右对齐": "表格頭儲存格左右對齐", "单元格样式函数": "儲存格樣式函數", "表格头样式函数": "表格頭樣式函數", "单元格格式化函数": "儲存格格式化函數", "单元格渲染函数": "儲存格渲染函數", "自动补全": "自動完成", "每页最大行数": "每頁最大行數", "表格脚显示": "表格腳顯示", "最后显示": "最後顯示", "没有足够空间进行表格分页，请调整页眉/页脚线": "沒有足夠空間進行表格分頁,請調整頁眉/頁腳線", "没有足够空间,显示下方内容, 可分页高度": "沒有足夠空間,顯示下方內容, 可分頁高度", "列属性": "列屬性", "在上方插入行": "在上方插入列", "在下方插入行": "在下方插入列", "向左方插入列": "向左方插入列", "向右方插入列": "向右方插入列", "删除行": "刪除列", "删除列": "刪除列", "对齐": "對齊", "左": "左", "左右居中": "左右置中", "右": "右", "下": "下", "合并单元格": "合併儲存格", "解开单元格": "解開儲存格", "条形码生成失败": "條碼生成失敗", "请检查 hiprint.init 的 provider 是否配置了": "請檢查 hiprint.init 的 provider 是否配置了", "已移除'tableCustom',请替换使用'table'详情见更新记录": "已移除'tableCustom',請替換使用'table'詳情見更新記錄", "确定": "確定", "删除": "刪除", "连接客户端失败": "連接客戶端失敗", "基础": "基礎", "边框": "邊框", "列": "列", "高级": "高級"}