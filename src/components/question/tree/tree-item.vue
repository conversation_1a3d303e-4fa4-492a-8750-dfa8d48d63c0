<template>
	<li class="s-tree-item" @click.stop="nodeClick">
		<i :class="[statusIconClass]" @click.stop="expand"></i>
		<el-link :class="['item-name', { 'is-active': activeValue === node.name }]" :underline="false">
			<i :class="[node.icon, nodeIconClass]" @click="iconClick"></i>
			{{ node.name }}
		</el-link>
		<!-- <el-link :underline="false">无下划线</el-link> -->
		<slot></slot>
	</li>
</template>
<script>
import Bus from './bus.vue'

export default {
	name: 'STreeItem',
	props: {
		node: {
			type: Object,
			default: () => {},
		},
	},
	computed: {
		statusIconClass() {
			return !this.node.isParent ? '' : this.node.isOpen ? 'el-icon-remove-outline' : 'el-icon-circle-plus-outline'
		},
		nodeIconClass() {
			return this.node.isOpen ? this.node.openedIcon : this.node.closedIcon
		},
		activeValue() {
			return Bus.activeValue
		},
	},
	methods: {
		nodeClick() {
			Bus.$emit('active', this.node.name)
			let _this = this
			while (isNotTree(_this.$parent)) {
				_this = _this.$parent
			}
			_this.$emit('node-click', this.node)
			console.log(this.node.name)
		},
		expand() {
			this.$emit('expand', this.node)
		},
		iconClick() {
			console.log('iconClick')
		},
	},
}

function isNotTree(vm) {
	let classStr = vm.$el.className
	if (classStr.indexOf('s-tree') !== -1) {
		return true
	}
	return false
}
</script>
<style scoped lang="scss">
.fa-square-check:before {
	content: '\f14a';
}
.s-tree-item {
	&:last-child {
		/*用来遮挡ul:before超出来的border*/
		&:after {
			content: '';
			position: absolute;
			left: -15px;
			top: 16px;
			bottom: 16px;
			width: 2px;
			background: #fff;
			z-index: 9;
		}
	}
	.iButton {
		color: #1d8ce0;
		padding-right: 5px;
		&:hover:before {
			color: #58b7ff;
		}
	}
	.item-name {
		color: #333;
		height: 30px;
		line-height: 30px;
		padding: 2px;
		// font-size: 14px;
		display: inline-block;
		box-sizing: border-box;

		&.is-active {
			color: #1d8ce0;
		}

		&:hover {
			color: #1d8ce0;
		}
	}
}
</style>