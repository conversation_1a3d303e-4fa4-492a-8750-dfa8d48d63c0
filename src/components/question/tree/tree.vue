<template>
	<div class="ut-tree">
		<ul class="s-tree" :class="{ noBorder: isNoBorder }">
			<ut-tree-item v-for="node in innerTreeData" :key="node.name" :node="node" :class="{ noBorder: isNoBorder }" @expand="expand">
				<ut-tree v-if="node.isOpen && node.children" :tree-data="node.children" />
			</ut-tree-item>
		</ul>
	</div>
</template>
<script>
import UtTreeItem from './tree-item.vue'
export default {
	name: 'UtTree',
	components: {
		UtTreeItem,
	},
	props: {
		treeData: {
			type: Array,
			require: true,
			default: () => [],
		},
	},
	data() {
		return {
			innerTreeData: {},
		}
	},
	computed: {
		isNoBorder() {
			if (this.innerTreeData.length <= 1 && this.innerTreeData[0] && this.innerTreeData[0].hasParent) {
				return true
			}
			return false
		},
	},
	watch: {
		treeData: {
			immediate: true,
			deep: true,
			handler(obj) {
				this.innerTreeData = obj
			},
		},
	},
	methods: {
		expand(node) {
			// eslint-disable-next-line no-prototype-builtins
			if (!node.hasOwnProperty('isOpen')) {
				this.$set(node, 'isOpen', false)
			}
			node.isOpen = !node.isOpen
		},
	},
}
</script>
<style scoped lang="scss">
.ut-tree {
	max-height: 450px;
}
.s-tree {
	cursor: pointer;
	margin-left: 20px;
	position: relative;
	&:before {
		content: '';
		// height: 100%;
		position: absolute;
		left: -15px;
		top: -7px;
		bottom: 16px;
		border-left: 1px solid #999;
	}
	.s-tree-item {
		line-height: 30px;
		position: relative;
		&:before {
			content: '';
			width: 10px;
			position: absolute;
			left: -15px;
			top: 16px;
			border-top: 1px solid #999;
		}
	}
}
.noBorder:before {
	border: none !important;
}

* {
	margin: 0;
	padding: 0;
	border: 0;
	font-size: 100%;
	font: inherit;
	vertical-align: baseline;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
	display: block;
}
body {
	line-height: 1;
}
ol,
ul {
	list-style: none;
}
blockquote,
q {
	quotes: none;
}
blockquote:before,
blockquote:after,
q:before,
q:after {
	content: '';
	content: none;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}
a {
	text-decoration: none;
	color: #444;
}
a:active,
a:hover {
	outline-width: 0;
}
button,
input,
optgroup,
select,
textarea {
	font-family: sans-serif;
	font-size: 100%;
	line-height: 1.15;
	margin: 0;
}
button {
	padding: 0;
	outline: none;
	border: 0;
}

input {
	outline: none;
	border: 1px solid #bfcbd9;
	padding: 3px 10px;
}
input:focus {
	outline: none;
	border-color: #20a0ff;
}
</style>