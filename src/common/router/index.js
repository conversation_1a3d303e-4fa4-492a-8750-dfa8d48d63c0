/**
 * @description router全局配置
 */
import Vue from 'vue'
import VueRouter from 'vue-router'
import { publicPath, routerMode } from '@/config'
import constantRoutes from '@/path/constantRoutes.js'

Vue.use(VueRouter)

const router = createRouter()

function fatteningRoutes(routes) {
	return routes.flatMap((route) => {
		return route.children ? fatteningRoutes(route.children) : route
	})
}

export function resetRouter(routes = constantRoutes) {
	routes.map((route) => {
		if (route.children) {
			route.children = fatteningRoutes(route.children)
		}
	})
	router.matcher = createRouter(routes).matcher
}

function createRouter(routes = constantRoutes) {
	return new VueRouter({
		base: publicPath,
		mode: routerMode,
		scrollBehavior(){
			return {y:0}
		},
		routes: routes,
	})
}

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
	return originalPush.call(this, location).catch((err) => err)
}

export default router
