import Vue from 'vue'
import axios from 'axios'
import {
	API_URL,
	messageName,
	contentType,
	debounce,
	requestTimeout,
	statusName,
	successCode,
	/* tokenName, */
} from '@/config'
import store from '@/common/store'
import qs from 'qs'
import router from '@/common/router'
import { isArray } from '@/common/utils/validate'
import { needErrorLog, addErrorLog } from '@/ut/plugins/errorLog'
import apiList from '@/api/index.js'

let loadingInstance

let refreshToking = false

let requests = []

// 操作正常Code数组
const codeVerificationArray = isArray(successCode) ? [...successCode] : [...[successCode]]

const CODE_MESSAGE = {
	0: '未可知错误，可能是因为后端不支持跨域CORS、接口地址不存在等问题引起',
	200: '服务器成功返回请求数据',
	201: '新建或修改数据成功',
	202: '一个请求已经进入后台排队(异步任务)',
	204: '删除数据成功',
	400: '发出信息有误',
	401: '用户没有权限(令牌失效、用户名、密码错误、登录过期)',
	402: '令牌过期',
	403: '用户得到授权，但是访问是被禁止的',
	404: '访问资源不存在',
	406: '请求格式不可得',
	410: '请求资源被永久删除，且不会被看到',
	500: '服务器发生错误',
	502: '网关错误',
	503: '服务不可用，服务器暂时过载或维护',
	504: '网关超时',
}

/**
 * axios请求拦截器配置
 * @param config
 * @returns {any}
 */
const requestConf = (config) => {
	config.baseURL = API_URL
	if (config.custom.auth) {
		const token = store.getters['user/token']
		if (token) config.headers['Authorization'] = `Bearer ${token}`
	}

	if (config.data && config.headers['Content-Type'] === 'application/x-www-form-urlencoded;charset=UTF-8') config.data = qs.stringify(config.data)
	if (debounce.some((item) => config.url.includes(item))) loadingInstance = Vue.prototype.$baseLoading()

	return config
}

/**
 * 刷新刷新令牌
 * @param config 过期请求配置
 * @returns {any} 返回结果
 */
const tryRefreshToken = async (config) => {
	if (!refreshToking) {
		refreshToking = true
		try {
			const {
				data: { token },
			} = await this.$ut.api('user/refreshToken')
			if (token) {
				store.dispatch('user/setToken', token).then(() => {})
				// 已经刷新了token，将所有队列中的请求进行重试
				requests.forEach((cb) => cb(token))
				requests = []
				return instance(requestConf(config))
			}
		} catch (error) {
			console.error('refreshToken error =>', error)
			router.push({ path: '/login', replace: true }).then(() => {})
		} finally {
			refreshToking = false
		}
	} else {
		return new Promise((resolve) => {
			// 将resolve放进队列，用一个函数形式来保存，等token刷新后直接执行
			requests.push(() => {
				resolve(instance(requestConf(config)))
			})
		})
	}
}

/**
 * axios响应拦截器
 * @param config 请求配置
 * @param data response数据
 * @param status HTTP status
 * @param statusText HTTP status text
 * @returns {Promise<*|*>}
 */
const responseHandle = async ({ config, data, status = 0, statusText,headers }) => {
	if (loadingInstance) loadingInstance.close()
	// 若data.code存在，覆盖默认code
	let code = data && data[statusName] ? data[statusName] : status
	// 若code属于操作正常code，则code修正为200
	if (codeVerificationArray.indexOf(code) + 1) code = 200
	switch (code) {
		case 200:
			if(config.responseType){
				return { data, headers }
			}
			else
			{
				return data
			}
		case 401:
			store.dispatch('user/resetAll').then(() => router.push({ path: '/login', replace: true }).then(() => {}))
			break
		case 402:
			return await tryRefreshToken(config)
		case 403:
			router.push({ path: '/403' }).then(() => {})
			break
	}
	// 异常处理
	// 若data.message存在，覆盖默认提醒消息
	const errMsg = `${data && data[messageName] ? data[messageName] : CODE_MESSAGE[code] ? CODE_MESSAGE[code] : statusText}`
	Vue.prototype.$baseMessage(errMsg, 'error', 'ut-hey-message-error')
	// 是否添加错误日志(与errorHandler钩子触发逻辑一致)
	if (needErrorLog()) addErrorLog({ message: errMsg, stack: data, isRequest: true })
	return Promise.reject(data)
}

/**
 * @description axios初始化
 */
const instance = axios.create({
	API_URL,
	timeout: requestTimeout,
	headers: {
		'Content-Type': contentType,
	},
})

/**
 * @description axios请求拦截器
 */
instance.interceptors.request.use(requestConf, (error) => {
	return Promise.reject(error)
})

/**
 * @description axios响应拦截器
 */
instance.interceptors.response.use(
	(response) => responseHandle(response),
	(error) => {
		const { response = {} } = error
		return responseHandle(response)
	}
)

function getApiObj(url) {
	let apiArray = []
	if (url.indexOf('.') > 0) {
		apiArray = url.split('.')
	} else {
		apiArray = url.split('/')
	}
	let apiData = apiList
	apiArray.forEach((v) => {
		apiData = apiData[v]
	})
	return apiData
}

instance.addQueryString=function(params) {
	let paramsData = ''
	Object.keys(params).forEach(key => {
		paramsData += key + '=' + encodeURIComponent(params[key]) + '&'
	})
	return paramsData.substring(0, paramsData.length - 1)
}

instance.apiUrl=function(url){
	const apiData = getApiObj(url)
	let mergeUrl = apiData.url
	return mergeUrl
}

instance.api = function (url, data = {}) {
	const apiData = getApiObj(url)
	let mergeUrl = apiData.url
	if(apiData.method=='GET'){
		let query = this.addQueryString(data);
		mergeUrl += mergeUrl.indexOf('?') === -1 ? `?${query}` : `&${query}`
	}
	let requestData = {
		url: mergeUrl,
		data,
		method: apiData.method,
		custom: {
			// 自定义数据，不会请求到服务器
			auth: apiData.auth || false,
		},
	}
	if(apiData.responseType) requestData.responseType =  apiData.responseType
	return this.request(requestData)
}
export default instance
