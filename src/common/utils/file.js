export function download(res, defaultName) {
    const url = window.URL.createObjectURL(new Blob([res.data]))
    let downloadName = ''
    if (res.headers['content-disposition']) {
        let tempstr = res.headers['content-disposition'].split('filename*=')[1]
        downloadName = decodeURI(tempstr.split("'")[2])
    }
    if (!downloadName) downloadName = defaultName
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', downloadName)
    document.body.appendChild(link)
    link.click()
    URL.revokeObjectURL(link.href) // 释放URL 对象
    document.body.removeChild(link)
}