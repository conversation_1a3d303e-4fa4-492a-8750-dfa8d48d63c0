export default {
	
	/**
	 * fn：检测图片协议，主要用于检测海报图片协议。
	 * param(imgPath): 图片地址。
	 */

	checkImgHttp(imgPath) {
		let newPath = '';
		let pathArr = imgPath.split('://');
		let ishttps = 'https:' == window.location.protocol ? true : false;
		ishttps ? (pathArr[0] = 'https') : (pathArr[0] = 'http');
		newPath = pathArr.join('://');
		return newPath;
	},

	/**
	 * 格式化时间
	 */
	//时间格式化 天时分秒
	format(t) {
		let format = {
			d: '00',
			h: '00',
			m: '00',
			s: '00',
		}
		if (t > 0) {
			let d = Math.floor(t / 86400)
			let h = Math.floor((t / 3600) % 24)
			let m = Math.floor((t / 60) % 60)
			let s = Math.floor(t % 60)
			format.d = d < 10 ? '0' + d : d
			format.h = h < 10 ? '0' + h : h
			format.m = m < 10 ? '0' + m : m
			format.s = s < 10 ? '0' + s : s
		}
		return format
	},
	//时间格式化(格式化最大为小时)
	formatToHours(t) {
		let format = {
			d: '00',
			h: '00',
			m: '00',
			s: '00',
		}
		if (t > 0) {
			let h = Math.floor(t / 3600)
			let m = Math.floor((t / 60) % 60)
			let s = Math.floor(t % 60)

			format.h = h < 10 ? '0' + h : h
			format.m = m < 10 ? '0' + m : m
			format.s = s < 10 ? '0' + s : s
		}
		return format
	},
	// 年月日
	timestamp(timestamp) {
		let date = new Date(timestamp * 1000); //根据时间戳生成的时间对象
		let y = date.getFullYear();
		let m = date.getMonth() + 1;
		let d = date.getDate();

		m = m < 10 ? '0' + m : m;
		d = d < 10 ? '0' + d : d

		let dateText = y + "-" + m + "-" + d
		return dateText
	},
	// 年月日，时分秒
	// "YYYY-mm-dd HH:MM"
	dateFormat(fmt, date) {
		let ret;
		const opt = {
			"Y+": date.getFullYear().toString(), // 年
			"m+": (date.getMonth() + 1).toString(), // 月
			"d+": date.getDate().toString(), // 日
			"H+": date.getHours().toString(), // 时
			"M+": date.getMinutes().toString(), // 分
			"S+": date.getSeconds().toString() // 秒
			// 有其他格式化字符需求可以继续添加，必须转化成字符串
		};
		for (let k in opt) {
			ret = new RegExp("(" + k + ")").exec(fmt);
			if (ret) {
				fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
			};
		};
		return fmt;
	},
	/**
	 *  @fn  时间间隔格式化
	 *  @param {*} startTime 开始时间的时间戳
	 *  @param {*} endTime 结束时间的时间戳
	 *  @return {string} str 返回时间字符串
	 */
	getTimeInterval(startTime, endTime) {
		let runTime = parseInt((endTime - startTime) / 1000);
		let year = Math.floor(runTime / 86400 / 365);
		runTime = runTime % (86400 * 365);
		let month = Math.floor(runTime / 86400 / 30);
		runTime = runTime % (86400 * 30);
		let day = Math.floor(runTime / 86400);
		runTime = runTime % 86400;
		let hour = Math.floor(runTime / 3600);
		runTime = runTime % 3600;
		let minute = Math.floor(runTime / 60);
		runTime = runTime % 60;
		let second = runTime;
		let str = '';
		if (year > 0) {
			str = year + '年';
		}
		if (year <= 0 && month > 0) {
			str = month + '月';
		}
		if (year <= 0 && month <= 0 && day > 0) {
			str = day + '天';
		}
		if (year <= 0 && month <= 0 && day <= 0 && hour > 0) {
			str = hour + '小时';
		}
		if (year <= 0 && month <= 0 && day <= 0 && hour <= 0 && minute > 0) {
			str = minute + '分钟';
		}
		if (year <= 0 && month <= 0 && day <= 0 && hour <= 0 && minute <= 0 && second > 0) {
			str += second + '秒';
		}
		str += '前';
		return str;
	},


	/**
	 *@alias 节流
	 *@param {function} fn 节流被执行函数 
	 *@param {Number}  delay 时间单位内
	 */
	throttle(fn, delay) {
		let flag = true,
			timer = null;
		return function(...args) {
			let context = this
			if (!flag) return
			flag = false
			clearTimeout(timer)
			timer = setTimeout(() => {
				fn.apply(context, args)
				flag = true
			}, delay)
		}
	},

	/**
	 *@alias 防抖
	 *@param {function} fn 防抖被执行函数 
	 *@param {Number}  delay 时间单位内
	 */
	debounce(fn, delay) {
		let timer = null
		return function(...args) {
			let context = this
			if (timer) clearTimeout(timer)
			timer = setTimeout(function() {
				fn.apply(context, args)
			}, delay)
		}
	},

	showImg(url, size = 300, quality = 70) {
		if (!url) return
		if (url.indexOf('oss.') > 0 || url.indexOf('/file/preview?file=') > 0) {
			let str = url
			if (size) str += "&width=" + size;
			if (quality) str += "&quality=" + quality
			return str
		} else {
			return url
		}
	},


	//得到字符串长度，英文1，中文2
	strLength(str) {
		var realLength = 0,
			len = str.length,
			charCode = -1;
		for (var i = 0; i < len; i++) {
			charCode = str.charCodeAt(i);
			if (charCode >= 0 && charCode <= 128) realLength += 1;
			else realLength += 2;
		}
		return realLength;
	},

	//截取字符串长度，汉字占2个
	truncateString(str, maxLength) {
		let total = 0;
		let result = [];
		
		for (const char of str) {
			const cost = /[\u4e00-\u9fa5]/.test(char) ? 2 : 1;
			if (total + cost > maxLength) break;
			total += cost;
			result.push(char);
		}
		
		return { 
			text: result.join(''),
			usedLength: total
		};
	},
	

	cloneObj (obj = {}) {
		let newobj = null;

		//判断是否需要继续进行递归
		if (typeof (obj) == 'object' && obj !== null) {
			newobj = obj instanceof Array ? [] : {};                //进行下一层递归克隆
			for (var i in obj) {
			newobj[i] = this.cloneObj(obj[i])
		}                //如果不是对象直接赋值
		} else newobj = obj;
		return newobj;
	},
	
}
