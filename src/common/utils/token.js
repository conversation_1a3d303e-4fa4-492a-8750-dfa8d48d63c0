import { storage, tokenTableName } from '@/config'
import cookie from 'js-cookie'
import router from '../router'

/**
 * @description 获取token
 * @returns {string|ActiveX.IXMLDOMNode|Promise<any>|any|IDBRequest<any>|MediaKeyStatus|FormDataEntryValue|Function|Promise<Credential | null>}
 */
export function getToken() {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.getItem(tokenTableName)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.getItem(tokenTableName)
		} else if ('cookie' === storage) {
			return cookie.get(tokenTableName)
		} else {
			return localStorage.getItem(tokenTableName)
		}
	} else {
		return localStorage.getItem(tokenTableName)
	}
}

/**
 * @description 存储token
 * @param token
 * @returns {void|*}
 */
export function setToken(token) {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.setItem(tokenTableName, token)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.setItem(tokenTableName, token)
		} else if ('cookie' === storage) {
			return cookie.set(tokenTableName, token)
		} else {
			return localStorage.setItem(tokenTableName, token)
		}
	} else {
		return localStorage.setItem(tokenTableName, token)
	}
}

/**
 * @description 移除token
 * @returns {void|Promise<void>}
 */
export function removeToken() {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.removeItem(tokenTableName)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.clear()
		} else if ('cookie' === storage) {
			return cookie.remove(tokenTableName)
		} else {
			return localStorage.removeItem(tokenTableName)
		}
	} else {
		return localStorage.removeItem(tokenTableName)
	}
}

export function checkLogin(path) {
	var token = getToken()
	if (!token) router.push({ path: path, query: { redirect: router.currentRoute.path } })
}

/**
 * @description 获取刷新token
 * @returns {string|ActiveX.IXMLDOMNode|Promise<any>|any|IDBRequest<any>|MediaKeyStatus|FormDataEntryValue|Function|Promise<Credential | null>}
 */
export function getRefresh() {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.getItem(tokenTableName+'_refresh')
		} else if ('sessionStorage' === storage) {
			return sessionStorage.getItem(tokenTableName+'_refresh')
		} else if ('cookie' === storage) {
			return cookie.get(tokenTableName+'_refresh')
		} else {
			return localStorage.getItem(tokenTableName+'_refresh')
		}
	} else {
		return localStorage.getItem(tokenTableName+'_refresh')
	}
}

/**
 * @description 存储刷新token
 * @param refresh
 * @returns {void|*}
 */
export function setRefresh(refresh) {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.setItem(tokenTableName+'_refresh', refresh)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.setItem(tokenTableName+'_refresh', refresh)
		} else if ('cookie' === storage) {
			return cookie.set(tokenTableName+'_refresh', refresh)
		} else {
			return localStorage.setItem(tokenTableName+'_refresh', refresh)
		}
	} else {
		return localStorage.setItem(tokenTableName+'_refresh', refresh)
	}
}

/**
 * @description 移除刷新token
 * @returns {void|Promise<void>}
 */
export function removeRefresh() {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.removeItem(tokenTableName+'_refresh')
		} else if ('sessionStorage' === storage) {
			return sessionStorage.clear()
		} else if ('cookie' === storage) {
			return cookie.remove(tokenTableName+'_refresh')
		} else {
			return localStorage.removeItem(tokenTableName+'_refresh')
		}
	} else {
		return localStorage.removeItem(tokenTableName+'_refresh')
	}
}