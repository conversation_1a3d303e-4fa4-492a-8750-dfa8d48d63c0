import { storage } from '@/config'
import cookie from 'js-cookie'

const UserId = 'ut_UserId'
const UserName = 'ut_UserName'
const UserPhone = 'ut_UserPhone'
const UserHeadimgurl = 'ut_Headimgurl'
const UserNickname = 'ut_Nickname'

export function getUserId() {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.getItem(UserId)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.getItem(UserId)
		} else if ('cookie' === storage) {
			return cookie.get(UserId)
		} else {
			return localStorage.getItem(UserId)
		}
	} else {
		return localStorage.getItem(UserId)
	}
}

export function setUserId(userId) {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.setItem(UserId, userId)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.setItem(UserId, userId)
		} else if ('cookie' === storage) {
			return cookie.set(UserId, userId)
		} else {
			return localStorage.setItem(UserId, userId)
		}
	} else {
		return localStorage.setItem(UserId, userId)
	}
}

export function removeUserId() {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.removeItem(UserId)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.clear()
		} else if ('cookie' === storage) {
			return cookie.remove(UserId)
		} else {
			return localStorage.removeItem(UserId)
		}
	} else {
		return localStorage.removeItem(UserId)
	}
}

export function getUserName() {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.getItem(UserName)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.getItem(UserName)
		} else if ('cookie' === storage) {
			return cookie.get(UserName)
		} else {
			return localStorage.getItem(UserName)
		}
	} else {
		return localStorage.getItem(UserName)
	}
}

export function setUserName(userName) {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.setItem(UserName, userName)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.setItem(UserName, userName)
		} else if ('cookie' === storage) {
			return cookie.set(UserName, userName)
		} else {
			return localStorage.setItem(UserName, userName)
		}
	} else {
		return localStorage.setItem(UserName, userName)
	}
}

export function removeUserName() {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.removeItem(UserName)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.clear()
		} else if ('cookie' === storage) {
			return cookie.remove(UserName)
		} else {
			return localStorage.removeItem(UserName)
		}
	} else {
		return localStorage.removeItem(UserName)
	}
}

export function getUserPhone() {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.getItem(UserPhone)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.getItem(UserPhone)
		} else if ('cookie' === storage) {
			return cookie.get(UserPhone)
		} else {
			return localStorage.getItem(UserPhone)
		}
	} else {
		return localStorage.getItem(UserPhone)
	}
}

export function setUserPhone(userPhone) {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.setItem(UserPhone, userPhone)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.setItem(UserPhone, userPhone)
		} else if ('cookie' === storage) {
			return cookie.set(UserPhone, userPhone)
		} else {
			return localStorage.setItem(UserPhone, userPhone)
		}
	} else {
		return localStorage.setItem(UserPhone, userPhone)
	}
}

export function removeUserPhone() {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.removeItem(UserPhone)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.clear()
		} else if ('cookie' === storage) {
			return cookie.remove(UserPhone)
		} else {
			return localStorage.removeItem(UserPhone)
		}
	} else {
		return localStorage.removeItem(UserPhone)
	}
}

export function getUserHeadimgurl() {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.getItem(UserHeadimgurl)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.getItem(UserHeadimgurl)
		} else if ('cookie' === storage) {
			return cookie.get(UserHeadimgurl)
		} else {
			return localStorage.getItem(UserHeadimgurl)
		}
	} else {
		return localStorage.getItem(UserHeadimgurl)
	}
}

export function setUserHeadimgurl(userHeadimgurl) {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.setItem(UserHeadimgurl, userHeadimgurl)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.setItem(UserHeadimgurl, userHeadimgurl)
		} else if ('cookie' === storage) {
			return cookie.set(UserHeadimgurl, userHeadimgurl)
		} else {
			return localStorage.setItem(UserHeadimgurl, userHeadimgurl)
		}
	} else {
		return localStorage.setItem(UserHeadimgurl, userHeadimgurl)
	}
}

export function removeUserHeadimgurl() {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.removeItem(UserHeadimgurl)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.clear()
		} else if ('cookie' === storage) {
			return cookie.remove(UserHeadimgurl)
		} else {
			return localStorage.removeItem(UserHeadimgurl)
		}
	} else {
		return localStorage.removeItem(UserHeadimgurl)
	}
}

export function getUserNickname() {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.getItem(UserNickname)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.getItem(UserNickname)
		} else if ('cookie' === storage) {
			return cookie.get(UserNickname)
		} else {
			return localStorage.getItem(UserNickname)
		}
	} else {
		return localStorage.getItem(UserNickname)
	}
}

export function setUserNickname(userNickname) {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.setItem(UserNickname, userNickname)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.setItem(UserNickname, userNickname)
		} else if ('cookie' === storage) {
			return cookie.set(UserNickname, userNickname)
		} else {
			return localStorage.setItem(UserNickname, userNickname)
		}
	} else {
		return localStorage.setItem(UserNickname, userNickname)
	}
}

export function removeUserNickname() {
	if (storage) {
		if ('localStorage' === storage) {
			return localStorage.removeItem(UserNickname)
		} else if ('sessionStorage' === storage) {
			return sessionStorage.clear()
		} else if ('cookie' === storage) {
			return cookie.remove(UserNickname)
		} else {
			return localStorage.removeItem(UserNickname)
		}
	} else {
		return localStorage.removeItem(UserNickname)
	}
}
