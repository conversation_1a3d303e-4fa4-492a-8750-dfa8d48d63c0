/**
 * @description 路由拦截状态管理，目前两种模式：all模式与intelligence模式，其中partialRoutes是菜单暂未使用
 */
import Vue from 'vue'
import store from '@/common/store'
import { resetRouter } from '@/common/router'
import { convertRouter, filterRoutes } from '@/common/utils/routes'
// import { commKey } from '@/config'
import { isArray } from '@/common/utils/validate'
import path from '@/path'
import constantRoutes from '@/path/constantRoutes'

const state = () => ({
	routes: [],
	activeName: '',
})
const getters = {
	routes: (state) => state.routes,
	activeName: (state) => state.activeName,
}
const mutations = {
	/**
	 * @description 多模式设置路由
	 * @param {*} state
	 * @param {*} routes
	 */
	setRoutes(state, routes) {
		state.routes = routes
	},
	/**
	 * @description 修改Meta
	 * @param {*} state
	 * @param options
	 */
	changeMenuMeta(state, options) {
		function handleRoutes(routes) {
			return routes.map((route) => {
				if (route.name === options.name) Object.assign(route.meta, options.meta)
				if (route.children && route.children.length) route.children = handleRoutes(route.children)
				return route
			})
		}
		state.routes = handleRoutes(state.routes)
	},
	/**
	 * @description 修改 activeName
	 * @param {*} state
	 * @param activeName 当前激活菜单
	 */
	changeActiveName(state, activeName) {
		state.activeName = activeName
	},
}
const actions = {
	/**
	 * @description 多模式设置路由
	 * @param {*} { commit }
	 * @param mode
	 * @returns
	 */
	async setProjectRoutes({ commit },paths=[]) {
		// 开发用前端路由
		let devRoutes=[]
		Object.values(path).forEach((item) => {
			item.forEach((item2) => {
				if (process.env.NODE_ENV === 'development') {
					devRoutes.push(item2)
				}else
				{
					if(!item2.meta) devRoutes.push(item2)
				}
			})
		})
		devRoutes.sort((a, b) => {
			return a.index - b.index
		})


		// 设置后端路由
		let routes = []
		if(store.state.comm && store.state.comm.commKey && store.state.comm.comm && store.state.comm.comm.id){
			const {	data } = await Vue.prototype.$ut.api('comm/user/router',{commKey:store.state.comm.commKey,baseId:store.state.comm.comm.id})
			if (isArray(data)){
				if (data[data.length - 1].path !== '*') data.push({ path: '*', redirect: '/404', meta: { hidden: true } })
				routes = convertRouter(data)
			}
		}
		// 根据权限和rolesControl过滤路由
		const accessRoutes = filterRoutes([...constantRoutes, ...routes, ...devRoutes,...paths], true)
		commit('setRoutes', JSON.parse(JSON.stringify(accessRoutes)))
		// 根据可访问路由重置Vue Router
		await resetRouter(accessRoutes)
	},
	//只设计白名单中的路由，即基础路由
	async setBaseRoutes({ commit }) {
		const accessRoutes = filterRoutes([...constantRoutes], false)
		commit('setRoutes', JSON.parse(JSON.stringify(accessRoutes)))
		// 根据可访问路由重置Vue Router
		await resetRouter(accessRoutes)
	},

	//在白名单路由上增加模块切换路由
	async setModuleRoutes({ commit },route) {
		let routes = [route]
		const accessRoutes = filterRoutes([...constantRoutes, ...routes], false)
		commit('setRoutes', JSON.parse(JSON.stringify(accessRoutes)))
		// 根据可访问路由重置Vue Router
		await resetRouter(accessRoutes)
	},
	/**
	 * @description 修改Route Meta
	 * @param {*} { commit }
	 * @param options
	 */
	changeMenuMeta({ commit }, options = {}) {
		commit('changeMenuMeta', options)
	},
	/**
	 * @description 修改 activeName
	 * @param {*} { commit }
	 * @param activeName 当前激活菜单
	 */
	changeActiveName({ commit }, activeName) {
		commit('changeActiveName', activeName)
	},
}
export default { state, getters, mutations, actions }
