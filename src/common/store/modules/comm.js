import { storage } from '@/config'

const store_commkey = 'ut_commkey'
const store_unit = 'ut_unit'
const store_school = 'ut_school'
const store_project = 'ut_project'
const store_shop = 'ut_shop'
const store_comm = 'ut_comm'

function store_getCommKey() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_commkey)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_commkey)
	} else {
		return localStorage.getItem(store_commkey)
	}
}

function store_setCommKey(commKey) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_commkey, commKey)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_commkey, commKey)
	} else {
		return localStorage.setItem(store_commkey, commKey)
	}
}

function store_removeCommKey() {
	if ('localStorage' === storage) {
		return localStorage.removeItem(store_commkey)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.removeItem(store_commkey)
	} else {
		return localStorage.removeItem(store_commkey)
	}
}


function store_getUnit_id() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_unit + '_id')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_unit + '_id')
	} else {
		return localStorage.getItem(store_unit + '_id')
	}
}

function store_getUnit_name() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_unit + '_name')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_unit + '_name')
	} else {
		return localStorage.getItem(store_unit + '_name')
	}
}

function store_getSchool_id() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_school + '_id')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_school + '_id')
	} else {
		return localStorage.getItem(store_school + '_id')
	}
}
function store_getSchool_name() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_school + '_name')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_school + '_name')
	} else {
		return localStorage.getItem(store_school + '_name')
	}
}

function store_getProject_id() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_project + '_id')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_project + '_id')
	} else {
		return localStorage.getItem(store_project + '_id')
	}
}

function store_getProject_name() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_project + '_name')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_project + '_name')
	} else {
		return localStorage.getItem(store_project + '_name')
	}
}

function store_getShop_id() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_shop + '_id')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_shop + '_id')
	} else {
		return localStorage.getItem(store_shop + '_id')
	}
}

function store_getShop_name() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_shop + '_name')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_shop + '_name')
	} else {
		return localStorage.getItem(store_shop + '_name')
	}
}

function store_getComm_id() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_comm + '_id')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_comm + '_id')
	} else {
		return localStorage.getItem(store_comm + '_id')
	}
}

function store_getComm_name() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_comm + '_name')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_comm + '_name')
	} else {
		return localStorage.getItem(store_comm + '_name')
	}
}

function store_setUnit_id(unit_id) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_unit + '_id', unit_id)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_unit + '_id', unit_id)
	} else {
		return localStorage.setItem(store_unit + '_id', unit_id)
	}
}

function store_setUnit_name(unit_name) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_unit + '_name', unit_name)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_unit + '_name', unit_name)
	} else {
		return localStorage.setItem(store_unit + '_name', unit_name)
	}
}

function store_setSchool_id(school_id) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_school + '_id', school_id)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_school + '_id', school_id)
	} else {
		return localStorage.setItem(store_school + '_id', school_id)
	}
}

function store_setSchool_name(school_name) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_school + '_name', school_name)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_school + '_name', school_name)
	} else {
		return localStorage.setItem(store_school + '_name', school_name)
	}
}

function store_setProject_id(project_id) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_project + '_id', project_id)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_project + '_id', project_id)
	} else {
		return localStorage.setItem(store_project + '_id', project_id)
	}
}

function store_setProject_name(project_name) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_project + '_name', project_name)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_project + '_name', project_name)
	} else {
		return localStorage.setItem(store_project + '_name', project_name)
	}
}

function store_setShop_id(shop_id) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_shop + '_id', shop_id)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_shop + '_id', shop_id)
	} else {
		return localStorage.setItem(store_shop + '_id', shop_id)
	}
}

function store_setShop_name(shop_name) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_shop + '_name', shop_name)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_shop + '_name', shop_name)
	} else {
		return localStorage.setItem(store_shop + '_name', shop_name)
	}
}

function store_setComm_id(comm_id) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_comm + '_id', comm_id)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_comm + '_id', comm_id)
	} else {
		return localStorage.setItem(store_comm + '_id', comm_id)
	}
}

function store_setComm_name(comm_name) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_comm + '_name', comm_name)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_comm + '_name', comm_name)
	} else {
		return localStorage.setItem(store_comm + '_name', comm_name)
	}
}


function store_removeUnit_id() {
	if ('localStorage' === storage) {
		return localStorage.removeItem(store_unit + '_id')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.removeItem(store_unit + '_id')
	} else {
		return localStorage.removeItem(store_unit + '_id')
	}
}

function store_removeUnit_name() {
	if ('localStorage' === storage) {
		return localStorage.removeItem(store_unit + '_name')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.removeItem(store_unit + '_name')
	} else {
		return localStorage.removeItem(store_unit + '_name')
	}
}

function store_removeSchool_id() {
	if ('localStorage' === storage) {
		return localStorage.removeItem(store_school + '_id')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.removeItem(store_school + '_id')
	} else {
		return localStorage.removeItem(store_school + '_id')
	}
}

function store_removeSchool_name() {
	if ('localStorage' === storage) {
		return localStorage.removeItem(store_school + '_name')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.removeItem(store_school + '_name')
	} else {
		return localStorage.removeItem(store_school + '_name')
	}
}

function store_removeProject_id() {
	if ('localStorage' === storage) {
		return localStorage.removeItem(store_project + '_id')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.removeItem(store_project + '_id')
	} else {
		return localStorage.removeItem(store_project + '_id')
	}
}

function store_removeProject_name() {
	if ('localStorage' === storage) {
		return localStorage.removeItem(store_project + '_name')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.removeItem(store_project + '_name')
	} else {
		return localStorage.removeItem(store_project + '_name')
	}
}

function store_removeShop_id() {
	if ('localStorage' === storage) {
		return localStorage.removeItem(store_shop + '_id')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.removeItem(store_shop + '_id')
	} else {
		return localStorage.removeItem(store_shop + '_id')
	}
}

function store_removeShop_name() {
	if ('localStorage' === storage) {
		return localStorage.removeItem(store_shop + '_name')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.removeItem(store_shop + '_name')
	} else {
		return localStorage.removeItem(store_shop + '_name')
	}
}

function store_removeComm_id() {
	if ('localStorage' === storage) {
		return localStorage.removeItem(store_comm + '_id')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.removeItem(store_comm + '_id')
	} else {
		return localStorage.removeItem(store_comm + '_id')
	}
}

function store_removeComm_name() {
	if ('localStorage' === storage) {
		return localStorage.removeItem(store_comm + '_name')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.removeItem(store_comm + '_name')
	} else {
		return localStorage.removeItem(store_comm + '_name')
	}
}

/////////////////////

const state = () => ({
	commKey:store_getCommKey(), //公共key,项目使用
	comm: { id: store_getComm_id(), name: store_getComm_name() },  //最小使用单位
	unit: { id: store_getUnit_id(), name: store_getUnit_name() },
	school: { id: store_getSchool_id(), name: store_getSchool_name() },
	project: { id: store_getProject_id(), name: store_getProject_name() },
	shop: { id: store_getShop_id(), name: store_getShop_name() },
})
const getters = {
	commKey: (state) => state.commKey,
	unit: (state) => state.unit,
	school: (state) => state.school,
	project: (state) => state.project,
	shop: (state) => state.shop,
	comm: (state) => state.comm,
}
const mutations = {
	setCommKey(state, commKey){
		state.commKey = commKey
		store_setCommKey(commKey)
	},
	setUnit(state, unit) {
		state.unit = unit
		store_setUnit_id(unit.id)
		store_setUnit_name(unit.name)
	},
	setSchool(state, school) {
		state.school = school
		store_setSchool_id(school.id)
		store_setSchool_name(school.name)
	},
	setProject(state, project) {
		state.project = project
		store_setProject_id(project.id)
		store_setProject_name(project.name)
	},
	setShop(state, shop) {
		state.shop = shop
		store_setShop_id(shop.id)
		store_setShop_name(shop.name)
	},
	setComm(state, comm) {
		state.comm = comm
		store_setComm_id(comm.id)
		store_setComm_name(comm.name)
	},
	clearAll(state){
		state.base=null
		state.unit=null
		state.school=null
		state.project=null
		state.shop=null
		state.comm=null
		store_removeCommKey()
		store_removeUnit_id()
		store_removeUnit_name()
		store_removeSchool_id()
		store_removeSchool_name()		
		store_removeProject_id()
		store_removeProject_name()
		store_removeShop_id()
		store_removeShop_name()
		store_removeComm_id()
		store_removeComm_name()
	}
}
const actions = {
	setCommKey({ commit }, commKey){
		commit('setCommKey', commKey)
	},
	setUnit({ commit }, unit) {
		commit('setUnit', unit)
	},
	setSchool({ commit }, school) {
        console.log(school)
		commit('setSchool', school)
	},
	setProject({ commit }, project) {
		commit('setProject', project)
	},
	setShop({ commit }, shop) {
		commit('setShop', shop)
	},
	setComm({ commit }, comm){
		commit('setComm', comm)
	},
	clearAll({ commit }){
		commit('clearAll')
	},
}
export default { state, getters, mutations, actions }
