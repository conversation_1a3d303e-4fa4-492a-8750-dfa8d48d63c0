/**
 * @description 登录、获取用户信息、退出登录、清除token逻辑，不建议修改
 */
import Vue from 'vue'
// import { socialLogin } from '@/api/user'
import { getToken, removeToken, setToken,getRefresh,setRefresh,removeRefresh } from '@/common/utils/token'
import {
	getUserId,
	removeUserId,
	setUserId,
	getUserName,
	removeUserName,
	setUserName,
	getUserPhone,
	removeUserPhone,
	setUserPhone,
	getUserHeadimgurl,
	removeUserHeadimgurl,
	setUserHeadimgurl,
	getUserNickname,
	removeUserNickname,
	setUserNickname,
} from '@/common/utils/user'
import { resetRouter } from '@/common/router'
import { isString } from '@/common/utils/validate'
import { encryptedData } from '@/common/utils/encrypt'
import { title, tokenName, loginRSA } from '@/config'

const state = () => ({
	token: getToken(),
	refresh:getRefresh(),
	id: getUserId(),
	name: getUser<PERSON><PERSON>(),
	phone: getUserP<PERSON>(),
	headimgurl: getUserHeadimgurl(),
	nickname: getUser<PERSON>ickname(),
})
const getters = {
	token: (state) => state.token,
	refresh: (state) => state.refresh,
	id: (state) => state.id,
	name: (state) => state.name,
	phone: (state) => state.phone,
	headimgurl: (state) => state.headimgurl,
	nickname: (state) => state.nickname,
}
const mutations = {
	setToken(state, token) {
		state.token = token
		setToken(token)
	},

	setRefresh(state, refresh){
		state.refresh=refresh
		setRefresh(refresh)
	},

	setId(state, id) {
		state.id = id
		setUserId(id)
	},

	setName(state, name) {
		state.name = name
		setUserName(name)
	},

	setPhone(state, phone) {
		state.phone = phone
		setUserPhone(phone)
	},

	setHeadimgurl(state, headimgurl) {
		state.headimgurl = headimgurl
		setUserHeadimgurl(headimgurl)
	},

	setNickname(state, nickname) {
		state.nickname = nickname
		setUserNickname(nickname)
	},

}
const actions = {
	/**
	 * @description 登录拦截放行时，设置虚拟角色
	 * @param {*} { commit, dispatch }
	 */
	setVirtualRoles({ commit, dispatch }) {
		dispatch('acl/setAdmin', true, { root: true })
		commit('setAvatar', 'https://i.gtimg.cn/club/item/face/img/2/15922_100.gif')
		commit('setUsername', 'admin(未开启登录拦截)')
	},
	/**
	 * @description 登录
	 * @param {*} { commit }
	 * @param {*} userInfo
	 */
	async login({ commit }, userInfo) {
		let params = userInfo

		if (loginRSA) {
			params = await encryptedData(params)
		}
		const {
			data: { access_token: access_token ,refresh_token:refresh_token},
		} = await Vue.prototype.$ut.api('comm/login/account', params)
		if (access_token) {
			commit('setToken', access_token)
			commit('setRefresh', refresh_token)
			// const hour = new Date().getHours()
			// const thisTime = hour < 8 ? '早上好' : hour <= 11 ? '上午好' : hour <= 13 ? '中午好' : hour < 18 ? '下午好' : '晚上好'
			// Vue.prototype.$baseNotify(`欢迎登录${title}`, `${thisTime}！`)
		} else {
			const err = `登录接口异常，未正确返回${tokenName}...`
			Vue.prototype.$baseMessage(err, 'error', 'ut-hey-message-error')
			throw err
		}
	},

	async login_website({ commit }, data) {
		const {
			data: { access_token: access_token,refresh_token:refresh_token },
		} = await Vue.prototype.$ut.api('comm/login/website', data)
		if (access_token) {
			commit('setToken', access_token)
			commit('setRefresh', refresh_token)
			const hour = new Date().getHours()
			const thisTime = hour < 8 ? '早上好' : hour <= 11 ? '上午好' : hour <= 13 ? '中午好' : hour < 18 ? '下午好' : '晚上好'
			Vue.prototype.$baseNotify(`欢迎登录${title}`, `${thisTime}！`)
		} else {
			const err = `登录接口异常，未正确返回${tokenName}...`
			Vue.prototype.$baseMessage(err, 'error', 'ut-hey-message-error')
			throw err
		}
	},

	async refresh({ commit,state }) {
		const {
			data: { access_token: access_token,refresh_token:refresh_token },
		} = await Vue.prototype.$ut.api('comm/login/refresh',{refresh_token:state.refresh})
		if (access_token) {
			commit('setToken', access_token)
			commit('setRefresh', refresh_token)
		} else {
			const err = `登录接口异常，未正确返回${tokenName}...`
			Vue.prototype.$baseMessage(err, 'error', 'ut-hey-message-error')
			throw err
		}
	},
	/**
	 * @description 第三方登录
	 * @param {*} {}
	 * @param {*} tokenData
	 */
	// async socialLogin({ commit }, tokenData) {
	// 	const {
	// 		data: { [tokenName]: token },
	// 	} = await socialLogin(tokenData)
	// 	if (token) {
	// 		commit('setToken', token)
	// 		const hour = new Date().getHours()
	// 		const thisTime =
	// 			hour < 8 ? '早上好' : hour <= 11 ? '上午好' : hour <= 13 ? '中午好' : hour < 18 ? '下午好' : '晚上好'
	// 		Vue.prototype.$baseNotify(`欢迎登录${title}`, `${thisTime}！`)
	// 	} else {
	// 		const err = `login核心接口异常，请检查返回JSON格式是否正确，是否正确返回${tokenName}...`
	// 		Vue.prototype.$baseMessage(err, 'error', 'ut-hey-message-error')
	// 		throw err
	// 	}
	// },
	/**
	 * @description 获取用户信息接口 这个接口非常非常重要，如果没有明确底层前逻辑禁止修改此方法，错误的修改可能造成整个框架无法正常使用
	 * @param {*} { commit, dispatch, state }
	 * @returns
	 */
	async getMyInfo({ commit }) {
		const { data } = await Vue.prototype.$ut.api('comm/login/myinfo')
		if (
			(data.id && !isString(data.id)) ||
			(data.name && !isString(data.name)) ||
			(data.phone && !isString(data.phone)) ||
			(data.headimgurl && !isString(data.headimgurl)) ||
			(data.nickname && !isString(data.nickname))
		) {
			const err = 'myinfo核心接口异常，请检查'
			Vue.prototype.$baseMessage(err, 'error', 'ut-hey-message-error')
			throw err
		} else {
			// 如不使用username用户名,可删除以下代码
			if (data.id) commit('setId', data.id)
			if (data.name) commit('setName', data.name)
			if (data.phone) commit('setPhone', data.phone)
			if (data.headimgurl) commit('setHeadimgurl', data.headimgurl)
			if (data.nickname) commit('setNickname', data.nickname)
			// 如不使用roles权限控制,可删除以下代码
			// if (data.roles) dispatch('acl/setRole', data.roles, { root: true })
			// 如不使用permissions权限控制,可删除以下代码
			// if (permissions) dispatch('acl/setPermission', permissions, { root: true })
		}
	},
	/**
	 * @description 退出登录
	 * @param {*} { dispatch }
	 */
	async logout({ dispatch }) {
		await dispatch('resetAll')
		// await Vue.prototype.$ut.api('user/logout')
	},
	/**
	 * @description 重置token、roles、permission、router、tabsBar等
	 * @param {*} { commit, dispatch }
	 */
	async resetAll({ commit, dispatch }) {
		commit('setId', '')
		commit('setName', '')
		commit('setPhone', '')
		commit('setHeadimgurl', 'https://i.gtimg.cn/club/item/face/img/2/15922_100.gif')
		commit('setNickname', '游客')
		commit('routes/setRoutes', [], { root: true })
		await dispatch('setToken', '')
		await dispatch('setRefresh','')
		await dispatch('acl/setAdmin', false, { root: true }) //是否管理员
		await dispatch('acl/setRole', [], { root: true }) //设置角色
		await dispatch('acl/setPermission', [], { root: true }) //设置权限
		await dispatch('tabs/delAllVisitedRoutes', null, { root: true }) //删除全部标签
		await resetRouter()
		removeRefresh()
		removeToken()
		removeUserId()
		removeUserName()
		removeUserPhone()
		removeUserHeadimgurl()
		removeUserNickname()
	},
	/**
	 * @description 设置token
	 * @param {*} { commit }
	 * @param {*} token
	 */
	setToken({ commit }, token) {
		commit('setToken', token)
	},

	setRefresh({commit},refresh){
		commit('setRefresh', refresh)
	},

	/**
	 * @description 设置头像
	 * @param {*} { commit }
	 * @param {*} avatar
	 */
	setHeadimgurl({ commit }, headimgurl) {
		commit('setHeadimgurl', headimgurl)
	},
}
export default { state, getters, mutations, actions }
