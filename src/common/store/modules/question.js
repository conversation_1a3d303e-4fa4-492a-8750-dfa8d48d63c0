import { storage } from '@/config'

const store_question_grade_type = 'ut_question_grade_type'
const store_question_course = 'ut_question_course'
const store_question_edition = 'ut_question_edition'
const store_question_book = 'ut_question_book'

function store_getQuestion_grade_type_id() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_question_grade_type + '_id')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_question_grade_type + '_id')
	} else {
		return localStorage.getItem(store_question_grade_type + '_id')
	}
}

function store_getQuestion_grade_type_name() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_question_grade_type + '_name')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_question_grade_type + '_name')
	} else {
		return localStorage.getItem(store_question_grade_type + '_name')
	}
}

function store_setQuestion_grade_type_id(gradeType_id) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_question_grade_type + '_id', gradeType_id)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_question_grade_type + '_id', gradeType_id)
	} else {
		return localStorage.setItem(store_question_grade_type + '_id', gradeType_id)
	}
}

function store_setQuestion_grade_type_name(gradeType_name) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_question_grade_type + '_name', gradeType_name)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_question_grade_type + '_name', gradeType_name)
	} else {
		return localStorage.setItem(store_question_grade_type + '_name', gradeType_name)
	}
}

function store_getQuestion_course_id() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_question_course + '_id')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_question_course + '_id')
	} else {
		return localStorage.getItem(store_question_course + '_id')
	}
}

function store_getQuestion_course_name() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_question_course + '_name')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_question_course + '_name')
	} else {
		return localStorage.getItem(store_question_course + '_name')
	}
}

function store_setQuestion_course_id(course_id) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_question_course + '_id', course_id)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_question_course + '_id', course_id)
	} else {
		return localStorage.setItem(store_question_course + '_id', course_id)
	}
}

function store_setQuestion_course_name(course_name) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_question_course + '_name', course_name)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_question_course + '_name', course_name)
	} else {
		return localStorage.setItem(store_question_course + '_name', course_name)
	}
}

function store_getQuestion_edition_id() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_question_edition + '_id')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_question_edition + '_id')
	} else {
		return localStorage.getItem(store_question_edition + '_id')
	}
}

function store_getQuestion_edition_name() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_question_edition + '_name')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_question_edition + '_name')
	} else {
		return localStorage.getItem(store_question_edition + '_name')
	}
}

function store_setQuestion_edition_id(edition_id) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_question_edition + '_id', edition_id)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_question_edition + '_id', edition_id)
	} else {
		return localStorage.setItem(store_question_edition + '_id', edition_id)
	}
}

function store_setQuestion_edition_name(edition_name) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_question_edition + '_name', edition_name)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_question_edition + '_name', edition_name)
	} else {
		return localStorage.setItem(store_question_edition + '_name', edition_name)
	}
}

function store_getQuestion_book_id() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_question_book + '_id')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_question_book + '_id')
	} else {
		return localStorage.getItem(store_question_book + '_id')
	}
}

function store_getQuestion_book_name() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_question_book + '_name')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_question_book + '_name')
	} else {
		return localStorage.getItem(store_question_book + '_name')
	}
}

function store_setQuestion_book_id(book_id) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_question_book + '_id', book_id)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_question_book + '_id', book_id)
	} else {
		return localStorage.setItem(store_question_book + '_id', book_id)
	}
}

function store_setQuestion_book_name(book_name) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_question_book + '_name', book_name)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_question_book + '_name', book_name)
	} else {
		return localStorage.setItem(store_question_book + '_name', book_name)
	}
}


/////////////////////

const state = () => ({
	gradeType: { id: store_getQuestion_grade_type_id(), name: store_getQuestion_grade_type_name() },
	course: { id: store_getQuestion_course_id(), name: store_getQuestion_course_name() },
	edition: { id: store_getQuestion_edition_id(), name: store_getQuestion_edition_name() },
	book: { id: store_getQuestion_book_id(), name: store_getQuestion_book_name() },
})
const getters = {
	gradeType: (state) => state.gradeType,
	course: (state) => state.course,
	edition: (state) => state.edition,
	book: (state) => state.book,
}
const mutations = {
	setGradeType(state, gradeType) {
		state.gradeType = gradeType
		store_setQuestion_grade_type_id(gradeType.id)
		store_setQuestion_grade_type_name(gradeType.name)
	},
	setCourse(state, course) {
		state.course = course
		store_setQuestion_course_id(course.id)
		store_setQuestion_course_name(course.name)
	},
	setEdition(state, edition) {
		state.edition = edition
		store_setQuestion_edition_id(edition.id)
		store_setQuestion_edition_name(edition.name)
	},
	setBook(state, book) {
		state.book = book
		store_setQuestion_book_id(book.id)
		store_setQuestion_book_name(book.name)
	},
}
const actions = {
	setGradeType({ commit }, gradeType) {
		commit('setGradeType', gradeType)
	},
	setCourse({ commit }, course) {
		commit('setCourse', course)
	},
	setEdition({ commit }, edition) {
		commit('setEdition', edition)
	},
	setBook({ commit }, book) {
		commit('setBook', book)
	},
}
export default { state, getters, mutations, actions }
