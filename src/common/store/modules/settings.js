/**
 * @description 所有全局配置的状态管理，如无必要请勿修改
 */
import { isJson } from '@/common/utils/validate'
import {
	background,
	collapse as _collapse,
	columnStyle,
	fixedHeader,
	i18n,
	layout,
	logo,
	showFullScreen,
	showLanguage,
	showNotice,
	showProgressBar,
	showRefresh,
	showSearch,
	showTabs,
	showTabsIcon,
	showTheme,
	showThemeSetting,
	tabsBarStyle,
	themeName,
	backgroundColor,
	title,
} from '@/config'

const defaultTheme = {
	background,
	columnStyle,
	fixedHeader,
	layout,
	showProgressBar,
	showTabs,
	tabsBarStyle,
	showTabsIcon,
	showLanguage,
	showRefresh,
	showSearch,
	showTheme,
	showNotice,
	showFullScreen,
	showThemeSetting,
	themeName,
	backgroundColor,
}
const getLocalStorage = (key) => {
	const value = localStorage.getItem(key)
	if (isJson(value)) {
		return JSON.parse(value)
	} else {
		return false
	}
}
const { collapse } = getLocalStorage('collapse')
const { language } = getLocalStorage('language')
const state = () => ({
	logo,
	title,
	device: 'desktop',
	collapse: collapse || _collapse,
	language: language || i18n,
	theme: getLocalStorage('theme') || { ...defaultTheme },
	extra: { first: '' },
})
const getters = {
	logo: (state) => state.logo,
	title: (state) => state.title,
	device: (state) => state.device,
	collapse: (state) => state.collapse,
	language: (state) => state.language,
	theme: (state) => state.theme,
	extra: (state) => state.extra,
}
const mutations = {
	openSideBar(state) {
		state.collapse = false
	},
	foldSideBar(state) {
		state.collapse = true
	},
	toggleDevice(state, device) {
		state.device = device
	},
	toggleCollapse(state) {
		state.collapse = !state.collapse
		localStorage.setItem('collapse', `{"collapse":${state.collapse}}`)
	},
	changeLanguage(state, language) {
		state.language = language
		localStorage.setItem('language', `{"language":"${language}"}`)
	},
	saveTheme(state) {
		switch(state.theme.themeName){
			case 'green-black':
				state.theme.backgroundColor =  '#41b584'
				break
		}
		localStorage.setItem('theme', JSON.stringify(state.theme))
	},
	resetTheme(state) {
		state.theme = { ...defaultTheme }
		localStorage.removeItem('theme')
	},
	updateTheme(state) {
		document.getElementsByTagName('body')[0].className = `ut-theme-${state.theme.themeName}`
		if (state.theme.background !== 'none') {
			document.getElementsByTagName('body')[0].classList.add(state.theme.background)
		}
	},
}
const actions = {
	openSideBar({ commit }) {
		commit('openSideBar')
	},
	foldSideBar({ commit }) {
		commit('foldSideBar')
	},
	toggleDevice({ commit }, device) {
		commit('toggleDevice', device)
	},
	toggleCollapse({ commit }) {
		commit('toggleCollapse')
	},
	changeLanguage: ({ commit }, language) => {
		commit('changeLanguage', language)
	},
	saveTheme({ commit }) {
		commit('saveTheme')
	},
	resetTheme({ commit }) {
		commit('resetTheme')
		commit('updateTheme')
	},
	updateTheme({ commit }) {
		commit('updateTheme')
	},
}
export default { state, getters, mutations, actions }
