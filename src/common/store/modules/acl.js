const state = () => ({
	admin: false,
	role: [],
	permission: [],
})
const getters = {
	admin: (state) => state.admin,
	role: (state) => state.role,
	permission: (state) => state.permission,
}
const mutations = {
	setAdmin(state, admin) {
		state.admin = admin
	},
	setRole(state, role) {
		state.role = role
	},
	setPermission(state, permission) {
		state.permission = permission
	},
}
const actions = {
	setAdmin({ commit }, admin) {
		commit('setAdmin', admin)
	},
	setRole({ commit }, role) {
		commit('setRole', role)
	},
	setPermission({ commit }, permission) {
		commit('setPermission', permission)
	},
}
export default { state, getters, mutations, actions }
