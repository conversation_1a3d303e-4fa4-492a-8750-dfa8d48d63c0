import { storage } from '@/config'


const store_exam = 'ut_exam'


function store_getExam_id() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_exam + '_id')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_exam + '_id')
	} else {
		return localStorage.getItem(store_exam + '_id')
	}
}

function store_getExam_name() {
	if ('localStorage' === storage) {
		return localStorage.getItem(store_exam + '_name')
	} else if ('sessionStorage' === storage) {
		return sessionStorage.getItem(store_exam + '_name')
	} else {
		return localStorage.getItem(store_exam + '_name')
	}
}

function store_setExam_id(exam_id) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_exam + '_id', exam_id)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_exam + '_id', exam_id)
	} else {
		return localStorage.setItem(store_exam + '_id', exam_id)
	}
}

function store_setExam_name(exam_name) {
	if ('localStorage' === storage) {
		return localStorage.setItem(store_exam + '_name', exam_name)
	} else if ('sessionStorage' === storage) {
		return sessionStorage.setItem(store_exam + '_name', exam_name)
	} else {
		return localStorage.setItem(store_exam + '_name', exam_name)
	}
}


/////////////////////

const state = () => ({
	exam: { id: store_getExam_id(), name: store_getExam_name() },
	course:{},
	tip:false,
})
const getters = {
	exam: (state) => state.exam,
	course: (state) => state.course,
	tip: (state) =>state.tip
}
const mutations = {
	setExam(state, exam) {
		state.exam = exam
		store_setExam_id(exam.id)
		store_setExam_name(exam.name)
	},
	setTip(state, tip) {
		state.tip = tip
	},
	setCourse(state,course){
		state.course = course
	}
}
const actions = {
	setExam({ commit }, exam){
		commit('setExam', exam)
	},
	setTip({ commit }, tip){
		commit('setTip', tip)
	},
	setCourse({commit}, course){
		commit('setCourse',course)
	}
}
export default { state, getters, mutations, actions }
