import websocket from '@/common/websocket'
import { API_URL } from '@/config'

const state = {
	socketTask: null,
	eventlist: {},
	unread: [],
}

const mutations = {
	socketInit(state, url) {
		let newUrl = API_URL.replace('http://', 'ws://').replace('https://', 'wss://')
		state.socketTask =websocket.socket()
		state.socketTask.connection(newUrl + url)
		// console.log(url,newUrl)
		// state.socketTask.connection('ws://127.0.0.1:7001/api/ChatHub')
	},
}

const actions = {
	async socketInit({ commit }, url) {
		commit('socketInit', url)
	},
}

export default {
	state,
	mutations,
	actions,
}
// export default new Vuex.Store({
// 	state: {
// 		socketTask: null,
// 		eventlist: {},
// 		unread: []
// 	},
// 	mutations: {
// 		WEBSOCKET_INIT(state, url) {
// 			// 创建一个this.socketTask对象【发送、接收、关闭socket都由这个对象操作】
// 			state.socketTask = new socket.socket();
// 			state.socketTask.connection('ws://' + url)
// 		},
// 		WEBSOCKET_SEND(state, method, params) {
// 			console.log("ws发送！");
// 			state.socketTask.invoke(method, params)
// 		},
// 		WEBSOCKET_ON(state, name, fun) {
// 			console.log("消息绑定");
// 			state.socketTask.on(name, fun)
// 		},
// 	},

// 	actions: {
// 		WEBSOCKET_INIT({
// 			commit
// 		}, url) {
// 			commit('WEBSOCKET_INIT', url)
// 		},
// 		WEBSOCKET_SEND({
// 			commit
// 		}, method, params) {
// 			commit('WEBSOCKET_SEND', method, params)
// 		},
// 		WEBSOCKET_ON({
// 			commit
// 		}, name, fun) {
// 			commit('WEBSOCKET_ON', name, fun)
// 		}
// 	}
// })

// let connection

// function init(url) {
// 	let token = Token.getToken()

// 	connection = new socket.socket();
// 	connection.connection('ws://' + url)

// }

// function on(name, fun) {
// 	connection.on(name, fun)
// }

// function send(method, params) {
// 	connection.invoke(method, params)
// }

// export default {
// 	init,
// 	on,
// 	send,
// }
