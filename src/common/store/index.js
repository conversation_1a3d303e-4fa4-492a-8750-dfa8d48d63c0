/**
 * @description 导入所有 vuex 模块，自动加入namespaced:true，用于解决vuex命名冲突，请勿修改。
 */
import Vue from 'vue'
import Vuex from 'vuex'
import websocket from './websocket.js'

Vue.use(Vuex)

const modules = {}
const files = require.context('./modules', false, /\.js$/)
files.keys().forEach((key) => {
	modules[key.replace(/(modules|\/|\.|js)/g, '')] = {
		...files(key).default,
		namespaced: true,
	}
})
modules.websocket=websocket

//输出版本信息和语雀链接
console.info(`%cUT 框架 %csmallyan %chttps://www.afjy.net`, 'color:#409EFF;font-size: 20px;font-weight:bolder', 'color:#999;font-size: 12px', 'color:#333')

const store = new Vuex.Store({
	modules,
})
export default store
