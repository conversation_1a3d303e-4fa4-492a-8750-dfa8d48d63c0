<template>
	<div class="head-menu">
		<template v-for="(item, index) in dropdownList">
			<div :key="item.id" style="display: flex; align-items: center">
				<div :id="'dropdown-container-' + index" class="dropdown-container">
					<div class="main-menu" @click="itemClick(index, item)">
						{{ item.title }}
						<i v-if="item.children.length" class="el-icon-caret-bottom"></i>
					</div>
					<div class="dropdown-menu" :class="{ 'dropdown-menu-show': item.children.length }">
						<template v-for="children in item.children">
							<div :key="children.id" class="menu-item" @click="itemClick(index, item, children)">{{ children.name }}</div>
						</template>
					</div>
				</div>
				<span v-if="index != dropdownList.length - 1" style="color: #fff">|</span>
			</div>
		</template>
	</div>
</template>

<script>
export default {
	props: {
		dropdownList: {
			type: Array,
			default: () => [],
		},
	},
	methods: {
		itemClick(index, item, children) {
			let dom = document.querySelector(`#dropdown-container-${index}`)
			dom = dom.querySelector('.dropdown-menu')
			if (dom) dom.style.display = 'none'
			setTimeout(() => {
				dom.style.display = ''
			}, 1)
			var data = {}
			if (!item.children || !item.children.length) data = item
			else data = children
			data == undefined ? '' : this.$emit('onItemClick', data)
		},
	},
}
</script>

<style lang="scss">
.head-menu {
	height: 100%;
	display: flex;
}
.dropdown-container {
	height: 100%;
	font-size: 12px;
	display: flex;
	justify-content: center;
	align-items: center;
	margin: 0 20px;
	cursor: pointer;
	position: relative;

	&:hover {
		.main-menu {
			color: rgba(255, 255, 255, 0.8);
		}
		.dropdown-menu-show {
			display: flex;
			z-index: 10001;
		}

		.el-icon-caret-bottom::before {
			content: '\e78f';
			color: #fff;
		}
	}

	.main-menu {
		color: #fff;
	}
	.dropdown-menu {
		display: none;
		border: 1px solid var(--colors);
		background-color: #fff;
		border-radius: 4px;
		position: absolute;
		top: 100%;
		left: 50%;
		transform: translateX(-50%);
		flex-direction: column;
		box-shadow: 0 0 5px #ccc;

		.menu-item {
			width: 120px;
			height: 40px;
			line-height: 40px;
			text-align: center;
			cursor: pointer;

			&:hover {
				color: var(--colors);
				background-color: rgba(0, 0, 0, 0.1);
			}
		}
	}
}
</style>