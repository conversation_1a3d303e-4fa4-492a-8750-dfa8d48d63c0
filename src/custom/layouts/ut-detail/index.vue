<template>
	<div :style="{ '--colors': colors, '--colors2': colors + 'D0', '--colors3': colors + '60', '--colors4': LightDarkenColor(colors, -30) }">
		<div class="top-inner-head">
			<div class="top-head">
				<div class="head-logo">logo</div>
				<div class="head-search">
					<div class="search-type">
						<div class="type">
							{{ searchType ? searchType.title : '' }}
							<i class="el-icon-caret-bottom"></i>
						</div>
						<div class="radio">
							<template v-for="item in searchTypeList">
								<div :key="item.id" class="item flex-center" @click="searchTypeChange(item)">{{ item.title }}</div>
							</template>
						</div>
					</div>
					<div class="search-input">
						<input type="text" class="input" placeholder="请输入搜索关键字" />
					</div>
					<div class="search-btn flex-center">
						<i class="el-icon-search"></i>
					</div>
				</div>
				<dropdown :dropdown-list="dropdownList" style="margin-left: 16px" @onItemClick="menuClick" />
				<div v-if="!userInfo.id" class="no-login">
					<div class="btn" @click="$router.push('/verify/login')">登录</div>
					<div class="btn" @click="$router.push('/verify/register')">注册</div>
				</div>
				<dropdown v-else :dropdown-list="userMenu" @onItemClick="menuClick" />
			</div>
		</div>
		<ut-router-view />
	</div>
</template>

<script>
import { mapGetters, mapState, mapActions } from 'vuex'
import Dropdown from '@/custom/layouts/ut-detail/dropdown.vue'
export default {
	name: 'Detail',
	components: { Dropdown },
	data() {
		return {
			colors: '',
			searchType: {
				id: '0001',
				title: '试题',
			},
			searchTypeList: [
				{
					id: '0001',
					title: '试题',
				},
				{
					id: '0002',
					title: '试卷',
				},
			],
			dropdownList: [
				{
					id: '1001',
					title: '网站首页',
					openType: 'open',
					target: '_self',
					path: '/question/index',
					children: [],
				},
				{
					id: '1002',
					title: '帮助中心',
					children: [],
				},
				{
					id: '1003',
					title: '购买服务',
					children: [
						{
							id: '100301',
							name: 'VIP服务',
						},
						{
							id: '100302',
							name: '团体服务',
						},
					],
				},
				{
					id: '1006',
					title: '旗下产品',
					children: [],
				},
				{
					id: '1007',
					title: '消息(0)',
					children: [],
				},
			],
			userMenu: [
				{
					id: this.userInfo ? this.userInfo.id : '',
					title: this.userInfo ? this.userInfo.nickname : '',
					children: [
						{
							id: '100401',
							name: '个人信息',
							openType: 'open',
							target: '_blank',
							path: '/question/my/index',
						},
						{
							id: '100402',
							name: '下载记录',
							openType: 'open',
							target: '_blank',
							path: '/question/my/download',
						},
						{
							id: '100403',
							name: '组卷记录',
							openType: 'open',
							target: '_blank',
							path: '/question/my/group-record',
						},
						{
							id: '100404',
							name: '我的收藏',
							openType: 'open',
							target: '_blank',
							path: '/question/my/title-collect',
						},
						{
							id: '100405',
							name: '测评中心',
							openType: 'open',
							target: '_blank',
							path: '/question/my/index',
						},
						{
							id: '100406',
							name: '错题本',
							openType: 'open',
							target: '_blank',
							path: '/question/my/index',
						},
						{
							id: '100407',
							name: '退出',
							openType: 'function',
							path: 'loginOut',
						},
					],
				},
			],
		}
	},

	computed: {
		...mapGetters({
			theme: 'settings/theme',
		}),
		...mapState({
			userInfo: (state) => state.user,
		}),
	},
	watch: {
		userInfo: {
			deep: true,
			handler() {
				console.log(123)
			},
		},
	},
	mounted() {
		this.colors = !this.theme.backgroundColor ? '#1890ff' : this.theme.backgroundColor
		if (this.userInfo) {
			if (this.userInfo.id) this.userMenu[0].id = this.userInfo.id
			if (this.userInfo.nickname) this.userMenu[0].title = this.userInfo.nickname
		}
	},
	methods: {
		...mapActions({
			loginOut: 'user/logout',
		}),
		searchTypeChange(item) {
			this.searchType = item
			let dom = document.querySelector('.top-inner-head')
			dom.querySelector('.radio').style.display = 'none'
			setTimeout(() => {
				dom.querySelector('.radio').style.display = ''
			}, 1)
		},
		menuClick(item) {
			if (item.openType === 'open') window.open(item.path, item.target)
			if (item.openType === 'function') {
				if (item.path === 'loginOut') this.loginOut()
			}
		},
		LightDarkenColor(color, num) {
			let colorArr = this.getRgbNum(color)
			let sColorChange = []
			for (var i = 0; i < colorArr.length; i++) {
				let val = colorArr[i] + num
				if (val < 0) {
					val = 0
				}
				if (val > 255) {
					val = 255
				}
				sColorChange.push(val)
			}
			return 'rgba(' + sColorChange.join(',') + ',1)'
		},
		getRgbNum(sColor) {
			if (sColor.length === 4) {
				let sColorNew = '#'
				for (let i = 1; i < 4; i += 1) {
					//补全颜色值 例如：#eee,#fff等
					sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))
				}
				sColor = sColorNew
			}
			//处理六位颜色值
			let sColorChange = []
			for (let i = 1; i < 7; i += 2) {
				//核心代码，通过parseInt将十六进制转为十进制，parseInt只有一个参数时是默认转为十进制的，第二个参数则是指定转为对应进制
				sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2)))
			}
			return sColorChange
		},
	},
}
</script>

<style lang="scss" scoped>
.top-inner-head {
	min-width: 1200px;
	width: 100%;
	height: 60px;
	background-color: var(--colors);

	.top-head {
		width: 1200px;
		height: 100%;
		position: relative;
		margin: 0 auto;
		display: flex;
		align-items: center;
	}
}

.top-head .head-logo {
	width: 180px;
}

.top-head .head-search {
	width: 350px;
	height: 34px;
	margin-left: 60px;
	background-color: #fff;
	border-radius: 4px;
	position: relative;
	display: flex;
	align-items: center;
	z-index: 1000;

	.search-type {
		font-size: 12px;
		padding: 8px 10px;
		cursor: pointer;
		border-left: 1px solid var(--colors);
		background-color: none;

		.type {
			border-right: 1px solid #aaa;
			padding-right: 10px;
			.el-icon-caret-bottom {
				margin-left: 4px;
			}
		}

		&:hover {
			.type {
				border-right: 1px solid var(--colors);
				.el-icon-caret-bottom::before {
					content: '\e78f';
				}
			}

			.radio {
				display: block;
			}
		}

		.radio {
			display: none;
			width: 65px;
			position: absolute;
			top: 24px;
			left: 0;
			padding-top: 10px;
			background-color: #fff;
			border: 1px solid var(--colors);
			border-top: none;
			border-bottom-left-radius: 4px;
			border-bottom-right-radius: 4px;
			user-select: none;

			.item {
				height: 34px;

				&:hover {
					background-color: rgba(0, 0, 0, 0.1);
					color: var(--colors);
				}
			}
		}
	}

	.search-input {
		flex: 1;

		.input {
			border: none;
		}
	}

	.search-btn {
		width: 34px;
		height: 34px;
		font-size: 20px;
		color: var(--colors);

		&:hover {
			cursor: pointer;
			background-color: rgba(0, 0, 0, 0.1);
		}
	}
}

.top-head .no-login {
	display: flex;
	color: #fff;

	.btn {
		cursor: pointer;
		margin: 0 20px;
		padding: 6px;
		border-radius: 6px;
		font-size: 12px;

		&:first-child {
			background-color: rgba(0, 0, 0, 0.1);
			margin-right: 0;
		}
	}
}

.flex-center {
	display: flex;
	justify-content: center;
	align-items: center;
}
</style>