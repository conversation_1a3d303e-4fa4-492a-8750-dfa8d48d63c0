<template>
	<div class="subject-box">
		<div class="tool-bar">
			<el-input :placeholder="'输入关键字'">
				<template slot="append">
					<div class="search-button">
						<i class="el-icon-search"></i>
						搜索
					</div>
				</template>
			</el-input>
		</div>
		<div class="box">
			<div v-for="(item,index) in dataList.info" :key="index" class="exam-box">
				<div class="exam-box-content">
					<div class="main">
						<div class="title">
							<span v-if="item.examType==1" class="type">校内考试</span>
							<span v-if="item.examType==2" class="type">教育局考试</span>
							<span v-if="item.examType==3" class="type">中心校考试</span>
							<span v-if="item.examType==4" class="type">多校联考</span>
							<span class="name">{{item.title}}</span>
						</div>
						<div class="state">
							<span v-if="item.isClose">关闭</span>
							<span v-else>进行中</span>
						</div>
					</div>
					
				</div>
				<div class="exam-box-option">
                    <!-- <router-link class="bullshit-return-home" to="./operate"><el-button type="primary" round plain>进入操作</el-button></router-link> -->
                    <el-button type="primary" round plain @click="goOperate(item)">切换</el-button>
					
				</div>
			</div>
		</div>
		<div>
			<el-pagination
			background
			:current-page="queryForm.pageindex"
			layout="total,  prev, pager, next"
			:page-size="queryForm.pagesize"
			:total="dataList.record"
			@current-change="handleCurrentChange"
			@size-change="handleSizeChange"
		/>
		</div>
	</div>
</template>

<script>
import { mapGetters,mapActions } from 'vuex'
// import AddDialog from '../operate/add-dialog.vue'
export default {
	name:'ExamList',
	components: {
		
	},
	props: {},
	data() {
		return {
			listLoading:false,
            showAddDialog:false,
			dataList:[],
			queryForm: {
				pageindex: 1,
				pagesize: 20,
				key: '',
			},
        }
	},
	computed: {
		...mapGetters({
			school: 'comm/comm',
			exam: 'exam/exam',
		}),
	},
	created() {
		this.fetchData()
	},
	methods: {
		...mapActions({
			setExam: 'exam/setExam',
			setCourse: 'exam/setCourse',
		}),
        handlerCreateExam(){
            this.showAddDialog=true
        },
        goOperate(item){
			this.setExam({id:item.id,name:item.title})
			if(item.courses && item.courses.length){
				const course=item.courses[0]
				this.setCourse({id:course.id,name:course.courseName})
			}else{
				this.setCourse({})
			}
			this.$emit('select',item)

        },
		handleSizeChange(val) {
			this.queryForm.pagesize = val
			this.fetchData()
		},
		handleCurrentChange(val) {
			this.queryForm.pageindex = val
			this.fetchData()
		},
		async fetchData(pageReq) {
			this.listLoading = true
			const {data} = await this.$ut.api('schoolexam/listpg', {
				schoolId: this.school.id,
				page:pageReq,
			}).finally(()=>{this.listLoading = false})
			this.dataList = data
		},
    },
}
</script>

<style lang="scss" scoped>

.subject-box {
	padding: 10px 10px 14px;
}


.tool-bar {
	padding-top: 2px;
	padding-bottom: 10px;
	display: flex;
	justify-content: space-between;

	.el-input {
		width: 300px;
	}
}

// .box{
//     overflow: scroll;
//     height: calc(100% - 80px);
// }

.exam-box:hover {
	background: #e8ece6;
	transition: all 0.1s;
}
.exam-box {
	transition: all 0.1s;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px dashed #67c23a;
	border-top: 1px dashed #67c23a;
	padding: 6px 4px;
	background: #f3f7f1;
	border-radius: 4px;
	margin-bottom: 4px;
	font-size: 12px;

	:deep(.el-button--small){
		padding: 4px 15px;
	}

	.exam-box-content {
		flex: 1;

		.main {
			display: flex;
			justify-content: space-between;

			.title {

				.type {
					background-color: #fdf6ec;
					border-color: #faecd8;
					color: #e6a23c;
					border: 1px solid #faecd8;
					font-size: 12px;
					padding: 2px 8px;
					border-radius: 2px;
					margin-right: 4px;
				}

				.name {
					font-size: 12px;
					// line-height: 2;
				}
			}

			.state {
				color: #67c23a;
			}
		}

		
	}
	.exam-box-option {
		width: 90px;
		text-align: center;
	}
}

.exam-box:last-child {
	margin-bottom: 0;
}

:deep(.el-pager li){
	line-height: 22px;
	height: 22px;
	min-width: 24px;
}

:deep(.el-pagination button){
	line-height: 22px;
	height: 22px;
}

:deep(.el-pagination__total){
	font-size: 12px;
}
</style>