<template>
	 <el-dialog v-dialogDrag :close-on-click-modal="true" title="提示" :visible.sync="dialogFormVisible" width="400px" append-to-body @close="close">
        你还没有选择考试不能进行接下来的操作
    </el-dialog>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'


export default {
	name: 'ExamTip',
	components: {
	},
	props: {
        // showDialog:{
        //     type:Boolean,
        //     default:false,
        // }
    },
	data() {
		return {
			dialogFormVisible:this.examTip,
		}
	},  
	computed: {
		...mapGetters({
			examTip: 'exam/tip',
		}),
	},
	watch:{

        examTip(v){
            this.dialogFormVisible = v
        },

    },
	created() {

	},
	
	methods: {
		...mapActions({
			setExamTip: 'exam/setTip',
		}),
		fetchData() {
		},
		close() {

			this.setExamTip(false)
		},
		save(){

		},
	},
}
</script>

<style lang="scss" scoped>
:deep(.el-dialog__body){
	background-color: #fff !important;
    display: grid;
}

</style>