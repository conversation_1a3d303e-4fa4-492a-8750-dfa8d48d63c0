<template>
	<div class="wrapper-full">
		<div class="wrapper">
			<div class="web-title">
                <img src="@/assets/ut100.png" />
			</div>
			<div class="web-title">
				<span class="title-text">(优题)智校云阅卷 v2.0</span>
            </div>
			<div class="top-item">
				<div class="wechat-nav">
					<div>
						<span class="wechat-title">微信相关</span>
						<i class="el-icon-caret-bottom icon-bottom" style="color: #7c7c7c"></i>
						<i class="el-icon-caret-top icon-top"></i>
					</div>
					<div class="nav-hover">
						<div class="nav-area">
								<div class="nav-item">
									<div class="nav-title">公众号</div>
									<div class="line"></div>
									<div class="nav-content">
										<img src="https://imgo.hackhome.com/img2018/1/12/9/551027876.jpg" />
									</div>
								</div>
						</div>
					</div>
				</div>
                <div class="top-title">关于我们</div>
				<div class="top-title">帮助中心</div>
				<div class="video-help">
					<span>视频帮助</span>
					<span class="video-new">NEW</span>
				</div>
				<div class="top-title">
					<span>消息</span>
					<span style="color: red; padding-left: 4px">(0)</span>
				</div>
				<div style="color: rgba(0, 0, 0, 0.3)">|</div>
				<!-- 已登录 -->
				<div class="user-login">
					<div class="user-nickname">
						<i class="el-icon-s-custom icon-user"></i>
                        <span class="user-name">{{ nickname }}</span>
						<i class="el-icon-caret-bottom icon-bottom" style="color: #7c7c7c"></i>
						<i class="el-icon-caret-top icon-top"></i>
					</div>
					<div class="user-setting">
						<div style="white-space: nowrap">
							<div class="setting-title" @click="goPage('info')">个人信息</div>
							<div class="setting-title" @click="goPage('phone')" >手机号码验证</div>
							<div class="setting-title" @click="goPage('headimg')" >个人头像修改</div>
							<div class="setting-title" >意见反馈</div>
							<div class="setting-title" @click="loginOut">退出</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'

export default {
	name: 'ExamHeader',
	components: {
	},
	props: {
		webInfo: {
			type: Object,
			default: () => {},
		},
		productList: {
			type: Array,
			default: () => [],
		},
		siteNavList: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
		}
	},
	computed: {
		...mapGetters({
			theme: 'settings/theme',
			token: 'user/token',
			nickname: 'user/nickname',
		}),
	},
	beforeMount() {
		window.addEventListener('resize', this.handleLayouts)
		this.handleLayouts()
		this.updateTheme()
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.handleLayouts)
	},
	methods: {
		...mapActions({
			toggleDevice: 'settings/toggleDevice',
			foldSideBar: 'settings/foldSideBar',
			openSideBar: 'settings/openSideBar',
			updateTheme: 'settings/updateTheme',
			setToken: 'user/setToken',
			setNickname: 'user/setNickname',
			logout: 'user/logout',
		}),
		handleLayouts() {
			const isMobile = document.body.getBoundingClientRect().width - 1 < 992
			if (this.isMobile !== isMobile) {
				if (isMobile) {
					this.foldSideBar()
				} else this.openSideBar()
				this.toggleDevice(isMobile ? 'mobile' : 'desktop')
				this.isMobile = isMobile
			}
		},
		// 退出登录
		loginOut() {
			this.logout()
            this.$router.push('/')
		},
		goPage(page){
			this.$router.push('/my?page='+page)
			//  this.$forceUpdate();
		}
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	position: relative;
	width:auto;
	min-width: 1350px;
	margin: 0 auto;
	height: 35px;	
	padding-left: 8px;
	padding-right: 8px;
}

.web-title {
    float: left;
    img{
        width:100px;
    }
}

.title-text {
    padding: 0 20px;
    display: inline-block;
    height: 35px;
    line-height: 35px;
    font-size: 18px;
    font-weight: bold;
}

   
.wechat-nav:hover {
    cursor: pointer;
    .nav-hover {
        display: block;
    }
    .nav-product {
        display: block;
    }
    .icon-top {
        display: inline;
        color: #1fb0ff;
    }
    .icon-bottom {
        display: none;
    }
    .wechat-title {
        color: #1fb0ff;
    }
}

.wechat-nav {
    position: relative;
    height: 35px;
    line-height: 35px;
    padding: 0px 16px;
    font-size: 12px;


    .nav-hover {
        border: 1px solid #1fb0ff;
        display: none;
        position: absolute;
        padding: 10px 20px;
        z-index: 9999;
        background: #fff;
        border-radius: 6px;
        text-align: center;
        box-shadow: 0 0 5px #ccc;
        &:hover {
            cursor: default;
        }

      
    }

     


    .nav-product {
        border: 1px solid var(--colors4);
        display: none;
        position: absolute;
        z-index: 9999;
        background: #fff;
        border-radius: 6px;
        box-shadow: 0 0 5px #ccc;
        &:hover {
            cursor: default;
        }
    }
    .nav-area {
        display: flex;
        justify-content: space-between;
        margin-right: -20px;
        .nav-title {
            color: #666;
            &:hover {
                cursor: text;
            }
        }
        .line {
            width: 100%;
            height: 1px;
            background-color: rgba(225, 225, 225, 1);
        }
        .nav-content {
            white-space: nowrap;
            img{
                width:260px;
                height: 260px;
            }
            .content-name {
                color: #333;
                &:hover {
                    cursor: pointer;
                    color: #1fb0ff;
                }
            }
        }
    }
    .nav-item {
        margin-right: 20px;
    }

    
    .product-list {
        white-space: nowrap;
        .product-title {
            color: #666;
            padding: 0px 20px 0px 12px;
            height: 40px;
            line-height: 40px;
            &:hover {
                cursor: pointer;
                color: var(--colors4);
                background-color: #f5f5f5;
                &:first-child {
                    border-radius: 6px 6px 0px 0px;
                }
                &:last-child {
                    border-radius: 0px 0px 6px 6px;
                }
            }
        }
    }
}

.top-title {
    padding: 0px 16px;
    height: 35px;
    line-height: 35px;
    font-size: 12px;
    &:hover {
        cursor: pointer;
        color: var(--colors4);
    }
    .login-btn {
        padding: 5px 10px;
        margin-right: 10px;
        background-color: var(--colors);
        border-radius: 4px;
        color: #fff;
        &:hover {
            cursor: pointer;
            background-color: var(--colors4);
        }
    }
    .register-btn {
        color: var(--colors);
        &:hover {
            cursor: pointer;
        }
    }
}

.video-help {
    position: relative;
    padding: 0px 16px;
    height: 35px;
    line-height: 35px;
    font-size: 12px;
    &:hover {
        cursor: pointer;
        color: var(--colors4);
    }
    .video-new {
        position: absolute;
        top: 2px;
        right: 4px;
        width: 20px;
        height: 10px;
        line-height: 10px;
        text-align: center;
        font-size: 8px;
        color: red;
        font-family: 'Courier New', Courier, monospace;
    }
}

.user-login {
    position: relative;
    padding-left: 16px;
    height: 35px;
    line-height: 35px;
    font-size: 12px;
    &:hover {
        cursor: pointer;
        color: #1fb0ff;
    }
    .user-nickname {
        display: flex;
        align-items: center;
        height: 35px;
    }
    .user-setting {
        border: 1px solid #1fb0ff;
        display: none;
        position: absolute;
        right: 0px;
        z-index: 9999;
        background: #fff;
        border-radius: 6px;
        box-shadow: 0 0 5px #ccc;
        .setting-title {
            color: #666;
            height: 40px;
            line-height: 40px;
            padding: 0px 60px 0px 12px;
            &:hover {
                cursor: pointer;
                color: #1fb0ff;
                background-color: #f5f5f5;
                &:first-child {
                    border-radius: 6px 6px 0px 0px;
                }
                &:last-child {
                    border-radius: 0px 0px 6px 6px;
                }
            }
        }
    }
}

.user-login:hover{
    .user-setting {
	    display: block;
    }
    .icon-bottom {
		display: none;
	}
    .icon-top {
		display: inline;
		color: #1fb0ff;
	}
    .user-name {
		color: #1fb0ff;
	}
    .icon-user{
        color: #1fb0ff;
    }
}

.icon-bottom {
	font-size: 14px;
	margin-left: 4px;
	display: inline;
}
.icon-top {
	display: none;
	font-size: 14px;
	margin-left: 4px;
}
.icon-user {
	font-size: 14px;
	color: #999;
}
.user-name {
	padding-left: 2px;
	width: auto;
	max-width: 500px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	font-size: 14px;
	color: #666;
}
.top-title:last-child {
	padding-right: 0px;
}


.top-item {
	height: 35px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
}


.logo {
	float: left;
	height: 16px;
	width: 153px;
	background: url(https://zxxkstatic.zxxk.com/skins/common/images/header-logo.png) center center no-repeat;
	background-size: 100%;
	margin-top: 10px;
}

.header-right {
	float: right;
	position: relative;
	color: #666;
	font-size: 13px;
	line-height: 36px;
	height: 36px;
	text-align: right;
	z-index: 4;
	width: auto !important;
	padding-right: 0px !important;
}

.user-info {
	position: relative;
	float: left;
	margin: 0 10px 0 0;
}

.my-download-btn {
	float: left;
	padding: 0 10px;
	color: #80848b !important;
}

.server-btn {
	position: relative;
	float: left;
	padding: 0 10px;
	text-align: center;
	color: #0080ff !important;
	line-height: 36px !important;
}

.map {
	position: relative !important;
	float: left !important;
	z-index: 100;
	top: 0;
	right: 0;
	color: #80848b !important;
	line-height: 36px !important;

	.title {
		padding: 0 10px 0 15px;
		cursor: pointer;
		height: 36px !important;
		font-size: 13px;
		line-height: 36px;
		color: #80848b;

		.icon-down {
			display: inline-block !important;
			width: 0 !important;
			height: 0 !important;
			border-style: solid;
			border-width: 3px;
			border-radius: 2px;
			border-color: transparent #bbbbbb #bbbbbb transparent;
			position: absolute;
			top: 18px;
			right: 2px;
			-webkit-transition: all 0.3s;
			-o-transition: all 0.3s;
			transition: all 0.3s;
		}
	}

	.list-box {
		display: none;
		position: absolute;
		right: 0;
		top: 35px;
		z-index: 1000;
		-webkit-box-shadow: 0 2px 6.51px 0.49px rgb(9 2 4 / 16%);
		box-shadow: 0 2px 6.51px 0.49px rgb(9 2 4 / 16%);
		border-radius: 10px;
		width: auto !important;

		.sanjiao {
			position: absolute;
			top: -8px;
			right: 20px;
			display: block;
			width: 24px;
			height: 10px;
			background-image: none;
			background-position: center;
			background-repeat: no-repeat;
		}

		.list {
			border: 1px solid #e6e7e9;
			background-color: #fff;
			padding: 20px 21px 0px 21px !important;
			border-radius: 10px;
			overflow: hidden;
			-webkit-box-sizing: border-box;
			box-sizing: border-box;
			white-space: nowrap;
		}
	}
}

.message {
	float: left;
	position: relative;
	width: auto !important;

	.mark {
		margin: 0;
		color: #666;
		cursor: pointer;
		position: relative;
	}
}

b {
	color: #d2d4d6;
	float: left;
	margin: 0 18px 0 11px;
	display: block !important;
}
</style>