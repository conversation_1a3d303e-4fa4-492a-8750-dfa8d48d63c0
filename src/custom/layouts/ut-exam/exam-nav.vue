<template>
	<div class="wrapper-full">
		<div class="exam-wrapper">
			<div class="main-nav">
				<div v-if="$route.path!='/index'" class="subject-list"  @mouseleave="subjectLeave">
					<div class="subject-item"  @click="subjectEnter">
						<span>{{ exam.name }}</span>
						<i v-if="showSubjectDom" class="el-icon-arrow-up icon-up"></i>
						<i v-if="showSubjectDom" class="el-icon-arrow-down icon-down"></i>
					</div>
					<div v-if="showSubjectList && showSubjectDom" class="subject-active">
						<!-- <sub-left  :subject-data="subjectData" @courseSelect="courseSelect" /> -->
						<exam-list class="subject-card" @select="subjectLeave" />
					</div>
				</div>
				<!-- 首页基本选项 -->
				<div class="func-sub">
					<div class="sub-empty"></div>
					<template v-for="(item, index) in navData">
						<div :key="index" class="title" :class="{ active: item.path == $route.path, e: navActive(item) }" @click="itemClick(item)">
							<span>{{ item.title }}</span>
							<!-- 会有图标和子选项 -->
							<i v-if="item.children.length > 0" class="el-icon-arrow-down icon-down"></i>
							<i v-if="item.children.length > 0" class="el-icon-arrow-up icon-up"></i>
							<div v-if="item.children.length > 0" class="ai-project">
								<div v-for="(item2, index2) in item.children" :key="index2">
									<div :class="item2.path == $route.path ? 'ai-item-active' : 'ai-item'" @click="toChapter(item2)">{{ item2.title }}</div>
								</div>
							</div>
						</div>
					</template>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex'
export default {
	name: 'ExamNav',
	data() {
		return {
			showExamTip:false,
			subjectData: [],
			navData: [
				{
					id: '1',
					title: '首页',
					active: true,
					path: '/exam/index',
					children: [],
				},
				{
					id: '2',
					title: '考务管理',
					active: false,
					path: '/exam/kw',
					children: [],
				},
				{
					id: '3',
					title: '模板制作',
					active: false,
					path: '/exam/template',
					children: [],
				},				
				{
					id: '4',
					title: '试卷扫描',
					active: false,
					path: '/exam/scan',
					children: [],
				},{
					id: '5',
					title: '识别与校对',
					active: false,
					path: '/exam/recognition',
					children: [],
				},
				{
					id: '6',
					title: '阅卷管理',
					active: false,
					path: '/exam/mang',
					children: [],
				},
				{
					id: '7',
					title: '成绩报表',
					active: false,
					path: '/exam/report',
					children: [],
				},	
				{
					id: '8',
					title: '其他功能',
					active: false,
					path: '/exam/other',
					children: [],
				},		
				{
					id: '9',
					title: '基础信息',
					active: false,
					path: '/basic/teacher',
					children: [],
					noExamInfo:true,
				},
				// {
				// 	id: '4',
				// 	title: '成绩发布',
				// 	active: false,
				// 	path: '',
				// 	children: [
				// 		{
				// 			id: '01',
				// 			title: '章节智能组卷',
				// 			active: false,
				// 			path: '/question/group/chapter',
				// 		},
				// 		{
				// 			id: '02',
				// 			title: '知识点智能组卷',
				// 			active: false,
				// 			path: '/question/group/knowledge',
				// 		},
				// 	],
				// },
				
				// {
				// 	id: '8',
				// 	title: '我的',
				// 	active: false,
				// 	path: '/question/my/index',
				// 	children: [],
				// },
			],
			scrollLeft: -1,
			scrollTop: -1,
			showSubjectList: false,
			showSubjectDom: true,
		}
	},
	computed: {
		...mapGetters({
			exam:'exam/exam',
			gradeType: 'question/gradeType',
			course: 'question/course',
		}),
		// topTitle() {
		// 	return this.gradeType.name + this.course.name
		// },
		navActive() {
			return (item) => {
				let temp = false
				if (item.children) {
					item.children.forEach((element) => {
						temp = element.path == this.$route.path || temp
					})
				}
				return temp
			}
		},
	},
	watch: {
		$route(to) {
			if (to.path == '/question/index') {
				this.showSubjectDom = false
				this.scrolls()
			} else this.showSubjectDom = true
		},
	},
	created() {
		// this.getGCTypeList()
		// if (this.$router.currentRoute.path == '/question/index') {
		// 	this.showSubjectDom = false
		// 	this.scrolls()
		// } else this.showSubjectDom = true
	},

	methods: {
		...mapActions({
			setExamTip: 'exam/setTip',
			setBook: 'question/setBook',
			setGradeType: 'question/setGradeType',
			setCourse: 'question/setCourse',
			setEdition: 'question/setEdition',
		}),
		scrolls() {
			window.onscroll = () => {
				if (this.$router.currentRoute.path == '/question/index') {
					if (document.documentElement.scrollTop > 460) this.showSubjectDom = true
					else this.showSubjectDom = false
				}
			}
		},

		itemClick(item) {
			if(!this.exam.id && item.path!='/exam/index' && !item.noExamInfo){
				this.setExamTip(true)
				return
			}
			this.$router.push(item.path)
		},
		toChapter(item2) {
			this.$router.push(item2.path)
		},
		subjectEnter() {
			this.showSubjectList = true
		},
		subjectLeave() {
			this.showSubjectList = false
		},
		courseSelect(grade, course) {
			this.setGradeType({ id: grade.gradeType, name: grade.gradeTypeName })
			this.setCourse({ id: course.id, name: course.name })
			this.setEdition({ id: undefined, name: undefined })
			this.setBook({ id: undefined, name: undefined })
			this.showSubjectList = false
		},
		async getGCTypeList() {
			const {data} = await this.$ut.api('schoolexam/course/gradeTypeCourseList')

			this.subjectData = data
			//如果没有默认科目查找默认设置初中第一个科目
			if (!this.gradeType.id || !this.course.id) {
				var obj = this.subjectData.find((u) => u.gradeType == 2)
				if (obj) {
					this.setGradeType({ id: obj.gradeType, name: obj.gradeTypeName })
					if (obj.courses && obj.courses.length) {
						this.setCourse({ id: obj.courses[0].id, name: obj.courses[0].name })
					}
				}
			}
		},
	},
}
</script>
<style lang="scss" scoped>
.wrapper-full {
	background: #fff;
	box-shadow: 0 2px 3px 0 rgb(0 0 0 / 10%);
	position: sticky;
	top: 0;
	z-index: 998;

	.exam-wrapper {
		position: relative;
		width: auto;
		min-width: 1350px;
		margin: 0 auto;
		padding-left: 8px;
		padding-right: 8px;
	}
}

.main-nav {
	width: 100%;
	height: 44px;
	background-color: #fff;
	display: flex;
	align-items: center;
	font-size: 15px;
	justify-content: space-between;

	.subject-list {
		margin-right: 30px;
		border-radius: 6px 6px 0 0;
		position: relative;
	}
	.icon-down {
		display: inline;
	}
	.icon-up {
		display: none;
	}
	.subject-list:hover .icon-down {
		display: none;
	}
	.subject-list:hover .icon-up {
		display: inline;
	}
	// .subject-list:hover .subject-card {
	// //	display: block;
	// }

	.subject-item {
		width: 320px;
		height: 44px;
		line-height: 44px;
		padding: 0 14px;
		color: #fff;
		border-radius: 6px 6px 0 0;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		background-color: var(--colors);
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 13px;
		text-align: center;
	}

	.subject-active {
		position: relative;

		.subject-card {
			//display: none;
			position: absolute;
			z-index: 10000;
			top: 0px;
			left: 0px;
			width: 520px;
			border: 1px solid var(--colors);
			background-color: #fff;
			padding: 10px;
			border-radius: 0 0 4px 4px;
		}
	}

	.subject-list:hover .subject-item {
		cursor: pointer;
		background-color: var(--colors4);
	}

	.func-sub {
		position: relative;
		height: 44px;
		line-height: 44px;
		display: flex;
		flex: 1;
		align-items: center;

		.title {
			position: relative;
			margin-right: 40px;
			&:last-child{
				margin-right: 0;
			}

			.ai-project {
				display: none;
				position: absolute;
				letter-spacing: 1px;
				width: 120px;
				// height: 80px;
				border-radius: 6px;
				font-size: 12px;
				background-color: #fff;
				box-shadow: 0px 4px 4px 4px rgb(0 0 0 / 4%);
				padding: 10px 0px;
				color: #000;
				&:hover {
					cursor: pointer;
					// color: #1890FF;
				}

				.ai-item {
					height: 32px;
					line-height: 32px;
					padding-left: 12px;
					&:hover {
						color: var(--colors4);
						background-color: #f5f5f5;
					}
					&-active {
						height: 32px;
						line-height: 32px;
						padding-left: 12px;
						color: var(--colors4);
						&:hover {
							color: var(--colors4);
							background-color: #f5f5f5;
						}
					}
				}
			}
		}
		.icon-down {
			display: inline;
			margin-left: 4px;
		}
		.icon-up {
			margin-left: 4px;
			display: none;
		}
		.title:hover .icon-down {
			display: none;
			margin-left: 4px;
		}
		.title:hover .icon-up {
			display: inline;
			margin-left: 4px;
		}

		.title:hover .ai-project {
			display: block;
		}

		.title:hover {
			color: var(--colors4);
			cursor: pointer;
		}

		.active {
			color: var(--colors4);
			font-weight: bold;
			cursor: pointer;
		}
	}
	.title:last-child {
		position: absolute;
		right: 0px;
	}
}
.e {
	color: var(--colors);
}

</style>
