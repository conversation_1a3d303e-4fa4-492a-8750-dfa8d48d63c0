<template>
	<div class="ut-empty" :style="{ '--colors': colors, '--colors2': colors + 'D0', '--colors3': colors + '60', '--colors4': LightDarkenColor(colors, -30) }">
			<ut-router-view />
	</div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
export default {
	name: 'UtEmpty',
	components: { 
	},
	data() {
		return {
			colors: '#1890ff',
		}
	},
	computed: {
		...mapGetters({
			theme: 'settings/theme',
		}),
	},
	mounted() {
		this.colors = !this.theme.backgroundColor ? '#1890ff' : this.theme.backgroundColor
	},
	beforeMount() {
		this.updateTheme()
	},
	beforeDestroy() {
	},
	methods: {
		...mapActions({
			updateTheme: 'settings/updateTheme',
		}),
		getRgbNum(sColor) {
			if (sColor.length === 4) {
				let sColorNew = '#'
				for (let i = 1; i < 4; i += 1) {
					//补全颜色值 例如：#eee,#fff等
					sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))
				}
				sColor = sColorNew
			}
			//处理六位颜色值
			let sColorChange = []
			for (let i = 1; i < 7; i += 2) {
				//核心代码，通过parseInt将十六进制转为十进制，parseInt只有一个参数时是默认转为十进制的，第二个参数则是指定转为对应进制
				sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2)))
			}
			return sColorChange
		},
		colorRgba(str, n) {
			//十六进制颜色值的正则表达式
			const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
			const sColor = str.toLowerCase()
			n = n || 1
			//十六进制颜色转换为RGB格式
			if (sColor && reg.test(sColor)) {
				let sColorChange = this.getRgbNum(sColor)
				return 'rgba(' + sColorChange.join(',') + ',' + n + ')'
			} else {
				return sColor
			}
		},
		LightDarkenColor(color, num) {
			let colorArr = this.getRgbNum(color)
			let sColorChange = []
			for (var i = 0; i < colorArr.length; i++) {
				let val = colorArr[i] + num
				if (val < 0) {
					val = 0
				}
				if (val > 255) {
					val = 255
				}
				sColorChange.push(val)
			}
			return 'rgba(' + sColorChange.join(',') + ',1)'
		},
	},
}
</script>

<style lang="scss" scoped>
.ut-exam{
	height: 100%;
	display: flex;
    flex-direction: column;
	// background: #fff;
}
.ut-exam-body {
	position: relative;
	// min-height: calc(100% - 75px);
	width: 1350px;
	min-width: 1350px;
	margin: 0 auto;
	// padding: 0 $base-padding;
	flex: 1;
	// background: #fff;
}
</style>
