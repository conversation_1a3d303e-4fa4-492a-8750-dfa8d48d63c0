<template>
	<div class="en-body" :style="{ '--colors': colors, '--colors2': colors + 'D0', '--colors3': colors + '60', '--colors4': LightDarkenColor(colors, -30) }">
		<div class="en-left">
			<div class="left-title">
				<div class="logo-icon"></div>
				<div class="en-title">EN物业管理系统</div>
			</div>
			<template v-for="(item, index) in leftNav">
				<div :key="index">
					<div class="leftNav-item" :style="{ color: Index == index ? '#fff' : '' }" @click="getLeftNav(item, index)">
						<i :class="item.icon"></i>
						<span class="item-title">{{ item.title }}</span>
						<i v-if="Index == index" class="el-icon-caret-top item-icon"></i>
						<i v-else class="el-icon-caret-bottom item-icon"></i>
					</div>

					<!-- 二级项 -->
					<template v-for="(obj, inx) in item.itemArr">
						<div v-if="Index == index" :key="inx" :class="itemIndex == inx ? 'item-item-true' : 'item-item'" @click="getLeftNavItem(obj, inx)">
							<i :class="obj.icon"></i>
							<span class="item-title1">{{ obj.title }}</span>
						</div>
					</template>
				</div>
			</template>
		</div>
		<div class="en-right">
			<div class="right-top">
				<div class="toolbar-top">
					<div class="top-left">
						<i class="el-icon-s-fold toolbar-icon" title="侧边伸缩"></i>
						<i class="el-icon-refresh-right toolbar-icon" title="刷新"></i>
						<span class="toolbar-title">演示小区</span>
					</div>

					<div class="top-right">
						<i class="el-icon-bell toolbar-icon" title="消息"></i>
						<i class="el-icon-price-tag toolbar-icons" title="便签"></i>
						<i class="el-icon-s-custom toolbar-icon" title="联系人"></i>
						<i class="el-icon-full-screen toolbar-icon" title="全屏"></i>
						<div class="toolbar-user">
							<img :src="userImg" class="user-img" />
							<span class="user-name">
								名称
								<i class="el-icon-caret-bottom"></i>
							</span>
						</div>
						<i class="el-icon-more toolbar-icon" title="主题"></i>
					</div>
				</div>
				<div class="toolbar-bottom">
					<i class="el-icon-d-arrow-left bottom-icon" title="左划"></i>
					<i class="el-icon-house bottom-icon" title="主页"></i>
					<div class="bottom-content">
						<template v-for="(item, index) in topNav">
							<div :key="index" :class="itemIndexOn == index ? 'topNav-item' : 'topNav-item-true'" @click="getTopTips(index)">
								<span class="content-title">{{ item.title }}</span>
								<i class="el-icon-close item-icon" @click="reMoveTopNav(item)"></i>
							</div>
						</template>
					</div>
					<i class="el-icon-d-arrow-right bottom-icon" title="右滑"></i>
					<i class="el-icon-arrow-down bottom-icon" title="展开"></i>
				</div>
			</div>
			<div class="right-content">ssss</div>
			<div class="right-bottom">
				<span class="bottom-company">云南邦途科技有限公司</span>
				<span class="bottom-num">在线:{{ personNum }}人</span>
				<span class="bottom-versions">copyright © 2019 zx.afjy.net all rights reserved. V1.0.0</span>
			</div>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
	name: 'Index',
	data() {
		return {
			colors: '#1890ff',
			personNum: 12, //在线人数
			Index: 0, //左边导航栏外层标记
			itemIndex: 0, //边导航栏内层标记
			itemIndexOn: 0, // 顶部小标签选中标记
			// userImg: require('@/views/tenement/components/static/en-logo.png'),
			userImg:'',
			leftNav: [
				{
					id: '01',
					title: '主页',
					icon: 'en-icon en-icon-home',
					itemArr: [
						{
							id: '01',
							title: '欢迎页',
							icon: '',
						},
						{
							id: '02',
							title: '切换小区',
							icon: '',
						},
					],
				},
				{
					id: '02',
					title: '房产信息',
					icon: 'fa fa-home',
					itemArr: [
						{
							id: '01',
							title: '用户信息',
							icon: '',
						},
						{
							id: '02',
							title: '查看房产',
							icon: '',
						},
					],
				},
				{
					id: '03',
					title: '装修管理',
					icon: 'fa fa-gavel',
					itemArr: [],
				},
				{
					id: '04',
					title: '三表管理',
					icon: 'fa fa-tablet',
					itemArr: [],
				},
				{
					id: '05',
					title: '车辆车位',
					icon: 'fa fa-automobile',
					itemArr: [],
				},
				{
					id: '06',
					title: '物业收费',
					icon: 'fa fa-dollar ',
					itemArr: [],
				},
				{
					id: '07',
					title: '财务管理',
					icon: 'fa fa-money',
					itemArr: [],
				},
				{
					id: '08',
					title: '门禁系统',
					icon: 'fa fa-drivers-license',
					itemArr: [],
				},
				{
					id: '09',
					title: '门禁（新）',
					icon: 'fa fa-life-buoy',
					itemArr: [],
				},
				{
					id: '10',
					title: '通知与催缴',
					icon: 'fa fa-volume-up',
					itemArr: [],
				},
				{
					id: '11',
					title: '微信与APP',
					icon: 'fa fa-mobile',
					itemArr: [],
				},
				{
					id: '12',
					title: '系统管理',
					icon: 'fa fa-cog',
					itemArr: [],
				},
			],
			topNav: [],
		}
	},
	computed: {
		...mapGetters({
			theme: 'settings/theme',
		}),
	},
	mounted() {
		this.colors = !this.theme.backgroundColor ? '#1890ff' : this.theme.backgroundColor
	},
	methods: {
		// leftNav一级项操作
		getLeftNav(item, index) {
			if (this.Index == index) {
				// 如果当前项被打开的情况下再次被点击，则收齐起该项
				this.Index = -1
			} else {
				// 如果一级标题改变，二级标题应该恢复默认
				this.Index = index
				// 但是当一级标题改变，改变后的一级标题下，有被选中的二级标题，就需要记录二级标题
				for (let i = 0; i < this.leftNav[index].itemArr.length; i++) {
					let elem = this.topNav.find((u) => u.title == this.leftNav[index].itemArr[i].title)
					if (elem) {
						// 有选中的二级标题，则记录该下标
						this.itemIndex = i
						break
					} else {
						// 没有选中的二级标题，则制空
						this.itemIndex = -1
					}
				}
			}
		},
		// leftNav二级项操作
		getLeftNavItem(obj, inx) {
			this.itemIndex = inx
			// 当前为空，可以随便添加
			if (this.topNav.length == 0) {
				this.topNav.push(obj)
				// 默认选中新添加的顶部小标题
				this.itemIndexOn = this.topNav.length - 1
			} else {
				//数组内有数据，需要先判断，不可加入重复的数据
				let elem = this.topNav.find((u) => u.title == obj.title)
				// 如果有重复的数据，应该指向顶部小标题，而不进行添加
				if (elem) {
					// 这里需要取下标，然后指向
					console.log(1)
				} else {
					this.topNav.push(obj)
					this.itemIndexOn = this.topNav.length - 1
				}
			}
		},
		// 得到顶部小标题
		getTopTips(index) {
			this.itemIndexOn = index
			// let obj = this.leftNav[this.Index].itemArr.find((u) => u.title == this.topNav[index].title)
			// 这里如果使用find，无法在找到的情况下，确认 this.itemIndex 的下标，所以用for
			for (let i = 0; i < this.leftNav[this.Index].itemArr.length; i++) {
				// 如果成立，则应该选中左侧leftNav的对应项
				if (this.leftNav[this.Index].itemArr[i].title == this.topNav[index].title) {
					this.itemIndex = i
					break
				}
			}
		},
		//点击删除顶部小标签
		reMoveTopNav(item) {
			let obj = this.topNav.find((u) => u.title == item.title)
			if (obj) {
				this.topNav.pop(obj)
				this.itemIndex = -1
			}
		},
		getRgbNum(sColor) {
			if (sColor.length === 4) {
				let sColorNew = '#'
				for (let i = 1; i < 4; i += 1) {
					//补全颜色值 例如：#eee,#fff等
					sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))
				}
				sColor = sColorNew
			}
			//处理六位颜色值
			let sColorChange = []
			for (let i = 1; i < 7; i += 2) {
				//核心代码，通过parseInt将十六进制转为十进制，parseInt只有一个参数时是默认转为十进制的，第二个参数则是指定转为对应进制
				sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2)))
			}
			return sColorChange
		},
		colorRgba(str, n) {
			//十六进制颜色值的正则表达式
			const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
			const sColor = str.toLowerCase()
			n = n || 1
			//十六进制颜色转换为RGB格式
			if (sColor && reg.test(sColor)) {
				let sColorChange = this.getRgbNum(sColor)
				return 'rgba(' + sColorChange.join(',') + ',' + n + ')'
			} else {
				return sColor
			}
		},
		LightDarkenColor(color, num) {
			let colorArr = this.getRgbNum(color)
			let sColorChange = []
			for (var i = 0; i < colorArr.length; i++) {
				let val = colorArr[i] + num
				if (val < 0) {
					val = 0
				}
				if (val > 255) {
					val = 255
				}
				sColorChange.push(val)
			}
			return 'rgba(' + sColorChange.join(',') + ',1)'
		},
	},
}
</script>

<style lang="scss" scoped>
* {
	margin: 0;
	padding: 0;
}

.en-body {
	width: 100%;
	height: 100%;
	position: absolute;
	display: flex;

	.en-left {
		width: 13%;
		height: 100%;
		background-color: rgb(36, 38, 47);
		.left-title {
			background-color: rgb(43, 45, 54);
			padding: 5px 7px;
			display: flex;
			margin-bottom: 5px;
			.logo-icon {
				width: 30px;
				height: 30px;
				// background: url('~@/views/tenement/components/static/en-logo.png');
				background-size: cover;
			}
			.en-title {
				height: 35px;
				line-height: 35px;
				color: #fff;
				font-size: 16px;
				font-weight: bolder;
				margin-left: 10px;
			}
		}
		.leftNav-item {
			color: rgba($color: #fff, $alpha: 0.7);
			padding: 12px 25px;
			border-left: 5px solid rgba($color: #000000, $alpha: 0);
			position: relative;
			&:hover {
				background-color: rgba(43, 45, 54, 0.9);
				border-left: 5px solid var(--colors);
				cursor: pointer;
				color: #fff;
			}
			.item-title {
				font-size: 14px;
			}
			.item-icon {
				position: absolute;
				right: 10px;
			}
		}
		.item-item {
			color: rgba($color: #fff, $alpha: 0.7);
			padding: 12px 25px 12px 35px;
			border-left: 5px solid rgba($color: #000000, $alpha: 0);
			font-size: 13px;
			background-color: rgba($color: #000000, $alpha: 0.6);

			&:hover {
				cursor: pointer;
			}
			&:hover .item-title1 {
				color: #fff;
			}
			.item-title1 {
			}
		}
		.item-item-true {
			color: rgba($color: #fff, $alpha: 1);
			padding: 12px 25px 12px 35px;
			border-left: 5px solid rgba($color: #000000, $alpha: 0);
			font-size: 13px;
			background-color: var(--colors);
			&:hover {
				cursor: pointer;
			}
			// &:hover .item-title1 {
			// 	color: #fff;
			// }
		}
	}
	.en-right {
		width: 87%;
		height: 100%;

		.right-top {
			width: 100%;

			.toolbar-top {
				display: flex;
				position: relative;
				border: 1px solid rgba($color: #000000, $alpha: 0.1);
				.top-left {
					font-size: 20px;
					.toolbar-title {
						padding: 10px 15px;
						border-top: 2px solid rgba($color: #000000, $alpha: 0);
						&:hover {
							border-top: 2px solid rgba($color: #000000, $alpha: 0.8);
							cursor: pointer;
						}
					}
					.toolbar-icon {
						padding: 10px 15px;
						border-top: 2px solid rgba($color: #000000, $alpha: 0);
						&:hover {
							border-top: 2px solid rgba($color: #000000, $alpha: 0.8);
							cursor: pointer;
						}
					}
					.toolbar-icons {
						padding: 10px 15px;
						border-top: 2px solid rgba($color: #000000, $alpha: 0);
						&:hover {
							border-top: 2px solid rgba($color: #000000, $alpha: 0.8);
							cursor: pointer;
						}
					}
				}
				.top-right {
					position: absolute;
					right: 0;
					font-size: 20px;
					.toolbar-icon {
						padding: 10px 15px;
						border-top: 2px solid rgba($color: #000000, $alpha: 0);
						&:hover {
							border-top: 2px solid rgba($color: #000000, $alpha: 0.8);
							cursor: pointer;
						}
					}
					.toolbar-icons {
						padding: 10px 15px;
						border-top: 2px solid rgba($color: #000000, $alpha: 0);
						&:hover {
							border-top: 2px solid rgba($color: #000000, $alpha: 0.8);
							cursor: pointer;
						}
					}
					.toolbar-user {
						padding: 5px 15px;
						display: inline-block;
						border-top: 2px solid rgba($color: #000000, $alpha: 0);
						&:hover {
							border-top: 2px solid rgba($color: #000000, $alpha: 0.8);
							cursor: pointer;
						}

						.user-img {
							width: 23px;
							height: 23px;
							border-radius: 50%;
						}
						.user-name {
							font-size: 15px;
						}
					}
				}
			}
			.toolbar-bottom {
				display: flex;
				position: relative;
				border: 1px solid rgba($color: #000000, $alpha: 0.1);

				.bottom-icon {
					padding: 10px;
					font-size: 14px;
					font-weight: bolder;
					border-left: 1px solid rgba($color: #000000, $alpha: 0.1);
					border-right: 1px solid rgba($color: #000000, $alpha: 0.1);
					&:hover {
						cursor: pointer;
						background-color: rgba($color: #000000, $alpha: 0.1);
					}
				}

				.bottom-content {
					width: 100%;
					display: flex;
					align-items: center;
					.topNav-item-true {
						padding: 9px;
						font-size: 12px;
						&:hover {
							cursor: pointer;
						}
						.item-icon {
							margin-left: 5px;
							border-radius: 50%;
							padding: 1px;
							&:hover {
								background-color: red;
								color: #fff;
							}
						}
					}
					.topNav-item {
						padding: 9px;
						font-size: 12px;
						border-top: 3px solid rgba($color: #000000, $alpha: 1);
						background-color: rgba($color: #000000, $alpha: 0.2);
						&:hover {
							cursor: pointer;
						}
						.item-icon {
							margin-left: 5px;
							border-radius: 50%;
							padding: 1px;
							&:hover {
								background-color: red;
								color: #fff;
							}
						}
					}
				}
			}
		}
		.right-content {
			width: 100%;
			// height: 607px;
			height: 84%;
			background-color: rgb(242, 242, 242);
		}
		.right-bottom {
			padding: 10px;
			font-size: 13px;
			position: relative;
			background-color: pink;
			.bottom-company {
				font-weight: bold;
				&:hover {
					cursor: pointer;
					color: #000000;
				}
			}
			.bottom-num {
				margin-left: 20px;
				&:hover {
					cursor: pointer;
				}
			}
			.bottom-versions {
				position: absolute;
				right: 10px;
			}
		}
	}
}
</style>