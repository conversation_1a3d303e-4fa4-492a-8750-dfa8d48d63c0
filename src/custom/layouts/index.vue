<template>
	<div class="wrapper clearfix">
		<a href="https://zujuan.xkw.com/" class="logo" target="_blank"></a>
		<div class="header-right">
			<div id="LoginInfo" class="user-info">
				<span id="un-login" class="un-login">
					<a href="https://sso.zxxk.com/login?service=https%3A%2F%2Fauth.zxxk.com%2Fhtml%2Flogincallback.html%3Fcurl%3Dhttps%3A%2F%2Fwww.zxxk.com%2F" class="login-btn">登录</a>
					<span>/</span>
					<a href="https://sso.zxxk.com/user/signup?service=https%3A%2F%2Fauth.zxxk.com%2Fhtml%2Flogincallback.html%3Fcurl%3Dhttps%3A%2F%2Fwww.zxxk.com%2F" class="reg-btn">注册</a>
				</span>
			</div>
			<a class="my-download-btn" href="//user.zxxk.com/ConsumeLog/list" target="_blank">我的下载</a>
			<a
				class="server-btn"
				href="https://zujuan.21cnjy.com/"
				target="_blank"
			>
				21组卷网
			</a>
			<div class="map service-btn">
				<div class="title">
					帮助中心
					<span class="icon-down"></span>
				</div>
				<div class="list-box help-box">
					<span class="sanjiao help-sanjiao"></span>
					<ul class="list help clearfix">
						<li>
							<ul class="item">
								<li>
									<a href="https://www.zxxk.com/service/" target="_blank">客户服务</a>
									<a href="https://www.zxxk.com/service/detail-video.html" target="_blank">视频帮助</a>
									<a href="https://wxt.zxxk.com/college/" target="_blank">客户学院</a>
								</li>
							</ul>
						</li>
					</ul>
				</div>
			</div>
			<div class="map">
				<div class="title">
					网站导航
					<span class="icon-down"></span>
				</div>
				<div class="list-box">
					<span class="sanjiao"></span>
					<ul class="list nav-header clearfix">
						<li>
							<div class="subtitle">阶段</div>
							<ul class="item">
								<li>
									<a href="https://www.zxxk.com/p-yw/" target="_blank">小学</a>
									<a href="https://www.zxxk.com/m-yw/" target="_blank">初中</a>
									<a href="https://www.zxxk.com/h-yw/" target="_blank">高中</a>
									<a href="https://yw.zxxk.com/sv/" target="_blank">
										中职
										<span class="new"><i>new</i></span>
									</a>
									<a href="https://zhongkao.zxxk.com" target="_blank">中考</a>
									<a href="https://gaokao.zxxk.com" target="_blank">高考</a>
								</li>
							</ul>
						</li>
						<li>
							<div class="subtitle">学科</div>
							<ul class="item">
								<li>
									<a href="http://yw.zxxk.com/" target="_blank">语文</a>
									<a href="http://sx.zxxk.com/" target="_blank">数学</a>
									<a href="http://yy.zxxk.com/" target="_blank">英语</a>
									<a href="http://wl.zxxk.com/" target="_blank">物理</a>
									<a href="http://hx.zxxk.com/" target="_blank">化学</a>
									<a href="http://sw.zxxk.com/" target="_blank">生物</a>
									<a href="http://zz.zxxk.com/" target="_blank">政治</a>
									<a href="http://ls.zxxk.com/" target="_blank">历史</a>
									<a href="http://dl.zxxk.com/" target="_blank">地理</a>
								</li>
								<li>
									<a href="http://kx.zxxk.com/" target="_blank">科学</a>
									<a href="http://xx.zxxk.com/" target="_blank">信息</a>
									<a href="http://lsysh.zxxk.com/m/books/" target="_blank">历史与社会</a>
									<a href="http://tz.zxxk.com/h/books-b4462/" target="_blank">班会</a>
									<a href="http://tyjs.zxxk.com/h/books/" target="_blank">通用技术</a>
									<a href="http://ty.zxxk.com/h/books/" target="_blank">体育与健康</a>
									<a href="http://ms.zxxk.com/h/books/" target="_blank">美术</a>
									<a href="http://yinyue.zxxk.com/h/books/" target="_blank">音乐</a>
									<a href="http://lj.zxxk.com/h/books/" target="_blank">劳动技术</a>
								</li>
							</ul>
						</li>
						<li>
							<div class="subtitle">资料类型</div>
							<ul class="item">
								<li>
									<a href="http://sj.zxxk.com/" target="_blank">试卷</a>
									<a href="http://kj.zxxk.com/" target="_blank">课件</a>
									<a href="http://ja.zxxk.com/" target="_blank">教案</a>
									<a href="http://sc.zxxk.com/" target="_blank">素材</a>
									<a href="http://xa.zxxk.com/" target="_blank">学案/导学</a>
								</li>
							</ul>
						</li>
						<li>
							<div class="subtitle">特色资源</div>
							<ul class="item">
								<li>
									<a href="http://mingxiao.zxxk.com/" target="_blank">名校</a>
									<a href="http://jp.zxxk.com/" target="_blank">精品</a>
									<a href="https://b.zxxk.com/" target="_blank">教辅</a>
									<a href="http://www.zxxk.com/zj/" target="_blank">专辑</a>
									<a href="http://www.zxxk.com/tj/" target="_blank">套卷</a>
									<a href="https://tz.zxxk.com/h/books-v703/" target="_blank">综合</a>
									<a href="https://www.zxxk.com/mingshi/" target="_blank">名师堂</a>
									<a href="https://www.zxxk.com/zhuanti/" target="_blank">专题</a>
									<a href="http://news.zxxk.com/" target="_blank">资讯</a>
								</li>
							</ul>
						</li>
						<li>
							<div class="subtitle">其他</div>
							<ul class="item">
								<li>
									<a href="http://www.zxxk.com/qudao/" target="_blank">渠道招募</a>
									<a href="https://xiaozhang.xkw.com/" target="_blank">校长帮</a>
								</li>
							</ul>
						</li>
					</ul>
				</div>
			</div>
			<div class="map">
				<div class="title">
					旗下产品
					<span class="icon-down"></span>
				</div>
				<div class="list-box">
					<span class="sanjiao"></span>
					<ul class="list product clearfix">
						<li>
							<div class="subtitle">智慧教学</div>
							<ul class="item">
								<li>
									<a href="https://www.zxxk.com/" target="_blank" rel="nofollow">学科网</a>
									<a href="https://zujuan.xkw.com/" target="_blank" rel="nofollow">组卷网</a>
									<a href="https://wxt.xkw.com/introduce/index.html" target="_blank" rel="nofollow">网校通</a>
									<a href="https://kehou.xkw.com" target="_blank" rel="nofollow">
										课后服务
										<span class="new"><i>new</i></span>
									</a>
									<a href="https://wxt.zxxk.com/hmketang/" target="_blank" rel="nofollow">3D课堂</a>
									<a href="https://zyke.zxxk.com/vr/introduce/" target="_blank" rel="nofollow">
										中职专业课
										<span class="new"><i>new</i></span>
									</a>
									<a href="https://beike.zxxk.com/" target="_blank" rel="nofollow">e备课</a>
									<a href="https://e.xkw.com/" target="_blank" rel="nofollow">e课堂</a>
								</li>
							</ul>
						</li>
						<li>
							<div class="subtitle">考试测评</div>
							<ul class="item">
								<li>
									<a href="https://ejt.xkw.com" target="_blank">e卷通</a>
									<a href="http://aixk.zxxk.com/index.html" target="_blank">AI学科</a>
									<a href="http://xycp.xkw.com/index" target="_blank">区域考试</a>
									<a href="http://www.zxxk.com/subject/gk-2022/2023dlk/?id=2753602" target="_blank">大联考(2023届)</a>
								</li>
							</ul>
						</li>
						<li>
							<div class="subtitle">教师成长</div>
							<ul class="item">
								<li>
									<a href="https://yx.xkw.com" target="_blank">
										AI研修
										<span class="hot"><i>hot</i></span>
									</a>
									<a href="https://www.zxxk.com/activity/2022train_camp/" target="_blank">
										班主任特训营
										<span class="new"><i>new</i></span>
									</a>
								</li>
							</ul>
						</li>
						<li>
							<div class="subtitle">赋能行业</div>
							<ul class="item">
								<li>
									<a href="https://open.xkw.com" target="_blank">
										开放平台
										<span class="hot"><i>hot</i></span>
									</a>
									<a href="https://b.zxxk.com/join_us" target="_blank">学科网书城</a>
								</li>
							</ul>
						</li>
						<li>
							<div class="subtitle">其他</div>
							<ul class="item">
								<li>
									<a href="https://e.xkw.com/b2b/index.html#/record-board" target="_blank">录播通</a>
									<a href="https://e.xkw.com/b2b/index.html#/black-board" target="_blank">智慧黑板</a>
								</li>
							</ul>
						</li>
					</ul>
				</div>
			</div>
			<b>|</b>
			<div class="message">
				<div class="mark" data-io-click="tracker" name="message_button" onclick="window.open('https://user.zxxk.com/systemmessage/')">
					<i class="iconfont icon-xiaoxi"></i>
					<span>消息</span>
					<em style="display: none">0</em>
				</div>
				<div class="message-list" style="display: none">
					<iframe id="messageframe" style="width: 100%; height: 100%; overflow: hidden; border: 0px" data-src="https://user.zxxk.com/message/MessageHeader"></iframe>
				</div>
				<div class="tips" style="display: none">
					<span id="unreadmessage"></span>
					<a class="look" href="https://user.zxxk.com/systemmessage/" target="_blank">马上查看</a>
					<span class="close" data-io-click="tracker" name="message_popup_close">×</span>
				</div>
				<!-- 新注册用户下载免费哦---弹框 start 20211209 -->
				<div class="new-registration-tips parent-box" style="display: none">
					<i data-io-click="tracker" name="homepage_newuser_register_pilot_popup_close">×</i>
					<a
						data-io-click="tracker"
						name="top_register"
						href="https://sso.zxxk.com/user/signup?service=https%3A%2F%2Fzxxkstatic.zxxk.com%2Flogin%2Flogincallback.html%3Fcurl%3Dhttps%3A%2F%2Fwww.zxxk.com%2F"
					></a>
				</div>
				<!-- 新注册用户下载免费哦---弹框 end  -->
			</div>
		</div>
        <component :is="'ut-question'"/>
        
        <el-backtop target="#app" />
		<!--  主题组件放到layouts下防止主题切换，导致主题组件重新加载 -->
		<ut-theme-drawer />
		<ut-theme-setting />
	</div>
</template>

<script>
	import { mapActions, mapGetters } from 'vuex'

	export default {
		name: 'Layouts',
		data() {
			return {
				isMobile: false,
			}
		},
		computed: {
			...mapGetters({
				theme: 'settings/theme',
			}),
		},
		beforeMount() {
			window.addEventListener('resize', this.handleLayouts)
			this.handleLayouts()
			this.updateTheme()
		},
		beforeDestroy() {
			window.removeEventListener('resize', this.handleLayouts)
		},
		methods: {
			...mapActions({
				toggleDevice: 'settings/toggleDevice',
				foldSideBar: 'settings/foldSideBar',
				openSideBar: 'settings/openSideBar',
				updateTheme: 'settings/updateTheme',
			}),
			handleLayouts() {
				const isMobile = document.body.getBoundingClientRect().width - 1 < 992
				if (this.isMobile !== isMobile) {
					if (isMobile) {
						this.foldSideBar()
					} else this.openSideBar()
					this.toggleDevice(isMobile ? 'mobile' : 'desktop')
					this.isMobile = isMobile
				}
			},
		},
	}
</script>

<style lang="scss">
.wrapper {
	position: relative;
	width: 1200px;
	min-width: 1200px;
	margin: 0 auto;
}

.logo {
	background-size: 100%;
}

.header-right {
	float: right;
	position: relative;
	color: #666;
	font-size: 13px;
	line-height: 36px;
	height: 36px;
	text-align: right;
	z-index: 4;
	width: auto !important;
	padding-right: 0px !important;
}

.user-info {
	position: relative;
	float: left;
	margin: 0 10px 0 0;
}

.my-download-btn {
	float: left;
	padding: 0 10px;
	color: #80848b !important;
}

.server-btn {
	position: relative;
	float: left;
	padding: 0 10px;
	text-align: center;
	color: #0080ff !important;
	line-height: 36px !important;
}

.map {
	position: relative !important;
	float: left !important;
	z-index: 100;
	top: 0;
	right: 0;
	color: #80848b !important;
	line-height: 36px !important;

	.title {
		padding: 0 10px 0 15px;
		cursor: pointer;
		height: 36px !important;
		font-size: 13px;
		line-height: 36px;
		color: #80848b;

		.icon-down {
			display: inline-block !important;
			width: 0 !important;
			height: 0 !important;
			border-style: solid;
			border-width: 3px;
			border-radius: 2px;
			border-color: transparent #bbbbbb #bbbbbb transparent;
			position: absolute;
			top: 18px;
			right: 2px;
			-webkit-transition: all 0.3s;
			-o-transition: all 0.3s;
			transition: all 0.3s;
		}
	}

	
}

.message {
    float: left;
    position: relative;
    width: auto !important;

    .mark {
    margin: 0;
    color: #666;
    cursor: pointer;
    position: relative;
}
}

</style>