<template>
	<div class="subject-box">
		<template v-for="(item, index) in subjectData">
			<div :key="index">
				<div class="grade-type" @click="onAll(item, index)">{{ item.gradeTypeName }}</div>
				<div class="course-list">
					<template v-for="(item2, index2) in item.courses">
						<span :key="index2">
							<span class="course-name" :class="item2.isShow ? 'active' : ''" @click="tabTitle(item2, index)">{{ item2.name }}</span>
						</span>
					</template>
				</div>
			</div>
		</template>
	</div>
</template>
<script>
export default {
	name: 'SubLeftCheck',
	// props: {
	// 	subjectData: {
	// 		type: Array,
	// 		default: () => [],
	// 	},
	// },
	data() {
		return {
			subjectData: [],
			checkMore: [], // 多选数组
			checkArr: [
				{
					gradeType: 1,
					checkMore: [], // 多选数组
				},
				{
					gradeType: 2,
					checkMore: ['01'], // 多选数组
				},
				{
					gradeType: 3,
					checkMore: [], // 多选数组
				},
			],
			checkIndex: 0, // 记录全选的按钮是哪个
		}
	},
	computed: {},
	created() {
		this.getGCTypeList()
	},
	methods: {
		getGCTypeList() {
			this.$ut.api('question.course.gradeTypeCourseList').then((res) => {
				if (res.data) {
					this.subjectData = res.data
					this.subjectData.forEach((item) => {
						item.courses.forEach((e) => {
							this.$set(e, 'isShow', false)
						})
					})
					// let obj = this.subjectData.find((u) => u.gradeType == 2)
					// if (obj) obj.courses[0].isShow = true

					//如果没有默认科目查找默认设置初中第一个科目
					// if (!this.gradeType.id || !this.course.id) {
					// 	var obj = this.subjectData.find((u) => u.gradeType == 2)
					// 	if (obj) {
					// 		this.setGradeType({ id: obj.gradeType, name: obj.gradeTypeName })
					// 		if (obj.courses && obj.courses.length) {
					// 			this.setCourse({ id: obj.courses[0].id, name: obj.courses[0].name })
					// 		}
					// 	}
					// }
				}
				this.setDefault() //根据传入的值设置默认值
			})
		},
		// 选中所有
		onAll(item, index) {
			// 如果if成立，表示当前为全选状态，需要先将数组制空，不然会有重复数据
			if (this.checkArr[index].gradeType == this.checkIndex) this.checkArr[index].checkMore = []
			this.checkIndex = item.gradeType
			item.courses.forEach((e) => {
				e.isShow = !e.isShow
				if (e.isShow) this.checkArr[index].checkMore.push(e.id)
				else this.checkArr[index].checkMore.pop(e.id)
			})
		},
		// 单击选择某项(可多选)
		tabTitle(item2, index) {
			item2.isShow = !item2.isShow
			if (item2.isShow) this.checkArr[index].checkMore.push(item2.id)
			else this.checkArr[index].checkMore.pop(item2.id)
		},
		// 根据传入的数据设置默认值
		setDefault() {
			this.checkArr.forEach((element) => {
				if (element.checkMore.length != 0) {
					element.checkMore.forEach((e) => {
						let obj = this.subjectData[element.gradeType - 1].courses.find((u) => u.id == e)
						if (obj) obj.isShow = true
					})
				}
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.subject-box {
	padding: 10px 10px 14px;
	// 暂时加的下面几个
	width: 260px;
	background-color: #fff;
	margin: 30px 0;
	border: 1px solid var(--colors);
	.grade-type {
		font-weight: bold;
		margin: 6px;
		letter-spacing: 1px;
		font-size: 14px;
		color: #333;
	}
	.grade-type:hover {
		cursor: pointer;
	}
	.course-list {
		padding: 0px 7px;
		.course-name {
			height: 26px;
			font-size: 12px;
			line-height: 26px;
			letter-spacing: 1px;
			margin-right: 4px;
			padding: 4px 6px;
			white-space: nowrap;
		}
		.course-name:hover {
			border-radius: 2px;
			cursor: pointer;
			color: #fff;
			background-color: var(--colors);
		}
		.course-name.active {
			border-radius: 2px;
			cursor: pointer;
			color: #fff;
			background-color: var(--colors);
		}
	}
}
</style>
