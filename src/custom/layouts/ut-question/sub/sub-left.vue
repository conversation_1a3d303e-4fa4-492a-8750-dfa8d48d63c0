<template>
	<div class="subject-box">
		<template v-for="(item, index) in subjectData">
			<div :key="index">
				<div class="grade-type">{{ item.gradeTypeName }}</div>
				<div class="course-list">
					<template v-for="(item2, index2) in item.courses">
						<span :key="index2" class="course-name" :class="item.gradeType == gradeType.id && item2.id == course.id ? 'active' : ''" @click="tabTitle(item, item2)">{{ item2.name }}</span>
					</template>
				</div>
			</div>
		</template>
	</div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex'
export default {
	name: 'SubLeft',
	props: {
		subjectData: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {}
	},
	computed: {
		...mapGetters({
			gradeType: 'question/gradeType',
			course: 'question/course',
		}),
	},
	created() {},
	mounted() {},
	methods: {
		...mapActions({
			setBook: 'question/setBook',
			setGradeType: 'question/setGradeType',
			setCourse: 'question/setCourse',
			setEdition: 'question/setEdition',
		}),
		tabTitle(item, item2) {
			this.$emit('courseSelect', item, item2)
			if (this.$router.currentRoute.path == '/question/index') {
				//this.setCourse()
				this.$router.push('/question/chapter/index')
			}
		},
	},
}
</script>
<style lang="scss" scoped>
.subject-box {
	padding: 10px 10px 14px;
	.grade-type {
		font-weight: bold;
		margin: 6px;
		letter-spacing: 1px;
		font-size: 14px;
		color: #333;
	}
	.course-list {
		padding: 0px 7px;
		.course-name {
			height: 26px;
			font-size: 12px;
			line-height: 26px;
			letter-spacing: 1px;
			margin-right: 4px;
			padding: 4px 6px;
			white-space: nowrap;
		}
		.course-name:hover {
			border-radius: 2px;
			cursor: pointer;
			color: #fff;
			background-color: var(--colors);
		}
		.course-name.active {
			border-radius: 2px;
			cursor: pointer;
			color: #fff;
			background-color: var(--colors);
		}
	}
}
</style>
