<template>
	<div class="wrapper-full">
		<div class="wrapper">
			<div class="search-box">
				<!-- logo -->
				<div class="logo-area">
					<div class="logo-outer">
						<!-- <img class="logo" src="https://img2.baidu.com/it/u=3996070207,1216924531&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500"> -->
						<img class="logo" :src="webInfo.imgHead" />
					</div>
				</div>
				<!-- 搜索框 -->
				<div class="search-list">
					<div class="search">
						<div class="hover-input">
							<input v-model="inputValue" class="search-input" placeholder="感受教学魅力" />
							<div class="search-active">
								<div class="recommend-title">为你推荐</div>
								<div class="rec-title">
									<template v-for="(item, index) in recommendList">
										<div :key="index" class="rec-list" @click="recommend">
											<i v-if="item.active" class="el-icon-view"></i>
											<i v-if="!item.active" class="el-icon-view" style="color: red"></i>
											<span style="margin-left: 4px; color: gray">{{ item.name }}</span>
										</div>
									</template>
								</div>
								<!-- <div class="upload-img">
                                    <el-upload class="upload" action="https://jsonplaceholder.typicode.com/posts/" drag multiple>
                                        <i class="el-icon-upload"></i>
                                        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                    </el-upload>
                                </div> -->
							</div>
							<div class="clear-input">
								<i class="el-icon-close" @click="clearInptValue"></i>
							</div>
						</div>
						<el-button type="primary" class="search-btn" @click="search"><i class="el-icon-search search-icon"></i></el-button>
					</div>
				</div>
				<!-- app下载 -->
				<div class="func-list">
					<!-- <el-tooltip content="APP" placement="bottom" effect="light" :visible-arrow="false">
						<div class="app-download">
							<div class="app-item">
								<i class="el-icon-download icon"></i>
							</div>
							<span style="margin-left: 5px; color: var(--colors4)">APP</span>
						</div>
					</el-tooltip>-->
					<div v-for="(item, index) in activeButton" :key="index">
						<el-tooltip :content="item.title" placement="bottom" effect="light" :visible-arrow="false">
							<div class="app-download">
								<div class="app-item">
									<span :key="index" class="icon" @click="goWeb(item)">{{ item.imgHead }}</span>
								</div>
								<span style="margin-left: 5px; color: var(--colors)">{{ item.title }}</span>
							</div>
						</el-tooltip>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
export default {
	name: 'QuestionSearch',
	props: {
		webInfo: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {
			inputValue: '',
			activeButton: [], // 动态按钮的信息
			recommendList: [
				{
					id: '1',
					active: true,
					name: '期末真题速提',
				},
				{
					id: '2',
					active: true,
					name: '计算能力培养',
				},
				{
					id: '3',
					active: true,
					name: '“双减”作业方案',
				},
				{
					id: '4',
					active: true,
					name: '二级能力高效解题',
				},
				{
					id: '5',
					active: false,
					name: '2023年高考指向',
				},
			],
		}
	},
	mounted() {
		this.getActionButtonList()
	},
	methods: {
		recommend() {
			console.log('跳转')
		},
		search() {
			if (this.inputValue != '') {
				this.$notify({
					title: '搜索中',
					message: 'hold',
					type: 'warning',
				})
			} else {
				this.$message.error('请输入关键字')
			}
		},
		clearInptValue() {
			this.inputValue = ''
		},
		getActionButtonList() {
			//得到动态按钮的信息
			this.$ut.api('question.index.actionButtonList').then((res) => {
				if (res.data) this.activeButton = res.data
			})
		},
		goWeb(item) {
			// 页面跳转
			this.$router.push(item.url)
		},
	},
}
</script>
<style lang="scss" scoped>
.wrapper-full {
	background: #fff;
	box-shadow: 0 2px 4px 0 rgb(0 0 0 / 10%);
}

.search-box {
	height: 118px;
	width: 1200px;
	min-width: 1200px;
	background-color: #fff;
	display: flex;
	justify-content: space-between;

	.logo-area {
		padding: 20px 0px;
		width: 260px;
		min-width: 260px;
		height: 118px;
		color: var(--colors);
		line-height: 118px;
		cursor: pointer;

		.logo-outer {
			width: 100%;
			height: 80px;
			position: relative;

			.logo {
				margin-top: 0px;
				position: absolute;
				top: 0px;
				left: 0px;
				width: 100%;
				height: 100%;
			}
		}
	}

	.search-list {
		width: 700px;
		min-width: 600px;
		display: flex;
		align-items: center;

		.search {
			border-radius: 6px;
			height: 44px;
			display: flex;
			align-items: center;
			margin: 0 auto;

			.search-input {
				width: 450px;
				padding: 0px 40px 0px 20px;
				height: 44px;
				border-radius: 6px 0px 0px 6px;
				border: 2px solid var(--colors);
			}

			.search-input::-webkit-input-placeholder {
				color: gray;
			}
			.search-input::-moz-placeholder {
				color: gray;
			}
			.search-input:-moz-placeholder {
				color: gray;
			}
			.search-input:-ms-input-placeholder {
				color: gray;
			}
			.clear-input {
				display: none;
				position: absolute;
				width: 30px;
				height: 44px;
				line-height: 44px;
				right: 0px;
				bottom: 0px;
				text-align: left;
				font-size: 18px;
				color: #bfbfbf;
			}

			.search-btn {
				border-radius: 0 6px 6px 0;
				height: 44px;
				width: 60px;

				.search-icon {
					font-size: 18px;
					font-weight: bold;
				}
			}
			.hover-input {
				position: relative;
			}
			.hover-input:hover .search-active {
				display: block;
			}
			.hover-input:hover .clear-input {
				display: block;
			}
			.hover-input:hover .search-input {
				border-radius: 6px 0px 0px 0px;
			}
			.search-active {
				display: none;
				position: absolute;
				left: 0px;
				top: 42px;
				border: 2px solid var(--colors);
				border-top: none;
				border-radius: 0px 0px 6px 6px;
				width: 450px;
				height: auto;
				z-index: 9999;
				background-color: #fff;
				padding-bottom: 20px;

				.recommend-title {
					padding: 12px 10px 4px;
					font-size: 16px;
					color: #999999;
				}

				.rec-title {
					display: flex;
					flex-wrap: wrap;

					.rec-list {
						display: flex;
						cursor: pointer;
						align-items: center;
						padding: 4px 10px;
						margin: 4px 10px;
						border-radius: 50px;
						background-color: #f8f8f8;
					}
				}
				.upload-img {
					text-align: center;
					padding-top: 20px;
				}
			}

			.search-active::before {
				position: absolute;
				content: '';
				height: 2px;
				width: 100%;
				background-color: #f7f7f7;
			}
		}
	}

	.func-list {
		flex: 1;
		min-width: 200px;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.app-download {
			display: flex;
			align-items: center;

			.app-item {
				width: 40px;
				height: 40px;
				border-radius: 50%;
				border: 2px solid var(--colors);
				position: relative;

				.icon {
					color: var(--colors);
					font-size: 28px;
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
				}

				.anim-icon {
					animation: pic ease 0s 1 normal none;
				}

				@keyframes pic {
					0% {
						transform: translateX(100px);
						opacity: 0;
					}

					100% {
						transform: translateX(0);
						opacity: 1;
					}
				}
			}
		}

		.app-download:hover {
			cursor: pointer;

			.app-item {
				width: 40px;
				height: 40px;
				border-radius: 50%;
				border: 2px solid var(--colors);
				position: relative;
				background-color: var(--colors);

				.icon {
					color: #fff;
					font-size: 28px;
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
				}
			}
		}
	}
}
</style>
