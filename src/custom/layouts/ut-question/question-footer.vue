<template>
	<footer class="question-footer">
		Copyright
		<ut-icon icon="copyright-line" />
		{{ fullYear }} {{ title }}
	</footer>
</template>

<script>
	import { title } from '@/config'

	export default {
		name: 'QuestionFooter',
		data() {
			return {
				fullYear: new Date().getFullYear(),
				title,
			}
		},
	}
</script>

<style lang="scss" scoped>
	.question-footer {
		position: relative;
		width:100%;
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 40px;
		padding: 0 $base-padding 0 $base-padding;
		color: rgba(0, 0, 0, 0.45);
		background: $base-color-white;
		border-top: 1px dashed $base-border-color;

		i {
			margin: 0 5px;
		}
	}
</style>
