<template>
	<div class="ut-question" :style="{ '--colors': colors, '--colors2': colors + 'D0', '--colors3': colors + '60', '--colors4': LightDarkenColor(colors, -30) }">
		<question-header />
		<question-search :web-info="webInfoList" />
		<question-nav />
		<div class="ut-question-body">
			<ut-router-view />
		</div>
		<question-footer />
		<el-backtop target="#app" />
		<!--  主题组件放到layouts下防止主题切换，导致主题组件重新加载 -->
		<ut-theme-drawer />
		<ut-theme-setting />
		<!-- <help-center-video :visible.sync="videoHelpVisible" :video-list="helpVideoList" /> -->
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
// import HelpCenterVideo from '@/views/question/components/help-center/video.vue'
export default {
	name: 'UtQuestion',
	components: { 
		// HelpCenterVideo 
	},
	data() {
		return {
			colors: '#1890ff',
			webInfoList: {}, //网页基本信息
			videoHelpVisible: false,
			// 帮助视频列表
			helpVideoList: [],
			// 旗下产品列表
			productList: [],
			// 站内导航列表
			siteNavList: [],
		}
	},
	computed: {
		...mapGetters({
			theme: 'settings/theme',
		}),
	},
	mounted() {
		this.getWebInfo()
		this.getHelpVideo()
		this.getNavList()
		this.colors = !this.theme.backgroundColor ? '#1890ff' : this.theme.backgroundColor
	},
	methods: {
		getWebInfo() {
			// 得到网站的基本信息
			this.$ut.api('question.index.webInfo').then((res) => {
				if (res.data) this.webInfoList = res.data
			})
		},
		// 获得视频帮助下的视频列表
		getHelpVideo() {
			this.$ut.api('question.help.video').then((res) => {
				this.helpVideoList = res.data
			})
		},
		// 获得首页顶部菜单导航
		getNavList() {
			// 站内导航菜单
			this.$ut.api('question.index.menu.navList').then((res) => {
				this.siteNavList = res.data
			})

			// 产品菜单列表
			this.$ut.api('question.index.menu.productList').then((res) => {
				this.productList = res.data
			})
		},
		getRgbNum(sColor) {
			if (sColor.length === 4) {
				let sColorNew = '#'
				for (let i = 1; i < 4; i += 1) {
					//补全颜色值 例如：#eee,#fff等
					sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))
				}
				sColor = sColorNew
			}
			//处理六位颜色值
			let sColorChange = []
			for (let i = 1; i < 7; i += 2) {
				//核心代码，通过parseInt将十六进制转为十进制，parseInt只有一个参数时是默认转为十进制的，第二个参数则是指定转为对应进制
				sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2)))
			}
			return sColorChange
		},
		colorRgba(str, n) {
			//十六进制颜色值的正则表达式
			const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
			const sColor = str.toLowerCase()
			n = n || 1
			//十六进制颜色转换为RGB格式
			if (sColor && reg.test(sColor)) {
				let sColorChange = this.getRgbNum(sColor)
				return 'rgba(' + sColorChange.join(',') + ',' + n + ')'
			} else {
				return sColor
			}
		},
		LightDarkenColor(color, num) {
			let colorArr = this.getRgbNum(color)
			let sColorChange = []
			for (var i = 0; i < colorArr.length; i++) {
				let val = colorArr[i] + num
				if (val < 0) {
					val = 0
				}
				if (val > 255) {
					val = 255
				}
				sColorChange.push(val)
			}
			return 'rgba(' + sColorChange.join(',') + ',1)'
		},
	},
}
</script>

<style lang="scss" scoped>
.ut-question {
	height: auto;
}
.ut-question-body {
	position: relative;
	min-height: calc(100% - 75px);
	width: 1200px;
	min-width: 1200px;
	margin: 0 auto;
}
</style>
