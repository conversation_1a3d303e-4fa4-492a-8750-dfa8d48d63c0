import Vue from 'vue'
import App from './App'
import i18n from '@/common/i18n'
import store from '@/common/store'
import router from '@/common/router'
import http from '@/common/request'
import '@/common/utils/directive'
import '@/ut'
import '@/custom'
import tools from '@/common/utils/tools.js'
import "default-passive-events"  //去除chrome烦人的element ui table passive提示
// /**
//  * @description 正式环境默认使用mock，正式项目记得注释后再打包
//  */
// import { BASE_URL, pwa } from '@/config'
// import { isExternal } from '@/common/utils/validate'


// if (process.env.NODE_ENV === 'production' && !isExternal(BASE_URL)) {
// 	const { mockXHR } = require('@/common/utils/static')
// 	mockXHR()
// }

// if (pwa) require('./registerServiceWorker')

Vue.prototype.$ut = http
Vue.prototype.$tools = tools

Vue.config.productionTip = false
new Vue({
	el: '#app',
	i18n,
	store,
	router,
	render: (h) => h(App),
})
