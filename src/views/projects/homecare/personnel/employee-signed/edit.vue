<template>
    <div v-loading="loading">
        <el-form ref="form" class="ut-form" :model="form" :rules="rules" label-width="120px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="选择员工：" prop="employeeId">
                        <el-input :value="form.name" :disabled="renew" placeholder="请选择员工" @focus="employeeFormVisible = true">
                            <template v-if="renew" #append>
                                <el-button icon="el-icon-search" @click="employeeFormVisible = true">选择</el-button>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="签约人：" prop="signedUserName">
                        <el-input v-model="form.signedUserName" placeholder="请输入签约人" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="签约开始日期：" prop="beginDate">
                        <el-date-picker v-model="form.beginDate" type="date" placeholder="选择开始日期" style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="签约结束日期：" prop="endDate">
                        <el-date-picker v-model="form.endDate" type="date" placeholder="选择结束日期" style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="签约地址：">
                        <el-input v-model="form.signedAddress" placeholder="请输入签约地址" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="备注：">
                        <el-input v-model="form.remark" placeholder="请输入备注" />
                    </el-form-item>
                </el-col>
            </el-row>
            <data-set v-if="!renew && form.datas?.length" :height="400" :datas="form.datas" @save="handleDataSave" />
        </el-form>

        <div class="ut-edit-footer">
            <el-button type="primary" @click="handleSave">保存</el-button>
            <el-button @click="handleCancel">取消</el-button>
        </div>

        <ut-modal v-model="employeeFormVisible" title="员工选择" width="800px">
            <employee-select select-window select-single :height="400" @select="handleSelectEmployee" />
        </ut-modal>
    </div>
</template>

<script>
import DataSet from './data-set'
import EmployeeSelect from './employee-select'
import { mapGetters } from 'vuex'

const emptyForm = () => ({
    id: '',
    employeeId: '',
    name: '',
    beginDate: '',
    endDate: '',
    signedUserName: '',
    signedAddress: '',
    remark: '',
    datas: [],
})

export default {
    name: 'EmployeeSignedEdit',
    components: {
        DataSet,
        EmployeeSelect,
    },
    props: {
        initData: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            loadingCount: 0,
            form: emptyForm(),
            rules: {
                employeeId: [{ required: true, message: '请选择员工', trigger: 'change' }],
                // signedUserName: [{ required: true, message: '请输入签约人名字', trigger: 'blur' }],
                beginDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
                endDate: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
            },
            employeeFormVisible: false,
            defaultDatas: [],
            materials: [],
        }
    },
    computed: {
        ...mapGetters({
            comm: 'comm/comm',
        }),
        loading() {
            return this.loadingCount > 0
        },
        renew() {
            return !this.initData.id && !!this.initData.employeeId
        },
    },
    watch: {
        initData: {
            async handler(val) {
                if (val?.id) {
                    await this.fetchData(val.id)
                } else if (val?.employeeId) {
                    await this.fetchDataByEmployee(val.employeeId, val.name)
                } else {
                    this.form = { ...emptyForm(), ...val, id: '' }
                }
            },
            immediate: true,
        },
    },
    methods: {
        async fetchData(id) {
            this.loadingCount += 1
            const { data } = await this.$ut
                .api('homecarepersonnel/employee/signed/info', {
                    communityId: this.comm.id,
                    id,
                })
                .finally(() => (this.loadingCount -= 1))
            this.form = { ...data }
        },
        async getDataByEmployee(employeeId) {
            if (!employeeId) return {}
            this.loadingCount += 1
            const { data } = await this.$ut
                .api('homecarepersonnel/employee/signed/infoByEmployee', {
                    communityId: this.comm.id,
                    employeeId,
                })
                .finally(() => (this.loadingCount -= 1))
            if (data) {
                delete data.id
                return Object.fromEntries(Object.entries(data).filter(([, v]) => !!v))
            }
            return {}
        },
        async fetchDataByEmployee(id, name) {
            this.form.datas = []
            this.loadingCount += 1
            const data = await this.getDataByEmployee(id).finally(() => (this.loadingCount -= 1))
            console.log(data)
            this.form = Object.assign(emptyForm(), data, {
                employeeId: id,
                name,
                datas: this.renew ? [] ?? (await this.getDefaultDatas()) : await this.getDefaultDatas(),
                beginDate: this.getNextDate(data.endDate),
                endDate: '',
            })
        },
        getNextDate(date) {
            if (!date) return ''
            const next = new Date(date)
            next.setDate(next.getDate() + 1)
            const year = next.getFullYear()
            const month = (next.getMonth() + 1).toString().padStart(2, '0')
            const day = next.getDate().toString().padStart(2, '0')
            return `${year}-${month}-${day}`
        },
        handleSelectEmployee(rows) {
            this.employeeFormVisible = false
            if (rows && rows[0]) {
                const { id, name } = rows[0]
                this.fetchDataByEmployee(id, name)
            }
        },
        async getDefaultDatas() {
            if (this.defaultDatas?.length) return this.defaultDatas
            this.loadingCount += 1
            const { data } = await this.$ut
                .api('homecarepersonnel/employee/signed/dataAllList', {
                    communityId: this.comm.id,
                })
                .finally(() => (this.loadingCount -= 1))
            this.defaultDatas = data.map((d) => ({
                ...d,
                details: d.details.map((sub) => ({ ...sub, files: sub.files ?? [] })),
            }))
            return this.defaultDatas
        },
        handleDataSave(data) {
            this.materials = data
        },
        async handleSave() {
            this.$refs.form.validate(async (valid) => {
                if (!valid) return
                if (new Date(this.form.endDate) <= new Date(this.form.beginDate)) {
                    this.$message.error('结束日期必须大于开始日期')
                    return
                }
                const formData = {
                    ...this.form,
                    communityId: this.comm.id,
                    datas: this.renew ? null : this.materials,
                }
                await this.$ut.api('homecarepersonnel/employee/signed/save', formData)
                this.$message.success('保存成功')
                this.$emit('save', formData)
            })
        },
        handleCancel() {
            this.$emit('close')
        },
    },
}
</script>

<style lang="scss" scoped></style>
