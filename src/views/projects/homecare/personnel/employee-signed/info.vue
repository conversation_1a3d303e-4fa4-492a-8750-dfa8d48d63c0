<template>
    <div v-loading="loading">
        <div class="ut-body">
            <el-descriptions border :column="2">
                <el-descriptions-item label="员工姓名">{{ form.name }}</el-descriptions-item>
                <el-descriptions-item label="签约人">{{ form.signedUserName }}</el-descriptions-item>
                <el-descriptions-item label="签约开始日期">{{ form.beginDate }}</el-descriptions-item>
                <el-descriptions-item label="签约结束日期">{{ form.endDate }}</el-descriptions-item>
                <el-descriptions-item label="签约地址" :span="2">{{ form.signedAddress }}</el-descriptions-item>
                <el-descriptions-item label="备注" :span="2">{{ form.remark }}</el-descriptions-item>
            </el-descriptions>
        </div>

        <div v-if="form.datas?.length" class="ut-body" style="margin-top: 20px">
            <div class="section-title">签约资料</div>
            <data-set :height="400" :datas="form.datas" :readonly="true" />
        </div>

        <div class="ut-edit-footer">
            <el-button @click="handleClose">关闭</el-button>
        </div>
    </div>
</template>

<script>
import DataSet from './data-set'
import { mapGetters } from 'vuex'

const emptyForm = () => ({
    id: '',
    employeeId: '',
    name: '',
    beginDate: '',
    endDate: '',
    signedUserName: '',
    signedAddress: '',
    remark: '',
    datas: [],
})

export default {
    name: 'EmployeeSignedInfo',
    components: {
        DataSet,
    },
    props: {
        initData: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            loading: false,
            form: emptyForm(),
            defaultDatas: [],
        }
    },
    computed: {
        ...mapGetters({
            comm: 'comm/comm',
        }),
    },
    async beforeMount() {
        await this.getInfo()
    },
    methods: {
        async getInfo() {
            if (!this.initData.id) return
            this.loading = true
            const { data } = await this.$ut
                .api('homecarepersonnel/employee/signed/info', {
                    communityId: this.comm.id,
                    id: this.initData.id,
                })
                .finally(() => {
                    this.loading = false
                })
            this.form = { ...emptyForm(), ...data }
        },
        handleClose() {
            this.$emit('close')
        },
    },
}
</script>

<style lang="scss" scoped>
.section-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
}

:deep(.el-descriptions__label) {
    font-weight: bold;
    width: 120px;
}

:deep(.el-descriptions__content) {
    word-break: break-all;
}
</style>
