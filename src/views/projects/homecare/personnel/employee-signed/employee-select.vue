<template>
    <div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
        <form-list
            :loading="listLoading"
            :columns="columns"
            :height="height"
            :op-width="100"
            :op-fixed="true"
            :data-list="dataList"
            :select-window="selectWindow"
            :select-single="selectSingle"
            :show-list="showList"
            table-name="employee-select"
            @select="handleSelect"
            @fetchData="fetchData"
            @fullscreen="onFullscreen"
            @selectRows="onSelectRows"
        >
            <template #button>
                <el-button v-if="selectWindow" icon="el-icon-check" type="primary" :disabled="!selectRows.length" @click="handleConfirmSelect">选择</el-button>
            </template>
            <template #op="{ row }">
                <el-button v-if="selectWindow" type="text" @click="handleSelectSingle(row)">选择</el-button>
            </template>
            <template #cell="{ row, item }">
                <div v-if="item.prop === 'sex'">
                    <span v-if="row.sex == 1">男</span>
                    <span v-if="row.sex == 2">女</span>
                </div>
                <span v-else>{{ row[item.prop] }}</span>
            </template>
        </form-list>
    </div>
</template>

<script>
import FormList from '@/views/components/form-list'
import { mapGetters } from 'vuex'
import Vue from 'vue'

export default {
    name: 'EmployeeSelect',
    components: {
        FormList,
    },
    props: {
        height: {
            type: Number,
            default: () => Vue.prototype.$baseTableHeight(1),
        },
        selectWindow: {
            type: Boolean,
            default: false,
        },
        selectSingle: {
            type: Boolean,
            default: false,
        },
        showList: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            listLoading: false,
            fullscreen: false,
            selectRows: [],
            page: {
                pageindex: 1,
                pagesize: 20,
                key: '',
            },
            columns: [
                {
                    label: '序',
                    align: 'center',
                    prop: 'index',
                    width: '50',
                    show: true,
                },
                {
                    label: '姓名',
                    align: 'left',
                    prop: 'name',
                    width: '120',
                    show: true,
                },
                {
                    label: '性别',
                    align: 'center',
                    prop: 'sex',
                    width: '60',
                    show: true,
                },
                {
                    label: '电话',
                    align: 'center',
                    prop: 'phone',
                    width: '120',
                    show: true,
                },
                {
                    label: '证件号码',
                    align: 'center',
                    prop: 'idcard',
                    minWidth: '180',
                    show: true,
                },
            ],
            dataList: {
                info: [],
                record: 0,
            },
        }
    },
    computed: {
        ...mapGetters({
            comm: 'comm/comm',
        }),
    },
    created() {
        this.fetchData()
    },
    methods: {
        async fetchData(pageReq) {
            if (pageReq) this.page = pageReq
            this.listLoading = true
            const { data } = await this.$ut
                .api('homecarepersonnel/employee/listpg', {
                    communityId: this.comm.id,
                    ...this.page,
                })
                .finally(() => {
                    this.listLoading = false
                })
            this.dataList = data
        },
        handleSelect(rows) {
            this.$emit('select', rows)
        },
        handleConfirmSelect() {
            if (this.selectRows.length > 0) {
                this.$emit('select', this.selectRows)
            }
        },
        handleSelectSingle(row) {
            this.$emit('select', [row])
        },
        onFullscreen(v) {
            this.fullscreen = v
        },
        onSelectRows(rows) {
            this.selectRows = rows
        },
    },
}
</script>
