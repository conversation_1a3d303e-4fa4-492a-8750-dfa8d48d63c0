<template>
    <div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
        <form-list
            :loading="listLoading"
            :columns="columns"
            :height="height"
            :op-width="160"
            :op-fixed="true"
            :data-list="dataList"
            :select-window="selectWindow"
            :select-single="selectSingle"
            :show-list="showList"
            table-name="employee-signed"
            @select="handleSelect"
            @fetchData="fetchData"
            @fullscreen="onFullscreen"
            @selectRows="onSelectRows"
        >
            <template #button>
                <el-button icon="el-icon-plus" type="primary" @click="handleAdd">添加</el-button>
                <!-- <el-button icon="el-icon-delete" type="danger" :disabled="!selectRows.length" @click="handleDelete($event)">删除</el-button> -->
            </template>
            <template #op="{ row }">
                <!-- <el-button type="text" @click="handleEdit(row)">编辑</el-button>-->
                <el-button type="text" @click="handleViewDetail(row)">详细</el-button>
                <el-button type="text" @click="handleRenew(row)">续约</el-button>
                <el-button type="text" @click="handleDelete(row)">删除</el-button>
            </template>
            <template #cell="{ row, item }">
                <div v-if="item.prop === 'sex'">
                    <span v-if="row.sex === 1">男</span>
                    <span v-else-if="row.sex === 2">女</span>
                    <span v-else>-</span>
                </div>
                <el-button v-else-if="item.prop == 'endDate' && expired(row[item.prop])" type="danger" size="small">
                    <span style="font-size: 14px">{{ row[item.prop] }}</span>
                </el-button>
                <span v-else>{{ row[item.prop] }}</span>
            </template>
        </form-list>

        <!-- 查看详细信息弹窗 -->
        <ut-modal v-model="dialogInfoVisible" title="签约详细信息" width="900px">
            <info :init-data="selectRow" @close="dialogInfoVisible = false" />
        </ut-modal>

        <!-- 添加/续约编辑弹窗 -->
        <ut-modal v-model="dialogFormVisible" :title="selectRow.id ? '编辑签约' : selectRow.employeeId ? '续约' : '添加签约'" width="900px">
            <edit :init-data="selectRow" @save="handleSave" @close="dialogFormVisible = false" />
        </ut-modal>
    </div>
</template>

<script>
import Edit from './edit'
import Info from './info'
import FormList from '@/views/components/form-list'
import { mapGetters } from 'vuex'
import Vue from 'vue'

export default {
    name: 'EmployeeSigned',
    components: {
        Edit,
        Info,
        FormList,
    },
    props: {
        height: {
            type: Number,
            default: () => Vue.prototype.$baseTableHeight(1),
        },
        selectWindow: {
            type: Boolean,
            default: false,
        },
        selectSingle: {
            type: Boolean,
            default: false,
        },
        showList: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
			now: new Date(),
            listLoading: false,
            fullscreen: false,
            columns: [
                {
                    label: '序',
                    align: 'center',
                    prop: 'index',
                    width: '50',
                    show: true,
                },
                {
                    label: '员工姓名',
                    align: 'left',
                    prop: 'name',
                    width: '120',
                    show: true,
                },
                {
                    label: '性别',
                    align: 'center',
                    prop: 'sex',
                    width: '60',
                    show: true,
                },
                {
                    label: '电话',
                    align: 'center',
                    prop: 'phone',
                    width: '120',
                    show: true,
                },
                {
                    label: '证件号码',
                    align: 'center',
                    prop: 'idcard',
                    minWidth: '180',
                    show: true,
                },
                {
                    label: '签约起日期',
                    align: 'center',
                    prop: 'beginDate',
                    width: '120',
                    show: true,
                },
                {
                    label: '签约止日期',
                    align: 'center',
                    prop: 'endDate',
                    width: '120',
                    show: true,
                },
                {
                    label: '签约人',
                    align: 'left',
                    prop: 'signedUserName',
                    width: '120',
                    show: true,
                },
                {
                    label: '签约时间',
                    align: 'center',
                    prop: 'createTime',
                    width: '160',
                    show: true,
                },
            ],
            dataList: {
                info: [],
                page: 0,
                record: 0,
            },
            page: {
                key: '',
                pageindex: 1,
                pagesize: 20,
                order: '',
            },
            selectRow: {},
            selectRows: [],
            dialogFormVisible: false,
            dialogInfoVisible: false,
        }
    },
    computed: {
        ...mapGetters({
            comm: 'comm/comm',
        }),
    },
    created() {
        this.fetchData()
    },
    methods: {
        async fetchData(pageReq) {
            if (pageReq) this.page = pageReq
            this.listLoading = true
            const { data } = await this.$ut
                .api('homecarepersonnel/employee/signed/listpg', {
                    communityId: this.comm.id,
                    ...this.page,
                })
                .finally(() => {
                    this.listLoading = false
                })
            this.dataList = data
        },
        handleSelect(rows) {
            this.$emit('select', rows)
        },
        onFullscreen(v) {
            this.fullscreen = v
        },
        onSelectRows(rows) {
            this.selectRows = rows
        },
        handleAdd() {
            this.selectRow = {}
            this.dialogFormVisible = true
        },
        handleEdit(row) {
            this.selectRow = row
            this.dialogFormVisible = true
        },
        handleViewDetail(row) {
            this.selectRow = row
            this.dialogInfoVisible = true
        },
        handleRenew(row) {
            this.selectRow = {
                ...row,
                id: '',
            }
            this.dialogFormVisible = true
        },
        handleSave() {
            this.dialogFormVisible = false
            this.fetchData()
        },
        handleDelete(row) {
            //let ids = []
            if (row.id) {
                //ids = [row.id]
            } else {
                // if (this.selectRows.length > 0) {
                //     ids = this.selectRows.map((item) => item.id)
                // } else {
                    this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
                    return
                //}
            }
            this.$baseConfirm('你确定要删除吗', null, async () => {
                await this.$ut.api('homecarepersonnel/employee/signed/delete', {
                    communityId: this.comm.id,
                    //ids: ids,
                    id:row.id
                })
                this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
                return this.fetchData()
            })
        },
		expired(date) {
			return new Date(date) <= this.now
		},
    },
}
</script>

<style lang="scss" scoped></style>
