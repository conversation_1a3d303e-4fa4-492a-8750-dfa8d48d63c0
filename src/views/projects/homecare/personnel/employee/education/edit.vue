<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="110px" :model="form" :rules="rules" class="ut-form">
			<el-row>
				<el-col :span="24">							
					<el-form-item label="标题：" prop="title">
						<el-input v-model.trim="form.title" placeholder="请输入标题" />
					</el-form-item>
				</el-col>
				
				<el-col :span="12">		
					<el-form-item label="时间起：" prop="beginDate">
						<el-date-picker v-model="form.beginDate" type="date" format="yyyy年MM月" value-format="yyyy-MM" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="时间止：" prop="endDate">
						<el-date-picker v-model="form.endDate" type="date" format="yyyy年MM月" value-format="yyyy-MM" />
					</el-form-item>
				</el-col>
				<el-col :span="24">							
					<el-form-item label="学校：" prop="school">
						<el-input v-model.trim="form.school" placeholder="请输入学校" />
					</el-form-item>
				</el-col>
				<el-col :span="12">							
					<el-form-item label="专业：" prop="major">
						<el-input v-model.trim="form.major" placeholder="" />
					</el-form-item>
				</el-col>
				<el-col :span="12">							
					<el-form-item label="学位：" prop="academicDegree">
						<el-input v-model.trim="form.academicDegree" placeholder="" />
					</el-form-item>
				</el-col>
				<el-col :span="24">		
					<el-form-item label="说明：" prop="remark">
						<el-input v-model.trim="form.remark" type="textarea" />
					</el-form-item>
				</el-col>
			</el-row>

		</el-form>


		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'

export default {
	components:{
	},
	// mixins: [UploadMixins],
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
		parentData: {
			type: Object,
			default: () => ({}),
		},
	},
	
	data() {
		return {
			loading: false,
			form: {
				id: '',
				title: '',
				beginDate: '',
				endDate: '',
				school:'',
				major:'',
				academicDegree:'',
				remark:'',
			},
			rules: {
				school: [{ required: true, trigger: 'blur', message: '请输入学校' }],
				beginDate: [{ required: true, trigger: 'blur', message: '请输入开始时间' }],
				endDate: [{ required: true, trigger: 'blur', message: '请输入结束时间' }],
			},

			typeFormVisible:false,
			levelFormVisible:false,
			sourceFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarepersonnel/employee/education/info', {			
				communityId:this.comm.id,
				employeeId:this.parentData.id,
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
		
		},
		handleSelectType(rows){
			this.typeFormVisible = false
			if (rows && rows[0]) {
				this.form.typeId = rows[0].id
				this.form.typeName = rows[0].name
			}
		},
		handleSelectLevel(rows){
			this.levelFormVisible = false
			if (rows && rows[0]) {
				this.form.levelId = rows[0].id
				this.form.levelName = rows[0].name
			}
		},
		handleSelectSource(rows){
			this.sourceFormVisible = false
			if (rows && rows[0]) {
				this.form.sourceId = rows[0].id
				this.form.sourceName = rows[0].name
			}
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
		
				await this.$ut.api('homecarepersonnel/employee/education/save', {
					communityId:this.comm.id,
					employeeId:this.parentData.id,
					...this.form,
				}).finally(()=>{this.loading = false})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
	},
}
</script>
<style lang="scss" scoped>

</style>
