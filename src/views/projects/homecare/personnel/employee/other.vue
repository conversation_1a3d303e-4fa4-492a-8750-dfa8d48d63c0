<template>
	<div v-loading="loading" class="ut-body">
        <div class="info-box">
            <el-row>
                <el-col :span="8">
                    <span>姓名：</span>
                    <span>{{ form.name }}</span>
                </el-col>
                <el-col :span="8">
                    <span>身份证：</span>
                    <span>{{ form.idcard }}</span>
                </el-col>
                <el-col :span="8">
                    <span>电话：</span>
                    <span>{{ form.phone }}</span>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <span>性别：</span>
                    <span v-if="form.sex==0">不详</span>
                    <span v-if="form.sex==1">男</span>
                    <span v-if="form.sex==2">女</span>
                </el-col>
                <el-col :span="8">
                    <span>出生日期：</span>
                    <span>{{ form.birthday }}</span>
                </el-col>
                <el-col :span="8">
                    <span>民族：</span>
                    <span>{{ form.nation }}</span>
                </el-col>
            </el-row>
        </div>
        <div>
            <el-tabs v-model="tabName" type="border-card" tab-position="left" style="height: 600px;">
                <el-tab-pane name="contacts">
                    <span slot="label">
                        <i class="el-icon-set-up"></i>
                        联系人
                    </span>
                    <contacts v-if="tabName=='contacts'" :parent-data="initData" :height="490" />
                </el-tab-pane>
                <el-tab-pane name="speciality">
                    <span slot="label">
                        <i class="el-icon-set-up"></i>
                        特长
                    </span>
                    <speciality v-if="tabName=='speciality'" :parent-data="initData" :height="490" />
                </el-tab-pane>
                <el-tab-pane name="education">
                    <span slot="label">
                        <i class="el-icon-set-up"></i>
                        教育经历
                    </span>
                    <education v-if="tabName=='education'" :parent-data="initData" :height="490" />
                </el-tab-pane>
                <el-tab-pane name="certificate">
                    <span slot="label">
                        <i class="el-icon-set-up"></i>
                        证书
                    </span>
                    <certificate v-if="tabName=='certificate'" :parent-data="initData" :height="490" />
                </el-tab-pane>
                <el-tab-pane name="work">
                    <span slot="label">
                        <i class="el-icon-set-up"></i>
                        工作经历
                    </span>
                    <work v-if="tabName=='work'" :parent-data="initData" :height="490" />
                </el-tab-pane>
                <el-tab-pane name="swipers">
                    <span slot="label">
                        <i class="el-icon-set-up"></i>
                        轮播图
                    </span>
                    <swipers v-if="tabName=='swipers'" :parent-data="initData" :height="490" />
                </el-tab-pane>
                <el-tab-pane name="content">
                    <span slot="label">
                        <i class="el-icon-set-up"></i>
                        详细
                    </span>
                    <content-control v-if="tabName=='content'" :parent-data="initData" :height="490" />
                </el-tab-pane>
            </el-tabs>
        </div>
		



	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'


import Contacts from './contacts'
import Speciality from './speciality'
import Education from './education'
import Certificate from './certificate'
import Work from './work'
import Swipers from './swipers'
import ContentControl from './content'

export default {
	components:{
        Contacts,
        Speciality,
        Education,
        Certificate,
        Work,
        Swipers,
        ContentControl,
	},
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
	},
	
	data() {
		return {
			loading: false,
            tabName:'contacts',
			form: {
				id: '',
				name: '',
				code: '',
				color: '',
				showIndex: '',
				valid:true,
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarepersonnel/employee/info', {
				communityId:this.comm.id,
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
		
		},
		// save() {
		// 	this.$refs['form'].validate(async (valid) => {
		// 		if (!valid) return
		// 		this.loading = true
		
		// 		await this.$ut.api('homecarelong/customer/save', {
		// 			communityId:this.comm.id,
		// 			module:'long',
		// 			...this.form,
		// 		}).finally(()=>{this.loading = false})
		// 		this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
		// 		this.$emit('fetchData')
		// 		this.$emit('close')	
		// 	})
		// },
	},
}
</script>
<style lang="scss" scoped>
.info-box{
    line-height: 30px;
    padding: 0 10px 10px 10px;
}


</style>
