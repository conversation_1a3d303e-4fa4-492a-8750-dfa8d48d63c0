<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-row>
				<el-col :span="18">
					<el-row>
						<el-col :span="12">							
							<el-form-item label="姓名：" prop="name">
								<el-input v-model.trim="form.name" placeholder="请输入名称" />
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="性别：" prop="sex">
								<el-select v-model="form.sex">
									<el-option label="不详" :value="0" />
									<el-option label="男" :value="1" />
									<el-option label="女" :value="2" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="12">		
							<el-form-item label="电话：" prop="phone">
								<el-input v-model.trim="form.phone" placeholder="请输入电话" />
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="证件号：" prop="idcard">
								<el-input v-model.trim="form.idcard" placeholder="请输入证件号" />
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="出生日期：" prop="birthday">
								<el-date-picker v-model="form.birthday" type="date" />
							</el-form-item>
						</el-col>
						<el-col :span="12">		
							<el-form-item label="民族：" prop="nation">
								<el-input v-model.trim="form.nation" />
							</el-form-item>
						</el-col>
						<el-col :span="12">		
							<el-form-item label="学历：" prop="education">
								<el-select v-model="form.education">
									<el-option label="请选择" value="" />
									<el-option label="小学" value="小学" />
									<el-option label="初中" value="初中" />
									<el-option label="高中" value="高中" />
									<el-option label="中专" value="中专" />
									<el-option label="大专" value="大专" />
									<el-option label="本科" value="本科" />
									<el-option label="硕士" value="硕士" />
									<el-option label="博士" value="博士" />
									<el-option label="其他" value="其他" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="12">		
							<el-form-item label="email：" prop="email">
								<el-input v-model.trim="form.email" />
							</el-form-item>
						</el-col>
						<el-col :span="24">		
							<el-form-item label="地址：" prop="address">
								<el-input v-model.trim="form.address" />
							</el-form-item>
						</el-col>
						<el-col :span="24">		
							<el-form-item label="备注：" prop="remark">
								<el-input v-model.trim="form.remark" type="textarea" />
							</el-form-item>
						</el-col>
					</el-row>
				</el-col>
				<el-col :span="6">
					<div class="face-body">
						<!-- <div class="face-top">
							<div class="face-ewm"><img class="upload-image" :src="uploadImage" alt="" /></div>
							<div class="ewm-tips">微信扫一扫，协同操作</div>
						</div> -->
						<div class="face-bottom">
							<ut-media :url-list="fileList" :width="172" :height="212" :show-title="false" @delete="imageDelete">
								<template v-if="!fileList.length" slot="add">
									<el-upload
										ref="upload" :action="uploadAction" :show-file-list="false" list-type="picture-card"
										:file-list="fileList" :on-success="onSuccess">
										<!-- <i slot="default" class="el-icon-plus"></i> -->
										<img class="img-body" :src="faceUrl" alt="" />
									</el-upload>
								</template>
							</ut-media>

							<div style="margin: 8px 0 0 0; display: flex">
								<el-button
									style="height: 28px" :disabled="!fileList.length" type="primary" size="mini"
									icon="el-icon-delete" @click="clearImage">删除
								</el-button>
							</div>

						</div>
					</div>
					
				</el-col>
	
			</el-row>
			
			
		</el-form>


		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'

import UploadMixins from '../../components/upload.mixins.js'
export default {
	components:{
	},
	mixins: [UploadMixins],
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
	},
	
	data() {
		return {
			loading: false,
			form: {
				id: '',
				name: '',
				sex:'',
				phone:'',
				idcard:'',
				address:'',
				imgHead:'',
				birthday:'',
				remark:'',
				email:'',
				education:'',
			},
			faceUrl: require('../../static/personface.jpg'),
			rules: {
				name: [{ required: true, trigger: 'blur', message: '请输入名称' }],
			},

		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarepersonnel/employee/info', {
				communityId:this.comm.id,
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
			if (data.imgHead) {
				this.fileList = [{url: data.imgHead}]
			}
		
		},
		clearImage() {
			this.fileList = []
		},
		
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
				await this.$ut.api('homecarepersonnel/employee/save', {
					communityId:this.comm.id,
					...this.form,
					imgHead:this.fileList.length?this.fileList[0].url:''
				}).finally(()=>{this.loading = false})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.color-box {
	display: inline-block;
	height: 32px;
	width: 150px;
	border: 1px solid #dcdfe6;
	padding-left: 20px;

	.color-box-text {
		color:#fff;
		mix-blend-mode: difference;
	}
}
.placeholder {
	height: 70px;
}

.face-body {
	display: flex;
	justify-content: flex-end;

	.face-top {
		padding-right: 10px;
		flex: 1;
	}

	.face-ewm {
		margin: 0 auto;
		text-align: center;
	}

	.ewm-tips {
		margin-top: 10px;
		font-size: 12px;
		text-align: center;
	}

	.face-bottom {
		text-align: center;

		.img-body {
			height: 100%;
			width: 100%;
		}

		.face-img {
			border: 1px solid #aaa;
			height: 220px;
			width: 90%;
			margin: 0 auto;
			padding: 10px 0;

			&:hover {
				cursor: pointer;
			}

		}


	}
}

el-upload-list__item-thumbnail {
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.disable {
	pointer-events: none;
	color: #e5e5e5;

	:deep(.el-input) {
		color: #e5e5e5;
	}

	:deep(.el-form-item__label) {
		color: #e5e5e5;
	}
}

:deep(.el-upload-list--picture-card) {

	li {
		width: 96px;
		height: 96px;
	}
}

:deep(.el-upload--picture-card) {
	width: 170px;
	height: 210px;
	display: flex;
	align-items: center;
}


:deep(.face-bottom) {
	.el-upload {
		border: 1px solid rgb(170, 170, 170);
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}
}
</style>
