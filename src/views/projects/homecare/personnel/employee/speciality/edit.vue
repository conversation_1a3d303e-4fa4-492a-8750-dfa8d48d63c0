<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-row>
				<el-col :span="24">							
					<el-form-item label="特长：" prop="title">
						<el-input v-model.trim="form.title" placeholder="请输入特长" />
					</el-form-item>
				</el-col>
				
			</el-row>

		</el-form>


		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'

// import UploadMixins from '../../../../components/upload.mixins.js'
export default {
	components:{
	},
	// mixins: [UploadMixins],
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
		parentData: {
			type: Object,
			default: () => ({}),
		},
	},
	
	data() {
		return {
			loading: false,
			form: {
				id: '',
				title: '',
			},
			faceUrl: require('../../../static/personface.jpg'),
			rules: {
				title: [{ required: true, trigger: 'blur', message: '请输入特长' }],
				// type: [{ required: true, trigger: 'blur', message: '请选择关系' }],
			},

			typeFormVisible:false,
			levelFormVisible:false,
			sourceFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarepersonnel/employee/speciality/info', {			
				communityId:this.comm.id,
				employeeId:this.parentData.id,
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
		
		},
		handleSelectType(rows){
			this.typeFormVisible = false
			if (rows && rows[0]) {
				this.form.typeId = rows[0].id
				this.form.typeName = rows[0].name
			}
		},
		handleSelectLevel(rows){
			this.levelFormVisible = false
			if (rows && rows[0]) {
				this.form.levelId = rows[0].id
				this.form.levelName = rows[0].name
			}
		},
		handleSelectSource(rows){
			this.sourceFormVisible = false
			if (rows && rows[0]) {
				this.form.sourceId = rows[0].id
				this.form.sourceName = rows[0].name
			}
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
		
				await this.$ut.api('homecarepersonnel/employee/speciality/save', {
					communityId:this.comm.id,
					employeeId:this.parentData.id,
					...this.form,
				}).finally(()=>{this.loading = false})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
	},
}
</script>
<style lang="scss" scoped>

</style>
