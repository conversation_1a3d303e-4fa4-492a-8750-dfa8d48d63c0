<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-row>
				<el-col :span="24">							
					<el-form-item label="证书：" prop="title">
						<el-input v-model.trim="form.title" placeholder="请输入名称" />
					</el-form-item>
				</el-col>
			
				<el-col :span="24">		
					<el-form-item label="备注：" prop="remark">
						<el-input v-model.trim="form.remark" type="textarea" />
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-form-item label="上传文件：">
					<ut-media :url-list="fileList" :width="96" :height="96" :class="{readonly}" @delete="imageDelete">
						<template slot="add">
							<el-upload
								ref="upload" :action="uploadAction" :disabled="readonly" :show-file-list="false"
								list-type="picture-card"
								:file-list="fileList" :on-success="onSuccess">
								<i slot="default" class="el-icon-plus"></i>
							</el-upload>
						</template>
					</ut-media>
				</el-form-item>
	
			</el-row>
			
			
		</el-form>


		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'

import UploadMixins from '../../../components/upload.mixins.js'
export default {
	components:{
	},
	mixins: [UploadMixins],
	props: {
		parentData: {
			type: Object,
			default: () => ({}),
		},
		initData: {
			type: Object,
			default: () => ({}),
		},
		readonly: {
			type: Boolean,
			default: false,
		},
	},
	
	data() {
		return {
			loading: false,
			form: {
				id: '',
				title: '',
				remark:'',
			},
			rules: {
				title: [{ required: true, trigger: 'blur', message: '请输入证书名称' }],
			},

		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarepersonnel/employee/certificate/info', {
				communityId:this.comm.id,
				employeeId:this.parentData.id,
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
			if (data) {
				this.fileList = data.list.map(o => ({
					id: o.id,
					name: o.name,
					url: o.url,
				}))
			}
		},
		clearImage() {
			this.fileList = []
		},
		
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
		
				await this.$ut.api('homecarepersonnel/employee/certificate/save', {
					communityId:this.comm.id,
					employeeId:this.parentData.id,
					...this.form,
					files: this.fileList.map(u=>({id:u.id,url:u.url})),
				}).finally(()=>{this.loading = false})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.preview-img {
	display: flex;
	align-items: center;
	justify-content: center;

	.img {
		max-width: 100%;
		max-height: 100%;
	}
}

.qr {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;

	.img {
		width: 100px;
	}

	.text {
		flex: 1;
		text-align: center;
	}

	.bold {
		font-weight: bold;
	}
}

.el-upload-list__item-thumbnail {
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.disable {
	pointer-events: none;
	color: #e5e5e5;

	:deep(.el-input) {
		color: #e5e5e5;
	}

	:deep(.el-form-item__label) {
		color: #e5e5e5;
	}
}

:deep(.el-upload-list--picture-card) {

	li {
		width: 96px;
		height: 96px;
	}
}

:deep(.el-upload--picture-card) {
	width: 96px;
	height: 96px;
	line-height: 96px;
}

.readonly {
	:deep(.el-upload--picture-card), :deep(.viewer-delete) {
		display: none !important;
	}
}
</style>
