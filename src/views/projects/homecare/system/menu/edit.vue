<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-row>
				<el-col v-if="opType=='add' || (opType=='edit' && treeNode.parent.id)" :span="24">
					<el-form-item label="上级菜单：">
						<span v-if="initData.id">
							{{ form.allTitle }}
						</span>
						<span v-else>
							{{treeNode.allTitle || treeNode.label}}
						</span>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="菜单标题" prop="title">
						<el-input v-model.trim="form.title" placeholder="请输入菜单标题" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="图标" prop="icon">
						<el-input v-model.trim="form.icon" placeholder="如：code-box-line"/>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="名称" prop="name">
						<el-input v-model.trim="form.name" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="路径" prop="path">
						<el-input v-model.trim="form.path" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="徽章" prop="badge">
						<el-input v-model.trim="form.badge" placeholder="如：Hot"/>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="显示顺序" prop="showIndex">
						<el-input v-model.trim="form.showIndex"/>
					</el-form-item>
				</el-col>
				<el-col>
					<el-form-item label="路由" prop="component">
						<el-input v-model.trim="form.component" placeholder="如：@/views/en/info/base/house-type.vue"/>
					</el-form-item>
				</el-col>
				<el-col>
					<el-form-item label="选项" prop="opt">
						<el-checkbox v-model="form.noClosable">noClosable</el-checkbox>
						<el-checkbox v-model="form.dot">dot</el-checkbox>
						<el-checkbox v-model="form.newTab">newTab</el-checkbox>
						<el-checkbox v-model="form.valid">valid</el-checkbox>
						<el-checkbox v-model="form.breadcrumbHidden">breadcrumbHidden</el-checkbox>
						<el-checkbox v-model="form.required">required</el-checkbox>
						<el-checkbox v-model="form.levelHidden">levelHidden</el-checkbox>
						<el-checkbox v-model="form.noColumn">noColumn</el-checkbox>
						<el-checkbox v-model="form.hidden">hidden</el-checkbox>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">确 定</el-button>
			<el-button @click="close">取 消</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
	name: 'PayTypeEdit',
	props: {
		treeNode:{
			type:Object,
			default:()=>({})
		},
		initData: {
			type: Object,
			default: () => ({}),
		},
		opType:{
			type:String,
			default:''
		}
	},
	emits: ['fetch-data', 'close'],
	data() {
		return {
			loading: false,
			form: {
				id: '',
				pId:'',
				title: '',
				name: '',
				code: '',
				valid:true,
			},
			rules: {
				title: [{ required: true, trigger: 'blur', message: '请输入菜单名称' }],
				name: [{ required: true, trigger: 'blur', message: '请输入名称' }],
				path: [{ required: true, trigger: 'blur', message: '请输入路径' }],
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
			commKey: 'comm/commKey',
		}),
	},
	mounted() {
		this.getInfo()
	},
	methods: {
		async getInfo() {
			if (!this.initData.id){
				this.form.pId=this.treeNode.id
				return
			} 
			this.loading = true
			try {
				const { data } = await this.$ut.api('homecaresystem/menu/router/info', {
					id: this.initData.id,
					commKey: this.commKey,
				})
				this.form = data
			} finally {
				this.loading = false
			}
		},
		close() {
			this.$emit('close')
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
				try {
					await this.$ut.api('homecaresystem/menu/router/save', {
						...this.form,
						commKey: this.commKey,
					})
					this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
					this.$emit('fetch-data')
					this.close()
				} finally {
					this.loading = false
				}
			})
		},
	},
}
</script>
<style lang="scss" scoped>
</style>
