<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-row>
				<el-col :span="24">
					<el-form-item label="路由：">
						<span v-if="initData.id">
							{{ form.routerAllTitle }}
						</span>
						<span v-else>
							{{treeNode.allTitle || treeNode.label}}
						</span>
					</el-form-item>
				</el-col>
				<el-col>
					<el-form-item label="API名称：" prop="name">
						<el-input v-model.trim="form.name" placeholder="请输入API名称" />
					</el-form-item>
				</el-col>
				<el-col>
					<el-form-item label="路径：" prop="path">
						<el-input v-model.trim="form.path" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">确 定</el-button>
			<el-button @click="close">取 消</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
	name: 'PayTypeEdit',
	props: {
		treeNode:{
			type:Object,
			default:()=>({})
		},
		initData: {
			type: Object,
			default: () => ({}),
		},
	},
	emits: ['fetch-data', 'close'],
	data() {
		return {
			loading: false,
			form: {
				id: '',
				name: '',
				code: '',
				valid: true,
				showIndex: 0,
			},
			rules: {
				name: [{ required: true, trigger: 'blur', message: '请输入API名称' }],
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
			commKey: 'comm/commKey',
		}),
	},
	mounted() {
		this.getInfo()
	},
	methods: {
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			try {
				const { data } = await this.$ut.api('homecaresystem/menu/api/info', {
					id: this.initData.id,
					commKey: this.commKey,
				})
				this.form = data
			} finally {
				this.loading = false
			}
		},
		close() {
			this.$emit('close')
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
				try {
					await this.$ut.api('homecaresystem/menu/api/save', {
						...this.form,
						commKey: this.commKey,
						routerId:this.treeNode.id,
					})
					this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
					this.$emit('fetch-data')
					this.close()
				} finally {
					this.loading = false
				}
			})
		},
	},
}
</script>
<style lang="scss" scoped>
</style>
