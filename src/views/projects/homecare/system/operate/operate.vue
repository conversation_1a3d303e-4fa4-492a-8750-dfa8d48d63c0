<template>
	<div>
		<form-tree-list :tree-width="250" :class="{ 'ut-fullscreen': fullscreen, 'table-container': true }">
			<template #tree>
				<role-tree v-loading="treeloading" :tree-data="treeData" @node-click="onClickNode" />
			</template>
			<template #list>
				<form-list
					:loading="listLoading"
					:columns="columns"
					:height="height"
					:op-width="100"
					:op-fixed="true"
					:data-list="dataList"
					:select-window="selectWindow"
					:select-single="selectSingle"
					:show-list="showList"
					table-name="redact"
					@select="handleSelect"
					@fetchData="fetchData"
					@fullscreen="onFullscreen"
					@selectRows="onSelectRows"
				>
					<template slot="button">
						<el-button icon="el-icon-plus" type="primary" :disabled="!isCheck"  @click="handleCommand('add')">增加</el-button>
						<el-button icon="el-icon-delete" type="danger" :disabled="!selectRows.length" style="margin-left: 10px" @click="handleDelete($event)">删除</el-button>
					</template>
					<template #op="{ row }">
						<div class="table-op">
							<el-button type="text" @click="handleCommand('edit',row)">编辑
							</el-button>
							<el-button type="text"  @click="handleCommand('delete',row)">删除
							</el-button>
						</div>
					</template>
					<template #cell="{ row, item }">
						<span v-if="item.prop == 'isSettlement'">
							<span v-if="row[item.prop]">已入住</span>
							<span v-if="!row[item.prop]"></span>
						</span>
						<span v-else-if="item.prop == 'roomTypeName'">
							<span class="special" :style="{ backgroundColor: row['roomTypeColor'] }">{{ row[item.prop] }}</span>
						</span>
						<el-button v-else-if="item.prop == 'calcValidTime' && expired(row[item.prop])" type="danger" size="small">
							<span style="font-size: 14px">{{ row[item.prop] }}</span>
						</el-button>
						<span v-else>{{ row[item.prop] }}</span>
					</template>
				</form-list>
			</template>
		</form-tree-list>
		<ut-modal v-model="editDialog.dialogFormVisible" :title="selectRows.id?'API编辑':'增加API'" width="400px">
			 <edit :init-data="selectRows" :tree-node="treeSetting" @fetch-data="fetchData" @close="editDialog.dialogFormVisible=false" />
		</ut-modal>
	</div>
</template>

<script>
import RoleTree from '../role/tree'
import FormList from '@/views/components/form-list'
import Edit from './edit.vue'
import FormTreeList from '@/views/components/form-tree-list'
import { mapGetters } from 'vuex'
import Vue from 'vue'

export default {
	name: 'Operate',
	components: {
		Edit,
		FormList,
		RoleTree,
		FormTreeList,
	},
	props: {
		readonly: {
			type: Boolean,
			default: false,
		},
		height: {
			type: Number,
			default: () => Vue.prototype.$baseTableHeight(1),
		},
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			now: new Date(),
			listLoading: false,
			fullscreen: false,
			treeHeight: this.$baseTableHeight(0.15),
			treeloading: true,
			treeData: [],
			editDialog: {
				dialogFormVisible: false,
			},
			treeSetting: {
				treeType: '',
				treeId: '',
			},
			columns: [
				{
					align: 'left',
					width: '200',
					label: '姓名',
					prop: 'name',
					sortable: false,
					show: true,
				},
				{
					align: 'left',
					width: '200',
					label: '联系电话',
					prop: 'phone',
					sortable: false,
					disableCheck: true,
					show: true,
				},
				{
					align: 'left',
					width: 'auto',
					label: '角色名称',
					prop: 'roleName',
					sortable: false,
					disableCheck: true,
					show: true,
				},
			],
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 20,
			},
			selectRows: {},
			isCheck: false, //是否是末节点
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
			commKey: 'comm/commKey',
		}),
	},
	watch: {
		treeSetting: {
			handler() {
				this.fetchData()
			},
			deep: true,
			immediate: true,
		},
	},
	beforeMount(){
		this.getTreeData()
	},
	created() {
		
	},
	methods: {
		// 得到所选单元树的节点信息
		onClickNode(node,nodeData) {
			this.treeSetting=node
			this.isCheck=false
			if(!nodeData.childNodes || !nodeData.childNodes.length) this.isCheck=true
		},
		// 得到左侧树列表
		getTreeData() {
			this.$ut.api('homecaresystem/role/operateTree', { commKey: this.commKey,baseId:this.comm.id }).then((res) => {
				this.treeData = res.data
				this.treeloading = false
			})
		},
		// 得到列表
		fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true
			let param = {
				commKey: this.commKey,
				baseId:this.comm.id,
				roleId: this.treeSetting.id&& this.treeSetting.id!=='op'?this.treeSetting.id:'',
				pagesize:this.page.pagesize,
				pageindex:this.page.pageindex,
				key:this.page.key,
			}
			this.$ut.api('homecaresystem/operate/listpg', param).then(async (res) => {
				this.dataList = res.data
				this.listLoading = false
			})
		},
		handleSelect(rows) {
			this.$emit('select', rows)
		},
		setSelectRows(val) {
			this.selectRows = val
		},
		onFullscreen(v) {
			this.fullscreen = v
		},
		onSelectRows(rows) {
			this.selectRows = rows
		},
		handleCommand(command, row) {
			switch (command) {
				case 'add':
					this.selectRows = {}
					this.editDialog.dialogFormVisible = true
					break
				case 'edit':
					this.selectRows = row
					this.editDialog.dialogFormVisible = true
					break
				case 'delete':
					this.handleDelete(row)
					break
			}
		},
		handleDelete(row) {
			let ids = []
			if (row.id) {
				ids = [row.id]
			} else {
				if (this.selectRows.length > 0) {
					ids = this.selectRows.map((item) => item.id)
				} else {
					this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
					return
				}
			}
			this.$baseConfirm('你确定要删除吗', null, async () => {
				this.$ut
					.api('homecaresystem/operate/delete', {
						ids: ids,
						commKey: this.commKey,
						baseId:this.comm.id
					})
					.then(() => {
						this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
						this.fetchData()
					})
			})
		},
		expired(date) {
			return new Date(date) <= this.now
		},
	},
}
</script>

<style lang="scss" scoped>
.special {
	padding: 10px 15px;
	border-radius: 12px;
}
</style>
