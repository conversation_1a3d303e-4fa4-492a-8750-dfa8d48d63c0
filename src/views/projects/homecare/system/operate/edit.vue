<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-form-item label="角色：" prop="roleName">
				<span v-if="initData.id">
					{{ form.roleName }}
				</span>
				<span v-else>
					{{treeNode.name || treeNode.label}}
				</span>
			</el-form-item>
      <el-form-item label="姓名：" prop="name">
        <el-input v-model.trim="form.name" placeholder="" />
      </el-form-item>
			<el-form-item label="电话：" prop="phone">
				<el-input v-model.trim="form.phone" placeholder="请输入操作员电话" />
			</el-form-item>
		</el-form>
		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">确 定</el-button>
			<el-button @click="close">取 消</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
	name: 'RoleUserEdit',
	props: {
		treeNode:{
			type:Object,
			default:()=>({})
		},
		initData: {
			type: Object,
			default: () => ({}),
		},
	},
	emits: ['fetch-data', 'close'],
	data() {
		return {
			loading: false,
			form: {
				id: '',
				name: '',
				valid: true,
				showIndex: '',
			},
			rules: {
        name: [{ required: true, trigger: 'blur', message: '请输入真实姓名' }],
				phone: [{ required: true, trigger: 'blur', message: '请输入电话号码' }],
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
			commKey: 'comm/commKey',
		}),
	},
	mounted() {
		this.form.roleId = this.treeNode.id
		this.getInfo()
	},
	methods: {
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			try {
				const { data } = await this.$ut.api('homecaresystem/operate/info', {
					id: this.initData.id,
					baseId: this.comm.id,
					commKey: this.commKey,
				})
				this.form = data
			} finally {
				this.loading = false
			}
		},
		close() {
			this.$emit('close')
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
				try {
					await this.$ut.api('homecaresystem/operate/save', {
						...this.form,
						baseId: this.comm.id,
						commKey: this.commKey,
					})
					this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
					this.$emit('fetch-data')
					this.close()
				} finally {
					this.loading = false
				}
			})
		},
	},
}
</script>
<style lang="scss" scoped>
</style>
