<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="100"
			:data-list="dataList"
			select-window
			select-single
			:show-list="showList"
			table-name="city-list"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template #cell="{ row, item }">
				<span>{{ row[item.prop] }}</span>
			</template>
		</form-list>
	</div>
</template>

<script>
import FormList from '@/views/components/form-list/index.vue'
import { mapGetters } from 'vuex'

export default {
	name: 'Type',
	components: {
		FormList,
	},
	props: {
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			listLoading: false,
			fullscreen: false,
			height: this.$baseTableHeight(1),
			columns: [
				{
					label: '省份',
					align: 'center',
					prop: 'provinceName',
					sortable: false,
					disableCheck: true,
					show: true,
				},
				{
					label: '省份代号',
					align: 'center',
					prop: 'provinceCode',
					sortable: false,
					disableCheck: true,
					show: true,
				},
				{
					label: '城市名',
					align: 'center',
					prop: 'name',
					sortable: false,
					disableCheck: true,
					show: true,
				},
				{
					label: '城市代号',
					align: 'center',
					prop: 'code',
					sortable: false,
					disableCheck: true,
					show: true,
				},
			],
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 20,
				order: '',
			},
			selectRows: '',
			dialogFormVisible: false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
			commKey: 'comm/commKey',
		}),
	},
	created() {
		this.fetchData()
	},
	methods: {
		fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true
			let param = {
				commKey: this.commKey,
				pageindex: this.page.pageindex,
				pagesize: this.page.pagesize,
				key: this.page.key,
			}
			this.$ut.api('ensystem/wechat/city/listpg', param).then(async (res) => {
				this.dataList = res.data
				this.listLoading = false
			})
		},
		handleSelect(rows) {
			this.$emit('select', rows)
		},
		setSelectRows(val) {
			this.selectRows = val
		},
		onFullscreen(v) {
			this.fullscreen = v
		},
		onSelectRows(rows) {
			this.selectRows = rows
		},
	},
}
</script>

<style lang="scss" scoped>
</style>
