<template>
	<div v-loading="loading" class="edit-container ut-body">
		<el-form ref="form" label-width="80px" :model="form" :rules="rules" class="ut-form">
			<el-form-item label="城市" prop="cityName">
				<el-input v-model="form.cityName" placeholder="选择城市" @focus="cityListDialog=true">
					<template slot="append">
						<i class="el-icon-search" @click="cityListDialog=true">选择</i>
					</template>
				</el-input>
			</el-form-item>
			<el-form-item label="社区名称" prop="communityName">
				<el-input v-model.trim="form.communityName" />
			</el-form-item>
			<el-form-item label="公司名称" prop="companyName">
				<el-input v-model.trim="form.companyName" />
			</el-form-item>
			<el-form-item label="电话" prop="tel">
				<el-input v-model.trim="form.tel" />
			</el-form-item>
			<el-row :gutter="10">
				<el-col :span="20">
					<el-form-item label="地址" prop="address">
						<el-input v-model.trim="form.address" />
					</el-form-item>
				</el-col>
				<el-col :span="4">
					<el-button @click="qqmapDialog=true">坐标拾取</el-button>
				</el-col>
				<el-col :span="12">
					<el-form-item label="经度" prop="address">
						<el-input v-model.trim="form.longitude" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="纬度" prop="address">
						<el-input v-model.trim="form.latitude" />
					</el-form-item>
				</el-col>
			</el-row>
			<el-form-item label="上传文件：">
				<ut-media :show-title="false" :url-list="fileList" :width="120" :height="120" @delete="imageDelete">
					<template v-if="fileList.length<1" slot="add">
						<el-upload
							ref="upload" :action="uploadAction" :show-file-list="false" list-type="picture-card"
							:file-list="fileList" :on-success="onSuccess">
							<i slot="default" class="el-icon-plus"></i>
						</el-upload>
					</template>
				</ut-media>
			</el-form-item>
			<el-form-item label="是否启用" prop="valid">
				<el-switch v-model.trim="form.valid" />
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="save">保 存</el-button>
			</el-form-item>
		</el-form>
		<el-dialog v-if="cityListDialog" v-dialog-drag append-to-body :close-on-click-modal="true" title="选择城市" top="7vh" :visible.sync="cityListDialog" width="800px">
			<city-list @select="handleSelectCity" @close="cityListDialog=false" />
		</el-dialog>
		<el-dialog v-if="qqmapDialog" v-dialog-drag append-to-body :close-on-click-modal="true" title="坐标拾取" top="3vh" :visible.sync="qqmapDialog" width="1000px">
			<div style="width:100%;height:800px">
				<qq-map :map-key="qqmap.mapKey" :key-name="qqmap.keyName" :lng="form.longitude" :lat="form.latitude" @callback="callback" @close="qqmapDialog = false" />
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import QqMap from '@/views/components/qq-map/index.vue'
import CityList from './city-list.vue'
import UploadMixins from '../../../components/upload.mixins.js'
import UtMedia from '@/ut/components/ut-media/ut-media.vue'

export default {
	name: 'WechatLink',
	components: {
		CityList,
		QqMap,
		UtMedia,
	},
	mixins: [UploadMixins],
	props: {},
	data() {
		return {
			loading: false,
			cityListDialog: false,
			qqmapDialog: false,
			qqmap: {
				mapKey: '',
				keyName: '',
			},
			form: {
				id: '',
				cityId: '',
				cityName: '',
				communityId: '',
				communityName: '',
				companyName: '',
				tel: '',
				address: '',
				longitude: '',
				latitude: '',
				valid: '',
			},
			rules: {
				cityName: [{ required: true, trigger: 'change', message: '请选择城市' }],
				communityName: [{ required: true, trigger: 'blur', message: '社区名称' }],
				companyName: [{ required: true, trigger: 'blur', message: '公司名称' }],
				tel: [{ required: true, trigger: 'blur', message: '电话' }],
				// code: [{ required: true, trigger: 'blur', message: '请输入作者' }],
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	created() {},
	beforeMount() {
		this.loadQQMap().then(() => {
			this.loadInfo()
		})
	},
	methods: {
		async loadQQMap() {
			const { data } = await this.$ut.api('ensystem/wechat/qqmap', {
				commKey: this.commKey,
			})
			this.qqmap = data
		},
		async loadInfo() {
			this.loading = true
			try {
				let data = await this.getMobileInfo()
				if (!data) {
					data = await this.getInfo()
					data.id = ''
					data.communityName = data.name
					data.companyName = data.company
				}
				this.form = {
					...this.form,
					...data,
				}
				if (data.imgHead) this.fileList = [{ url: data.imgHead }]
			} finally {
				this.loading = false
			}
		},
		async getMobileInfo() {
			if (!this.comm.id) return null
			this.loading = true
			const { data } = await this.$ut.api('ensystem/wechat/community/infoByCommunityId', {
				commKey: this.commKey,
				communityId: this.comm.id,
			})
			return data
		},
		async getInfo() {
			if (!this.comm.id) return null
			const { data } = await this.$ut.api('enplatform/community/info', {
				id: this.comm.id,
			})
			return data
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				const params = {
					...this.form,
					imgHead: this.fileList[0]?.url || '',
					commKey: this.commKey,
					communityId: this.comm.id,
				}
				this.loading = true
				await this.$ut.api('ensystem/wechat/community/save', params).finally(() => this.loading = false)
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				return this.loadInfo()
			})
		},
		handleSelectCity([row]) {
			this.form.cityName = row.name
			this.form.cityId = row.id
			this.cityListDialog = false
		},
		callback(loc) {
			this.form.latitude = loc.latlng.lat
			this.form.longitude = loc.latlng.lng
			this.form.address = loc.poiaddress
		},
	},
}
</script>
<style lang="scss" scoped>
:deep(.el-upload--picture-card) {
	width: 120px;
	height: 120px;
	line-height: 120px;
}
</style>
