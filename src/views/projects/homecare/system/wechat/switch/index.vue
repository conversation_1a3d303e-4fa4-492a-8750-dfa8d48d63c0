<template>
	<div v-loading="loading" class="edit-container ut-body">
		<el-form ref="form" label-width="100px" :model="form" :rules="rules" class="ut-form">
			<el-row>
				<el-col :span="12">
					<el-form-item label="门禁开门" prop="openDoor">
						<el-switch v-model="form.openDoor" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="费用查询" prop="feeSearch">
						<el-switch v-model="form.feeSearch" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="物业缴费" prop="chargeWYF">
						<el-switch v-model="form.chargeWYF" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="三表缴费" prop="chargeMeter">
						<el-switch v-model="form.chargeMeter" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="装修缴费" prop="chargeDec">
						<el-switch v-model="form.chargeDec" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="车辆缴费" prop="chargeCar">
						<el-switch v-model="form.chargeCar" />
					</el-form-item>
				</el-col>
				<el-col :span="12">

					<el-form-item label="车位缴费" prop="chargePlace">
						<el-switch v-model="form.chargePlace" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="非机动车缴费" prop="chargeBike">
						<el-switch v-model="form.chargeBike" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="账单缴费" prop="chargeBill">
						<el-switch v-model="form.chargeBill" />
					</el-form-item>
				</el-col>
			</el-row>
			<el-form-item>
				<el-button type="primary" @click="save">保 存</el-button>
			</el-form-item>
		</el-form>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
	name:'WechatSwitch',
	props: {},
	data() {
		return {
			loading: false,
			form: {
				openDoor: false,
				feeSearch: false,
				chargeWYF: false,
				chargeMeter: false,
				chargeDec: false,
				chargeCar: false,
				chargePlace: false,
				chargeBike: false,
				chargeBill: false,
			},
			rules: {},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
			commKey: 'comm/commKey',
		}),
	},
	beforeMount() {
		this.fetchData()
	},
	methods: {
		async fetchData() {
			this.loading = true
			try {
				const { data } = await this.$ut.api('ensystem/wechat/community/setInfoByCommunityId', {
					commKey: this.commKey,
					communityId: this.comm.id,
				})
				this.form = {
					...this.form,
					...data,
				}
			} finally {
				this.loading = false
			}
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				const params = {
					...this.form,
					commKey: this.commKey,
					communityId: this.comm.id,
				}
				this.loading = true
				await this.$ut.api('ensystem/wechat/community/set', params).finally(() => this.loading = false)
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				return this.fetchData()
			})
		},
	},
}
</script>

<style scoped lang="scss">

</style>
