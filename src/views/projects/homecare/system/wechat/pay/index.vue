<template>
	<div v-loading="loading" class="edit-container ut-body">
		<el-form ref="form" label-width="160px" :model="form" :rules="rules" class="ut-form">
			<el-col :span="12">
				<el-form-item label="微信支付商户号" prop="wechatMerchant">
					<el-input v-model.trim="form.wechatMerchant" />
				</el-form-item>
			</el-col>
			<el-col :span="24">
			<el-form-item>
				<el-button type="primary" @click="save">保 存</el-button>
			</el-form-item>
		</el-col>
		</el-form>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
	name:'Payset',
	components: {
	},
	props: {},
	data() {
		return {
			loading: false,
			form:{
                wechatMerchant:'',
            },
			rules:{
				wechatMerchant: [{ required: true, message: '请输入微信支付商户号', trigger: 'blur' }],
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
			commKey: 'comm/commKey',
		}),
	},
	created() {},
	beforeMount() {
        this.getInfo()
	},
	methods: {
        async getInfo(){
            this.loading = true
            const {data} = await this.$ut.api('ensystem/wechat/community/payInfo',{
				commKey:this.commKey,
                communityId:this.comm.id
            }).finally(()=>{this.loading=false})
            this.form = data
        },
		save(){
			this.$refs['form'].validate((valid) => {
				if(valid){
					this.$ut.api('ensystem/wechat/community/paySave',{
						commKey:this.commKey,
						communityId:this.comm.id,
						...this.form,
					})
				}
				
			})
        },
	},
}
</script>
<style lang="scss" scoped>
:deep(.el-upload--picture-card) {
	width: 120px;
	height: 120px;
	line-height: 120px;
}
</style>
