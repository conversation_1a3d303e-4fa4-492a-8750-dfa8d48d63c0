<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-form-item label="姓名：" prop="name">
				<el-input v-model.trim="form.name" />
			</el-form-item>
			<el-form-item label="联系电话：" prop="phone">
				<el-input v-model.trim="form.phone" placeholder="请输入联系电话" />
			</el-form-item>
			<el-form-item label="是否启用：" prop="valid">
				<el-switch v-model="form.valid" />
			</el-form-item>
		</el-form>
		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">确 定</el-button>
			<el-button @click="close">取 消</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
	name: 'PayTypeEdit',
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
	},
	emits: ['fetch-data', 'close'],
	data() {
		return {
			loading: false,
			form: {
				id: '',
				name: '',
				valid: true,
				showIndex: '',
			},
			rules: {
				phone: [{ required: true, trigger: 'blur', message: '请输入联系电话' }],
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
			commKey: 'comm/commKey',
		}),
	},
	mounted() {
		this.getInfo()
	},
	methods: {
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			try {
				const { data } = await this.$ut.api('homecaresystem/manager/info', {
					id: this.initData.id,
					baseId: this.comm.id,
					commKey: this.commKey,
				})
				this.form = data
			} finally {
				this.loading = false
			}
		},
		close() {
			this.$emit('close')
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
				try {
					await this.$ut.api('homecaresystem/manager/save', {
						...this.form,
						baseId: this.comm.id,
						commKey: this.commKey,
					})
					this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
					this.$emit('fetch-data')
					this.close()
				} finally {
					this.loading = false
				}
			})
		},
	},
}
</script>
<style lang="scss" scoped>
</style>
