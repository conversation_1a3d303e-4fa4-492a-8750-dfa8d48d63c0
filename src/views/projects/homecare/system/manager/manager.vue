<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="200"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			table-name="huxing"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button">
				<el-button icon="el-icon-plus" type="primary" @click="handleAdd">添加</el-button>
				<el-button icon="el-icon-delete" type="danger" :disabled="!selectRows.length" @click="handleDelete($event)">
					删除
				</el-button>
			</template>
			<template #op="{ row }">
				<el-button type="text" @click="handleEdit(row)">编辑</el-button>
				<el-button type="text" @click="handleDelete(row)">删除</el-button>
			</template>
			<template #cell="{ row, item }">
				<span v-if="item.prop == 'valid'">
						<span v-if="row[item.prop] == '1'" style="color: rgb(4, 205, 4); font-size: 18px"><i
							class="el-icon-success"></i></span>
						<span v-if="row[item.prop] == '0'" style="color: red; font-size: 18px"><i class="el-icon-error"></i></span>
				</span>
				<span v-else>{{ row[item.prop] }}</span>
			</template>
		</form-list>
		<ut-modal v-model="dialogFormVisible" :title="selectRows.id?'管理员编辑':'增加管理员'" width="500px">
			<edit :init-data="selectRows" flag="type" @fetch-data="fetchData" @close="dialogFormVisible=false" />
		</ut-modal>
	</div>
</template>

<script>
import Edit from './edit'
import FormList from '@/views/components/form-list'
import { mapGetters } from 'vuex'

export default {
	name: 'Manager',
	components: {
		Edit,
		FormList,
	},
	props: {
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			listLoading: false,
			fullscreen: false,
			height: this.$baseTableHeight(1),
			columns: [
				{
					label: '联系电话',
					align: 'left',
					width: '180',
					prop: 'phone',
					sortable: false,
					disableCheck: true,
					show: true,
				},
				{
					label: '姓名',
					align: 'left',
					width: 'auto',
					prop: 'name',
					sortable: false,
					disableCheck: true,
					show: true,
				},
				{
					align: 'center',
					label: '是否启用',
					width: '100',
					prop: 'valid',
					sortable: false,
					show: true,
				},
			],
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 20,
				order: '',
			},
			selectRows: '',
			dialogFormVisible: false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
			commKey: 'comm/commKey',
		}),
	},
	created() {
		this.fetchData()
	},
	methods: {
		async fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true
			try {
				const { data } = await this.$ut.api('homecaresystem/manager/listpg', {
					commKey: this.commKey,
					baseId:this.comm.id,
					pageindex: this.page.pageindex,
					pagesize: this.page.pagesize,
					key: this.page.key,
				})
				this.dataList = data
			} finally {
				this.listLoading = false
			}
		},
		handleSelect(rows) {
			this.$emit('select', rows)
		},
		setSelectRows(val) {
			this.selectRows = val
		},
		onFullscreen(v) {
			this.fullscreen = v
		},
		onSelectRows(rows) {
			this.selectRows = rows
		},
		handleAdd() {
			this.selectRows = {}
			this.dialogFormVisible = true
		},
		handleEdit(row) {
			this.selectRows = row
			this.dialogFormVisible = true
		},
		handleDelete(row) {
			let ids = []
			if (row.id) {
				ids = [row.id]
			} else {
				if (this.selectRows.length > 0) {
					ids = this.selectRows.map((item) => item.id)
				} else {
					this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
					return
				}
			}
			this.$baseConfirm('你确定要删除吗', null, async () => {
				this.$ut.api('homecaresystem/manager/delete', {
						ids: ids,
						baseId: this.comm.id,
						commKey:this.commKey,
					})
					.then(() => {
						this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
						this.fetchData()
					})
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.color-box {
	display: flex;
	align-items: center;
	justify-content: center;

	.color-preview {
		width: 30px;
		height: 30px;
		margin-right: 10px;
	}
}

</style>
