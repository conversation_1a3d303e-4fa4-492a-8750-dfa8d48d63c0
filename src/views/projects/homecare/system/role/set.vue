<template>
	<div v-loading="loading">
		<el-row :gutter="20">
			<el-col :span="24">
				<el-table border class="table-role" :data="tableData" default-expand-all row-key="path" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" :height="tableHeight">
					<el-table-column align="left" label="权限名称" prop="meta.title" show-overflow-tooltip width="250" >
						<template #default="{ row }">
							<span v-if="row.children">{{row.meta?row.meta.title:''}}</span>
							<div v-else style="display:inline-block">
								<el-checkbox v-if="rowSubLength(row)" :indeterminate="rowCheckState2(row)" :value="rowCheckState(row)" @change="checked=>checkRow(checked,row)">{{row.meta?row.meta.title:''}}</el-checkbox>
								<span v-else>{{row.meta?row.meta.title:''}}</span>
							</div>
							
						</template> 
					</el-table-column>
					<el-table-column align="left" label="API 功能" show-overflow-tooltip>
						<template #default="{ row }">
							<el-checkbox v-for="(item,index) in findApi(row.id)" :key="index" v-model="item.isCheck">{{item.name}}</el-checkbox>
						</template> 
					</el-table-column>
				</el-table>
			</el-col>
		</el-row>
		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">确 定</el-button>
			<el-button @click="close">取 消</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { filterRoutes } from '@/common/utils/routes'

export default {
	name: 'PayTypeEdit',
	props: {
		routerTree: {
			type: Array,
			default: () => ([]),
		},
		initData: {
			type: Object,
			default: () => ({}),
		},
		tableHeight:{
			type:Number,
			default:500
		},
	},
	emits: ['fetch-data', 'close'],
	data() {
		return {
			loading: false,
			roleId:'',
			roleApiList:[],
			tableData:[],
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
			commKey: 'comm/commKey',
		}),
	},
	mounted() {
		this.switchData()
		this.getInfo()
	},
	methods: {
		switchData(){
			if(!this.routerTree) return
			this.tableData = filterRoutes([...this.routerTree])
		},
		checkRow(isCheck,item){
			if(!this.roleApiList) return
			const idApis=this.roleApiList.filter(u=>u.routerId==item.id)
			idApis.forEach(item=>{
				this.$set(item,'isCheck',isCheck)
			})

		},
		rowSubLength(item){
			const apis = this.roleApiList.filter(u=>u.routerId==item.id)
			return apis.length
		},
		rowCheckState(item){
			const idApis=this.roleApiList.filter(u=>u.routerId==item.id && !u.isCheck)
			if(idApis.length) return false
			return true
		},
		rowCheckState2(item){
			const sourceApis=this.roleApiList.filter(u=>u.routerId==item.id)
			const idApis=this.roleApiList.filter(u=>u.routerId==item.id && u.isCheck)
			if(!idApis.length) return false
			if(sourceApis.length == idApis.length ) return null
			return true
		},
		async getInfo() {
			if (!this.initData.id) return
			this.roleId=this.initData.id
			this.loading = true
			try {
				const { data } = await this.$ut.api('homecaresystem/role/api/routerApiList', {
					roleId: this.initData.id,
					baseId: this.comm.id,
					commKey: this.commKey,
				})
				this.roleApiList = data
			} finally {
				this.loading = false
			}
		},
		findApi(id){
			if(!this.roleApiList) return
			let findArr = this.roleApiList.filter(f=>f.routerId==id);
			// this.roleApiList.forEach(roleAPi=>{
			// 	if(roleAPi.routerId==id) findArr.push(roleAPi)
			// })
		    //let res = this.roleApiList.findAll(f=>f.routerId==id)
			return findArr
		},
		close() {
			this.$emit('close')
		},
		save() {
			this.loading = true
			let checkList = this.roleApiList.filter(f=>f.isCheck)
			// this.roleApiList.forEach(roleApi=>{
			// 	if(roleApi.isCheck) checkList.push(roleApi)
			// })
			let ids=[]
			if(checkList) ids = checkList.map((item) => item.id)
			try {
					this.$ut.api('homecaresystem/role/api/save', {
					roleId:this.roleId,
					baseId: this.comm.id,
					commKey: this.commKey,
					apiIds:ids,
				})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetch-data')
				this.close()
			} finally {
				this.loading = false
			}
		},
	},
}
</script>
<style lang="scss" scoped>

:deep(.el-table .cell.el-tooltip ){
white-space:normal !important;
}

</style>
