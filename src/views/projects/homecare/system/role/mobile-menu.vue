<template>
	<div v-loading="loading">
		<el-row :gutter="20">
			<el-col :span="24">
				<el-table border class="table-role" :data="tableData" default-expand-all row-key="id" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" :height="tableHeight">
					<el-table-column align="left" label="权限名称" prop="meta.title" show-overflow-tooltip width="250" >
						<template #default="{ row }">
							<span >{{row.title}}</span>
							
						</template> 
					</el-table-column>
					<el-table-column align="left" label="功能启用" show-overflow-tooltip>
						<template #default="{ row }">
							<el-checkbox v-model="row.isCheck">启用</el-checkbox>
							<!-- <el-checkbox v-for="(item,index) in findApi(row.id)" :key="index" v-model="item.isCheck">{{item.name}}</el-checkbox> -->
						</template> 
					</el-table-column>
				</el-table>
			</el-col>
		</el-row>
		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">确 定</el-button>
			<el-button @click="$emit('close')">取 消</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
	props: {
		parentData: {
			type: Object,
			default: () => ({}),
		},
		tableHeight:{
			type:Number,
			default:500
		},
	},
	emits: ['fetchData', 'close'],
	data() {
		return {
			loading: false,
			roleId:'',
			roleApiList:[],
			tableData:[],
			routerTree:[],
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
			commKey: 'comm/commKey',
		}),
	},
	async mounted() {
		await this.getTree()
		await this.getRoleRouterList()
		// this.switchData()
		// this.getInfo()
	},
	methods: {
		async getTree(){
			const { data } = await this.$ut.api('homecaresystem/role/mobile/tree', {
				commKey: this.commKey,
			})
			this.tableData = data
			// this.tableData = filterRoutes([...this.routerTree])
		},
		async getRoleRouterList(){
			const { data } = await this.$ut.api('homecaresystem/role/mobile/listpg', {
				commKey: this.commKey,
				baseId: this.comm.id,
				roleId: this.parentData.id,
			})

			data.forEach(item=>{
				let obj=this.tableData.find(u=>u.id==item.mobileRouterId)
				if(obj){
					this.$set(obj,'isCheck',true)
				}
			})
		},
		
		save() {
			this.loading = true
			let checkList = this.tableData.filter(f=>f.isCheck)
			let ids=[]
			if(checkList) ids = checkList.map((item) => item.id)
			this.$ut.api('homecaresystem/role/mobile/save', {
				roleId:this.parentData.id,
				baseId: this.comm.id,
				commKey: this.commKey,
				routerMobileIds:ids,
			}).then(()=>{
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')
			}).finally(()=>{this.loading=false})

		},
	},
}
</script>
<style lang="scss" scoped>

:deep(.el-table .cell.el-tooltip ){
white-space:normal !important;
}

</style>
