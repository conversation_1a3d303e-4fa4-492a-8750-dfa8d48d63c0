<template>
	<div class="ut-fullscreen'">
		<div v-if="showTool" style="margin-bottom:10px">
            <el-button type="primary" :disabled="!treeSelectNode.id" @click="treeAdd">增加</el-button>
            <el-button type="normal" :disabled="!treeSelectNode.id" @click="treeEdit">修改</el-button>
            <el-button type="danger" :disabled="!treeSelectNode.id" @click="treeDelete">删除</el-button>
		</div>
		<el-tree
			ref="tree"
			value="currentNodeKey"
			class="tree"
			:expand-on-click-node="false"
			node-key="id"
			:default-expanded-keys="treeExpandedKeys"
			:data="menu"
			:props="defaultProps"
			:default-expand-all="openNode"
			:check-on-click-node="true"
			@node-click="treeNodeClick"
		>
			<span slot-scope="{ data }" class="custom-tree-node" :class="{ 'curr-node': treeSelectNode.id && treeSelectNode.id == data.id && treeSelectNode.pId == data.pId }">
				
				<span :ref="data.id" style="margin-left: 6px">{{ data.name }}</span>
			</span>
		</el-tree>
	</div>
</template>

<script>
export default {
	props: {
		treeData: {
			type: Array,
			default: () => [],
		},
		showTool:{
			type:Boolean,
			default:false,
		},
		openNode: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			list: [],
			filterChecked: false,
			treeExpandedKeys: [],
			defaultProps: {
				children: 'children',
				label: 'label',
			},
			treeSelectNode: {},
		}
	},
	computed: {
		menu: function () {
			if (this.list && this.list.length > 0) {
				return this.arrToTree(this.list, '')
			} else {
				return []
			}
		},
	},
	watch: {
		treeData: {
			handler(v) {
				this.list = v
				if (this.list && this.list.length > 0) {
					this.list.forEach((item) => {
						this.treeExpandedKeys.push(item.id)
					})
				}
			},
			immediate: true,
		},
	},
	methods: {
		operationType(data) {
			this.$emit('get-type', data)
		},
		treeAdd() {
            this.type='add'
            this.dialogFormVisible=true
		},
		treeEdit() {
			this.type='edit'
            this.dialogFormVisible=true
		},
		treeDelete() {

			// this.$baseConfirm('你确定要删除吗', null, async () => {
			// 	this.$ut.api('encar/base/area/delete', {
			// 			ids: [this.treeSelectNode.id],
			// 			communityId: this.comm.id,
			// 		})
			// 		.then(() => {
			// 			this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
			// 			const treeDataIndex = this.treeData.findIndex((u) => u.id === this.treeSelectNode.id)
			// 			this.treeData.splice(treeDataIndex, 1)
			// 			this.treeSelectNode = {}
			// 		})
			// })
		},
		arrToTree(arr, pId = '') {
			const res = []
			arr.forEach((item) => {
				if (item.pId == pId) {
					const children = this.arrToTree(
						arr.filter((v) => v.pId !== pId),
						item.id
					)
					if (children.length) {
						res.push({ ...item, children })
					} else {
						res.push({ ...item })
					}
				}
			})
			return res
		},
		treeNodeClick(node, nodeData) {
			this.treeSelectNode = node
			this.treeSelectNode.level = nodeData.level
			this.treeSelectNode.parent = {
				id: nodeData.parent.data.id,
				label: nodeData.parent.data.name,
			}
			this.$emit('node-click', node,nodeData)
		},
	},
}
</script>

<style lang="scss" scoped>
.btn-body {
	background-color: rgb(24, 144, 255);
	color: #fff;
	padding: 9px 15px;
	font-size: 12px;
	display: inline-block;
	border-radius: 2px;
	position: relative;
	&:hover {
		cursor: pointer;
		background-color: rgba(24, 144, 255, 0.8);
	}
	.add-box {
		position: fixed;
		top: 24%;
		left: 20%;
		background-color: red;
		padding: 3px 0;
		color: black;
		font-weight: bolder;
		z-index: 9;
		.box1 {
			padding: 4px 10px;
		}
	}
}

.el-tree {
	:deep(.el-tree-node__expand-icon.expanded) {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}
	:deep(.el-icon-caret-right:before) {
		content: '\e723';
		font-size: 14px;
	}
	:deep(.el-tree-node__expand-icon.expanded.el-icon-caret-right:before) {
		content: '\e722';
		font-size: 14px;
	}
	.curr-node {
		color: skyblue;
		font-weight: bold;
	}
}

.el-tree :deep() {
	.el-tree-node__content {
		position: relative;
	}

	.el-tree-node:last-child > .el-tree-node__content::before {
		content: '';
		border-left: 1px dotted #ccc;
		height: 10000px;
		position: absolute;
		margin-left: -9px;
		bottom: 13px;
	}

	.el-tree-node__children .el-tree-node__content::after {
		content: '';
		width: 9px;
		height: 1px;
		border-top: 1px dotted #ccc;
		position: absolute;
		margin-left: -9px;
	}

	.el-tree-node__content > .el-tree-node__expand-icon {
		padding: 6px 3px;
	}

	.el-tree-node__expand-icon.is-leaf {
		display: none;
		border-top: 1px dotted #ccc;
		height: 1px;
		margin-top: 10px;
		margin-left: 1px;
		margin-right: 5px;
		width: 14px;

		&::before{
			content: "";
    		font-size: 14px;
		}
	}
}
</style>