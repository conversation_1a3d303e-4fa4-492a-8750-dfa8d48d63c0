<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="260"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			table-name="huxing"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button">
				<el-button icon="el-icon-plus" type="primary" @click="handleAdd">添加</el-button>
				<el-button icon="el-icon-delete" type="danger" :disabled="!selectRows.length" @click="handleDelete($event)">
					删除
				</el-button>
			</template>
			<template #op="{ row }">
				<el-button type="primary" @click="handleSet(row)">分配权限</el-button>
				<el-button type="success" @click="handleMobile(row)">移动端权限</el-button>
				<el-button type="text" @click="handleEdit(row)">编辑</el-button>
				<el-button type="text" @click="handleDelete(row)">删除</el-button>
			</template>
			<template #cell="{ row, item }">
				<span v-if="item.prop == 'valid'">
						<span v-if="row[item.prop] == '1'" style="color: rgb(4, 205, 4); font-size: 18px"><i
							class="el-icon-success"></i></span>
						<span v-if="row[item.prop] == '0'" style="color: red; font-size: 18px"><i class="el-icon-error"></i></span>
				</span>
				<span v-else>{{ row[item.prop] }}</span>
			</template>
		</form-list>

		<ut-modal v-model="dialogFormVisible" :title="selectRows.id?'角色编辑':'增加角色'" width="500px">
			<edit :init-data="selectRows" flag="type" @fetch-data="fetchData" @close="dialogFormVisible=false" />
		</ut-modal>

		<ut-modal v-model="dialogSetVisible" title="权限分配" width="800px">
			<set-form :init-data="selectRows" :table-height="500" flag="type" :router-tree="routerTree" @fetch-data="fetchData" @close="dialogSetVisible=false" />
		</ut-modal>

		<ut-modal v-model="mobileSetVisible" title="移动端权限分配" width="800px">
			<mobile-set-form :parent-data="selectRows" :table-height="500" @fetchData="fetchData" @close="mobileSetVisible=false" />
		</ut-modal>
	</div>
</template>

<script>
import Edit from './edit'
import SetForm from './set'
import MobileSetForm from './mobile-menu'
import FormList from '@/views/components/form-list'
import { mapGetters } from 'vuex'
export default {
	name: 'Role',
	components: {
		Edit,
		SetForm,
		FormList,
		MobileSetForm,
	},
	props: {
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			listLoading: false,
			fullscreen: false,
			height: this.$baseTableHeight(1),
			columns: [
				{
					label: '名称',
					align: 'left',
					prop: 'name',
					sortable: false,
					disableCheck: true,
					show: true,
				},
				{
					align: 'center',
					label: '是否启用',
					width: 'auto',
					prop: 'valid',
					sortable: false,
					show: true,
				},
			],
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 20,
				order: '',
			},
			selectRows: '',
			dialogFormVisible: false,
			dialogSetVisible: false,
			mobileSetVisible:false,
			routerTree:[],
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
			commKey: 'comm/commKey',
		}),
	},
	created() {
		this.fetchData()
		this.getRouterTree()
	},
	methods: {
		async fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true
			try {
				const { data } = await this.$ut.api('homecaresystem/role/listpg', {
					commKey: this.commKey,
					baseId:this.comm.id,
					pageindex: this.page.pageindex,
					pagesize: this.page.pagesize,
					key: this.page.key,
				})
				this.dataList = data
			} finally {
				this.listLoading = false
			}
		},
		async getRouterTree() {
			try {
				const { data } = await this.$ut.api('homecaresystem/menu/router/tree', {
					commKey: this.commKey,
				})
				this.routerTree = data
			}finally {
				this.listLoading = false
			}
		},
		handleSelect(rows) {
			this.$emit('select', rows)
		},
		setSelectRows(val) {
			this.selectRows = val
		},
		onFullscreen(v) {
			this.fullscreen = v
		},
		onSelectRows(rows) {
			this.selectRows = rows
		},
		handleAdd() {
			this.selectRows = {}
			this.dialogFormVisible = true
		},
		handleEdit(row) {
			this.selectRows = row
			this.dialogFormVisible = true
		},
		handleSet(row) {
			this.selectRows = row
			this.dialogSetVisible = true
		},
		handleDelete(row) {
			let ids = []
			if (row.id) {
				ids = [row.id]
			} else {
				if (this.selectRows.length > 0) {
					ids = this.selectRows.map((item) => item.id)
				} else {
					this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
					return
				}
			}
			this.$baseConfirm('你确定要删除吗', null, async () => {
				this.$ut.api('homecaresystem/role/delete', {
						ids: ids,
						baseId: this.comm.id,
						commKey:this.commKey,
					})
					.then(() => {
						this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
						this.fetchData()
					})
			})
		},
		handleMobile(row) {
			this.selectRows = row
			this.mobileSetVisible = true
		},
	},
}
</script>

<style lang="scss" scoped>
.color-box {
	display: flex;
	align-items: center;
	justify-content: center;

	.color-preview {
		width: 30px;
		height: 30px;
		margin-right: 10px;
	}
}

</style>
