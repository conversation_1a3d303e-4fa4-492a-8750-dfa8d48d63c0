<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-form-item label="角色名称：" prop="name">
				<el-input v-model.trim="form.name" placeholder="请输入角色名称" />
			</el-form-item>
			<el-form-item label="是否启用：" prop="valid">
				<el-switch v-model="form.valid" />
			</el-form-item>
			<el-form-item label="显示顺序：" prop="showIndex">
				<el-input v-model.number="form.showIndex" placeholder="显示顺序" />
			</el-form-item>
		</el-form>
		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">确 定</el-button>
			<el-button @click="close">取 消</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
	name: 'PayTypeEdit',
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
	},
	emits: ['fetch-data', 'close'],
	data() {
		return {
			loading: false,
			form: {
				id: '',
				name: '',
				valid: true,
				showIndex: '',
			},
			rules: {
				name: [{ required: true, trigger: 'blur', message: '请输入角色名称' }],
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
			commKey: 'comm/commKey',
		}),
	},
	mounted() {
		this.getInfo()
	},
	methods: {
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			try {
				const { data } = await this.$ut.api('homecaresystem/role/info', {
					id: this.initData.id,
					baseId: this.comm.id,
					commKey: this.commKey,
				})
				this.form = data
			} finally {
				this.loading = false
			}
		},
		close() {
			this.$emit('close')
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
				try {
					await this.$ut.api('homecaresystem/role/save', {
						...this.form,
						baseId: this.comm.id,
						commKey: this.commKey,
					})
					this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
					this.$emit('fetch-data')
					this.close()
				} finally {
					this.loading = false
				}
			})
		},
	},
}
</script>
<style lang="scss" scoped>
</style>
