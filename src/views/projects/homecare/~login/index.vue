<template>
    <div class="wrap-full">
        <div class="wrap">
            <div class="logo-wrap">
                <a>
                    <img :src="require('./img/logo.png')" style="width:220px" alt="该链接已失效">
                </a>
            </div>
            <div class="register-wrap">
                <div class="pic">
                    <img :src="require('./img/bg.png')" alt="该链接已失效" />
                </div>
                <div class="register">
                    <div v-if="curPage == 1" class="register-top">
                        <h2 class="normal" :class="{ active: curTab == 0 }" @click="curTab = 0">普通登录</h2>
                        <a @click="curPage = 0"></a>
                    </div>
                    <!-- 扫码登录 -->
                    <div v-if="curPage == 0" class="scan-code">
                        <div class="screen-tu" @click="curPage = 1"></div>
                        <div class="scan-wechat">
                            <ut-icon icon="qr-scan-2-line" />
                            <span>微信扫一扫登录注册更方便</span>
                        </div>
                        <div v-loading="loadQrCode" class="qr-code">
                             <wxlogin 
v-if="qrCode.appid && qrCode.redirect_uri" :appid="qrCode.appid" scope="snsapi_login" 
                                :redirect-uri="qrCode.redirect_uri" :href="qrCode.href" :state="qrCode.state" />
                        </div>
                    </div>
                    <!--普通登录-->
                    <div v-if="curPage == 1 && curTab == 0" class="register-con">
                        <el-form ref="form" label-position="left" :model="form" :rules="rules">
                            <ul>
                                <li class="rc-inner-num">
                                    <i></i>
                                    <span>请输入账号</span>
                                </li>
                                <li class="rc-inner-virity">
                                    <i></i>
                                    <span>请输入密码</span>
                                </li>
                                <li
                                    style="width:300px;	height:32px;padding:0 6px;	color:#ff1877;	border:1px solid #ffd797;display:none;">
                                    <i
                                        style="display:inline-block;	float:left;	width:20px;	height:20px;margin:6px 10px;border:none;background:url(http://demo.sucaihuo.com/modals/25/2521/demo/images/bz_16x16.png) no-repeat 0 0;"></i>
                                    <span
                                        style="color:#ff1877;	display:inline-block;float:left;line-height:26px;">您输入的用户名或密码不正确</span>
                                </li>
                                <li class="form-group">
                                    <input v-model="form.account" type="text" class="form-control" placeholder="请输入账号" />
                                    <span
class="fa fa-check success"
                                        style="display:none;color:green;position:relative;left:-25px;top:5px;"></span>
                                </li>
                                <li class="form-group">
                                    <input v-model="form.password" type="password" class="form-control" placeholder="密码" />
                                </li>
                                <li class="form-group">
                                    <el-button class="submit" style="border-radius:5px;" @click="handleLogin">{{
                                        lang('立即登录') }}</el-button>
                                </li>
                                <!-- <li>
                                    <a href="../register/register.html" class="zhuce">{{ lang('新用户注册') }}</a>
                                    <a href="#">忘记密码？</a>
                                </li> -->
                                <li class="zjdl">
                                    新用户请使用微信登录无需注册
                                </li>
                                <li class="form-group">
                                    <div class="btn-other qq"></div>
                                    <div class="btn-other weixin" @click="curPage = 0"></div>
                                    <div class="btn-other weibo"></div>
                                </li>
                            </ul>
                        </el-form>
                    </div>
                    <!--手机无密码登录-->
                    <div v-if="curPage == 1 && curTab == 1" class="login-sms">
                        <ul>
                            <li id="inner-num">
                                <i></i>
                                <span>请输入手机号码</span>
                            </li>
                            <li id="inner-virity">
                                <i></i>
                                <span>请输入验证码</span>
                            </li>
                            <li class="lg-num  form-group">
                                <input type="text" name="phone-num" class="form-control" placeholder="手机号码" />
                                <span
class="fa fa-check success"
                                    style="display:none;color:green;position:relative;left:-25px;top:5px;"></span>
                            </li>
                            <li class="password form-group">
                                <input type="password" name="password" class="form-control" placeholder="验证码" />

                            </li>
                            <li>
                                <button class="getcode" style="border-radius:5px;">获取手机验证码</button>
                            </li>
                            <li>
                                <button class="submit" type="submit" style="border-radius:5px;">登录</button>
                            </li>
                        </ul>
                    </div>

                </div>
            </div>
            <div class="copyright">
                © 先施康养 2024
            </div>
        </div>
    </div>
</template>


<script>
import wxlogin from '@/extra/ut-wxlogin'
import { mapActions, mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
import { isPassword } from '@/common/utils/validate'

export default {
    components: { wxlogin },
    directives: {
        focus: {
            inserted(el) {
                el.querySelector('input').focus()
            },
        },
    },
    beforeRouteLeave(to, from, next) {
        clearInterval(this.timer)
        next()
    },

    data() {
        const validateUsername = (rule, value, callback) => {
            if ('' === value) callback(new Error(this.lang('用户名不能为空')))
            else callback()
        }
        const validatePassword = (rule, value, callback) => {
            if (!isPassword(value)) callback(new Error(this.lang('密码不能少于6位')))
            else callback()
        }
        return {
            host: '',
            curPage: 0,
            curTab: 0,

            form: {
                account: '',
                password: '',
                verificationCode: '',
            },
            rules: {
                account: [{ required: true, trigger: 'blur', validator: validateUsername, },],
                password: [{ required: true, trigger: 'blur', validator: validatePassword, },],
            },
            loading: false,
            redirect: undefined,
            loadQrCode: false,
            qrCode: {
                appid: '',
                redirect_uri: '',
                state: '1',
                href: 'data:text/css;base64,LmltcG93ZXJCb3ggLnFyY29kZSB7d2lkdGg6IDIwMHB4O2hlaWdodDoyMDBweH0NCi5pbXBvd2VyQm94IC5pbmZvIHt3aWR0aDogMjAwcHh9DQouc3RhdHVzX2ljb24ge2Rpc3BsYXk6IG5vbmV9DQouaW1wb3dlckJveCAuc3RhdHVzIHt0ZXh0LWFsaWduOiBjZW50ZXI7fQ0KLmltcG93ZXJCb3ggLnRpdGxlIHtkaXNwbGF5OiBub25lfQ0KaWZyYW1lIHtoZWlnaHQ6IDMyMnB4O30NCg==' // 自定义样式链接
            },
            cssBase64Data: 'data:text/css;base64,LmltcG93ZXJCb3ggLnFyY29kZSB7d2lkdGg6IDIwMHB4O2hlaWdodDoyMDBweH0NCi5pbXBvd2VyQm94IC5pbmZvIHt3aWR0aDogMjAwcHh9DQouc3RhdHVzX2ljb24ge2Rpc3BsYXk6IG5vbmV9DQouaW1wb3dlckJveCAuc3RhdHVzIHt0ZXh0LWFsaWduOiBjZW50ZXI7fQ0KLmltcG93ZXJCb3ggLnRpdGxlIHtkaXNwbGF5OiBub25lfQ0KaWZyYW1lIHtoZWlnaHQ6IDMyMnB4O30NCg=='
        }
    },
    computed: {
        ...mapGetters({
            title: 'settings/title',
            comm: 'comm/comm',
        }),
    },
    watch: {
        $route: {
            handler(route) {
                this.redirect = (route.query && route.query.redirect) || '/'
            },
            immediate: true,
        },
    },
    created() {
        console.log(window.location.host)
    },
    mounted() {
        if (process.env.NODE_ENV === 'development') {
            this.form.account = '***********'
            this.form.password = '1234'
        }
        this.getWebInfo()
        this.host = window.location.hostname
        // console.log(this.$isServer)
    },
    methods: {
        ...mapActions({
            login: 'user/login',
            getMyInfo: 'user/getMyInfo',
            clearAll: 'comm/clearAll'
        }),
        lang,
        handlePassword() {
            this.passwordType === 'password' ? (this.passwordType = '') : (this.passwordType = 'password')
            this.$nextTick(() => {
                this.$refs.password.focus()
            })
        },
        handleRoute() {
            return this.redirect === '/404' || this.redirect === '/403' ? '' : this.redirect
        },
        handleLogin() {
            this.$refs.form.validate(async (valid) => {
                if (valid)
                    try {
                        this.loading = true
                        await this.login(this.form).catch(() => { })
                        if (this.handleRoute() === '/') {
                            this.$router.push('/index')
                        } else {
                            if (!this.comm || !this.comm.id) {
                                this.$router.push('/index')
                            } else {
                                this.$router.push(this.handleRoute())
                            }

                        }
                    } finally {
                        this.loading = false
                    }
            })
        },
        changeCode() {
           
        },
        async getWebInfo() {
            try {
                this.loadQrCode = true
                const { data } = await this.$ut.api('comm/wechat/open_website', { commKey: 'ut', redirect: this.redirect })
                if (data) {
                    this.qrCode.appid = data.appid
                    this.qrCode.redirect_uri = data.redirect_uri
                }
            }
            finally {
                this.loadQrCode = false
            }
        }
    },
}
</script>
<style>
body {
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
    font-size: 14px !important;
    line-height: 1.42857143 !important;
}
</style>

<style lang="scss" scoped>
.wrap-full {
    background: #93defe;
    height: 100vh;
}

// *{
//     transition:all 0.5s linear;
// 	-webkit-transition:all 0.5s linear;
// }
div,
img,
input,
h2,
table,
tr,
td,
select,
i,
option,
button,
a,
dl,
dd,
ul,
li,
b {
    margin: 0;
    padding: 0;
    font-style: normal;
    font: 12px/1.6 tahoma, arial, sans-serif;
    color: #333;
    box-sizing: border-box;
}

ul {
    list-style: none;
}

a {
    text-decoration: none;
}

.wrap {
    width: 1200px;
    margin: 0 auto;
    overflow: auto;

    &:before {
        content: '';
        display: table;
    }
}

.logo-wrap {
    width: 100%;
    height: 52px;
    margin-top: 30px;

    a {
        width: 220px;
        height: 100%;
        text-decoration: none;
        display: inline-block;

        img {
            display: block;
        }
    }
}

.register-wrap::after {
    content: '';
    display: block;
    clear: both;
}

.register-wrap {
    width: 100%;
    padding: 120px 0;
    min-height: 480px;
    position: relative;

    .pic {
        float: left;
        width: 500px;

        img {
            width: 100%;
        }
    }

    .register {
        position: absolute;
        top: 100px;
        right: 30px;
        width: 400px;
        float: right;
        border: 1px solid #ccc;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0px 4px 4px 4px rgb(0 0 0 / 4%);

    }
}

// 扫码
.scan-code {
    height: 358px;
    position: relative;

    &>div {
        border: none;
    }

    .screen-tu {
        width: 53px;
        height: 53px;
        position: relative;
        left: 338px;
        top: 10px;
        cursor: pointer;
        background: url('./img/pc.png') no-repeat;
        background-position: 0 0;
    }

    .scan-wechat {
        padding-left: 40px;
        width: 300px;
        position: relative;
        height: 39px;
        top: -30px;
        font-size: 20px;

        [class*='ri'] {
            font-size: 20px;
        }
    }

    .qr-code {
        width: 300px;
        height: 280px;
        overflow: hidden;
        margin: 0 auto;
        position: relative;
        top: -30px;

        img {
            display: block;
        }
    }
}


.register-top::after {
    content: '';
    display: block;
    clear: both;
}

.register-top {
    border-bottom: 1px solid #ccc;
    box-sizing: content-box;
    padding: 8px 0 0 0;
    display: block;

    h2 {
        font-size: 14px;
        color: #333;
        height: 42px;
        line-height: 42px;
        font-weight: 800;
        letter-spacing: 1px;
        float: left;
        cursor: pointer;
        width: 119px;
        text-align: center;
        position: relative;
    }

    h2.normal {
        margin-left: 40px;
        top: 4px;
    }

    h2.nopassword {
        margin-left: 36px;
        top: 4px;
    }

    h2.active {
        border-bottom: 2px solid #f55;
    }

    a {
        cursor: pointer;
        float: right;
        width: 46px;
        height: 46px;
        margin-right: 8px;
        background: url('./img/qrcode.png') no-repeat 0 0;
    }
}

// 账号登录
.register-con::after {
    content: '';
    display: block;
    clear: both;
}

.register-con {
    padding: 0 0 30px 38px;
    display: block;

    ul {
        margin-top: 20px;

        li {
            margin-bottom: 10px;
            vertical-align: top;

            .btn {
                width: 104px;
                margin-left: 10px;
                height: 30px;
                padding: 0 6px;
                font-size: 100%;
                border-radius: 0;
                /* display:inline-block; */
                border: 1px solid #ccc;
                vertical-align: top;
            }

            button {
                display: inline;
                border: none;
                width: 300px;
                height: 30px;
                padding: 0 6px;
                background: #ff1877;
                border-radius: 2px;
                cursor: pointer;
                color: #fff;
            }

            a.zhuce {
                float: right;
                margin-right: 60px;
            }

            div {
                width: 100px;
                height: 36px;
                float: left;
                margin-right: 6px;
                border: 1px solid #ccc;
                cursor: pointer;
            }
        }

        .rc-inner-num,
        .rc-inner-virity {
            width: 300px;
            height: 32px;
            padding: 0 6px;
            color: #ff1877;
            border: 1px solid #ffd797;
            display: none;
        }

        .rc-inner-num>i,
        .rc-inner-virity>i,
        .rc-innerError>i {
            float: left;
            width: 20px;
            height: 20px;
            margin: 6px 10px;
            border: none;
            background: url(http://demo.sucaihuo.com/modals/25/2521/demo/images/bz_16x16.png) no-repeat 0 0;
        }

        #rc-inner-num>i,
        #rc-inner-virity>i,
        #rc-innerError>i {
            float: left;
            display: inline-block;
            width: 20px;
            height: 20px;
            margin: 6px 10px;
            border: none;
            background: url(http://demo.sucaihuo.com/modals/25/2521/demo/images/bz_16x16.png) no-repeat 0 0;
        }
    }

    .btn-other {
        border-radius: 5px;
    }

    div.qq {
        background: url('./img/sprite.png') no-repeat 0 -120px;
    }

    div.weixin {
        background: url('./img/sprite.png') no-repeat 0 -200px;
    }

    div.weibo {
        background: url('./img/sprite.png') no-repeat 0 -160px;
    }

    .form-group {
        padding: 4px 4px;
    }

    input {
        width: 300px;
        height: 30px;
        line-height: 30px;
        padding: 0 6px;
        font-size: 100%;
        display: inline-block;
        vertical-align: top;
    }
}

// 短信登录
.login-sms:after {
    content: '';
    display: block;
    clear: both;
}

.login-sms {
    padding: 0 0 30px 38px;

    ul:after {
        content: '';
        display: block;
        clear: both;
    }

    ul {
        margin-top: 20px;

        li {
            margin-bottom: 10px;

            i {
                display: inline-block;
                width: 104px;
                height: 30px;
                text-align: center;
                line-height: 30px;
                margin-right: 10px;
                font-size: 14px;
                border: 1px solid #ccc;
                background: #eaeaea;
                vertical-align: top;
            }
        }
    }

    input {
        width: 180px;
        height: 30px;
        line-height: 30px;
        padding: 0 6px;
        font-size: 100%;
        display: inline-block;
        vertical-align: top;
    }

    button {
        display: inline;
        border: none;
        width: 300px;
        height: 30px;
        padding: 0 6px;
        background: #ff1877;
        border-radius: 2px;
        cursor: pointer;
        color: #fff;
    }
}

.copyright{
    text-align: center;
    font-size: 120%;
    color: #fdfdfd;
    letter-spacing: 4px;
}


</style>