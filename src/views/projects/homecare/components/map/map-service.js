/**
 * 腾讯地图服务 - 统一管理地图API加载和实例
 */
class MapService {
	constructor() {
		this.isLoaded = false
		this.isLoading = false
		this.loadPromise = null
		this.mapKey = ''
		this.loadedScripts = new Set()
		this.mapInstances = new Map()
	}

	/**
	 * 加载腾讯地图API
	 * @param {string} mapKey - 地图密钥
	 * @returns {Promise} 加载Promise
	 */
	loadMapAPI(mapKey) {
		if (this.isLoaded && this.mapKey === mapKey) {
			return Promise.resolve()
		}

		if (this.isLoading && this.loadPromise) {
			return this.loadPromise
		}

		this.mapKey = mapKey
		this.isLoading = true

		this.loadPromise = new Promise((resolve, reject) => {
			// 检查是否已经加载
			if (window.TMap) {
				this.isLoaded = true
				this.isLoading = false
				resolve()
				return
			}

			// 创建唯一的回调函数名
			const callbackName = `initTMap_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`

			// 设置全局回调函数
			window[callbackName] = () => {
				this.isLoaded = true
				this.isLoading = false
				// 清理回调函数
				delete window[callbackName]
				resolve()
			}

			// 创建script标签
			const script = document.createElement('script')
			script.type = 'text/javascript'
			script.src = `https://map.qq.com/api/gljs?v=1.exp&key=${mapKey}&callback=${callbackName}`
			script.onerror = () => {
				this.isLoading = false
				delete window[callbackName]
				reject(new Error('地图API加载失败'))
			}

			// 添加到页面
			document.head.appendChild(script)
		})

		return this.loadPromise
	}

	/**
	 * 创建地图实例
	 * @param {string} containerId - 地图容器ID
	 * @param {Object} options - 地图配置选项
	 * @param {string} instanceId - 实例ID，用于管理多个地图实例
	 * @returns {Object} 地图实例
	 */
	createMap(containerId, options = {}, instanceId = containerId) {
		if (!this.isLoaded || !window.TMap) {
			throw new Error('地图API未加载，请先调用loadMapAPI')
		}

		// 销毁已存在的实例
		this.destroyMap(instanceId)

		const defaultOptions = {
			pitch: 0,
			zoom: 15,
			baseMap: {
				type: 'vector',
				features: ['base', 'building3d'],
			},
		}

		const mapOptions = { ...defaultOptions, ...options }

		// 处理center选项 - 如果是普通对象，转换为TMap.LatLng
		if (
			mapOptions.center &&
			typeof mapOptions.center === 'object' &&
			mapOptions.center.lat !== undefined &&
			mapOptions.center.lng !== undefined &&
			!(mapOptions.center instanceof window.TMap.LatLng)
		) {
			mapOptions.center = new window.TMap.LatLng(mapOptions.center.lat, mapOptions.center.lng)
		}

		const mapInstance = new window.TMap.Map(containerId, mapOptions)

		// 保存实例引用
		this.mapInstances.set(instanceId, mapInstance)

		return mapInstance
	}

	/**
	 * 销毁地图实例
	 * @param {string} instanceId - 实例ID
	 */
	destroyMap(instanceId) {
		const instance = this.mapInstances.get(instanceId)
		if (instance) {
			try {
				instance.destroy()
			} catch (error) {
				console.warn('销毁地图实例时出错:', error)
			}
			this.mapInstances.delete(instanceId)
		}
	}

	/**
	 * 创建标记样式
	 * @param {Object} options - 样式选项
	 * @returns {Object} 标记样式
	 */
	createMarkerStyle(options = {}) {
		if (!window.TMap) {
			throw new Error('地图API未加载')
		}

		return new window.TMap.MarkerStyle({
			width: options.width || 32,
			height: options.height || 48,
			anchor: options.anchor || { x: 16, y: 48 },
			src: options.src || 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/marker_blue.png',
			...options,
		})
	}

	/**
	 * 创建标签样式
	 * @param {Object} options - 样式选项
	 * @returns {Object} 标签样式
	 */
	createLabelStyle(options = {}) {
		if (!window.TMap) {
			throw new Error('地图API未加载')
		}

		return new window.TMap.LabelStyle({
			color: options.color || '#333',
			size: options.size || 12,
			offset: options.offset || { x: 0, y: -10 },
			angle: options.angle || 0,
			alignment: options.alignment || 'center',
			...options,
		})
	}

	/**
	 * 创建多标记图层
	 * @param {Object} mapInstance - 地图实例
	 * @param {Object} options - 选项
	 * @returns {Object} 多标记图层
	 */
	createMultiMarker(mapInstance, options = {}) {
		if (!window.TMap) {
			throw new Error('地图API未加载')
		}

		return new window.TMap.MultiMarker({
			map: mapInstance,
			...options,
		})
	}

	/**
	 * 创建信息窗
	 * @param {Object} mapInstance - 地图实例
	 * @param {Object} options - 选项
	 * @returns {Object} 信息窗实例
	 */
	createInfoWindow(mapInstance, options = {}) {
		if (!window.TMap) {
			throw new Error('地图API未加载')
		}

		return new window.TMap.InfoWindow({
			map: mapInstance,
			position: options.position || new window.TMap.LatLng(39.908823, 116.39747),
			content: options.content || '',
			...options,
		})
	}

	/**
	 * 自适应地图边界
	 * @param {Object} mapInstance - 地图实例
	 * @param {Array} positions - 位置数组
	 * @param {Object} options - 选项
	 */
	fitBounds(mapInstance, positions, options = {}) {
		if (!window.TMap || !positions.length) return

		const bounds = new window.TMap.LatLngBounds()
		positions.forEach((pos) => {
			const latLng = pos instanceof window.TMap.LatLng ? pos : new window.TMap.LatLng(pos.lat, pos.lng)
			bounds.extend(latLng)
		})

		mapInstance.fitBounds(bounds, { padding: 50, ...options })
	}

	/**
	 * 清理所有资源
	 */
	cleanup() {
		// 销毁所有地图实例
		this.mapInstances.forEach((instance, id) => {
			this.destroyMap(id)
		})

		this.isLoaded = false
		this.isLoading = false
		this.loadPromise = null
		this.mapKey = ''
	}

	/**
	 * 检查坐标是否有效
	 * @param {number} lat - 纬度
	 * @param {number} lng - 经度
	 * @returns {boolean} 是否有效
	 */
	static isValidCoordinate(lat, lng) {
		return typeof lat === 'number' && typeof lng === 'number' && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180 && !isNaN(lat) && !isNaN(lng)
	}

	/**
	 * 格式化坐标对象
	 * @param {Object} coord - 坐标对象 {lat, lng}
	 * @returns {Object} 格式化后的坐标对象
	 */
	static formatCoordinate(coord) {
		if (!coord || typeof coord !== 'object') {
			return null
		}

		const lat = parseFloat(coord.lat || coord.latitude)
		const lng = parseFloat(coord.lng || coord.longitude)

		if (!this.isValidCoordinate(lat, lng)) {
			return null
		}

		return { lat, lng }
	}

	/**
	 * 计算两点之间的距离（单位：公里）
	 * @param {Object} coord1 - 坐标1 {lat, lng}
	 * @param {Object} coord2 - 坐标2 {lat, lng}
	 * @returns {number} 距离（公里）
	 */
	static calculateDistance(coord1, coord2) {
		const c1 = this.formatCoordinate(coord1)
		const c2 = this.formatCoordinate(coord2)

		if (!c1 || !c2) {
			return 0
		}

		const R = 6371 // 地球半径（公里）
		const dLat = this.toRadians(c2.lat - c1.lat)
		const dLng = this.toRadians(c2.lng - c1.lng)
		const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(this.toRadians(c1.lat)) * Math.cos(this.toRadians(c2.lat)) * Math.sin(dLng / 2) * Math.sin(dLng / 2)
		const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
		return R * c
	}

	/**
	 * 角度转弧度
	 * @param {number} degrees - 角度
	 * @returns {number} 弧度
	 */
	static toRadians(degrees) {
		return degrees * (Math.PI / 180)
	}

	/**
	 * 获取地图中心点（多个坐标的中心）
	 * @param {Array} coordinates - 坐标数组 [{lat, lng}, ...]
	 * @returns {Object} 中心点坐标 {lat, lng}
	 */
	static getCenterPoint(coordinates) {
		if (!Array.isArray(coordinates) || coordinates.length === 0) {
			return null
		}

		const validCoords = coordinates.map((coord) => this.formatCoordinate(coord)).filter((coord) => coord !== null)

		if (validCoords.length === 0) {
			return null
		}

		const totalLat = validCoords.reduce((sum, coord) => sum + coord.lat, 0)
		const totalLng = validCoords.reduce((sum, coord) => sum + coord.lng, 0)

		return {
			lat: totalLat / validCoords.length,
			lng: totalLng / validCoords.length,
		}
	}
}

// 创建单例实例
const mapService = new MapService()

export default mapService
