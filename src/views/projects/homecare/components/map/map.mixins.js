import mapService from './map-service'
import { mapGetters } from 'vuex'

/**
 * 地图混入 - 提供统一的地图功能
 */
const MapMixins = {
	data() {
		return {
			mapLoading: false,
			mapKey: '',
			mapInstance: null,
			mapInstanceId: '',
			mapMarkers: [],
			mapZoom: 15,
			mapPitch: 0,
		}
	},

	computed: {
		...mapGetters({
			commKey: 'comm/commKey',
			comm: 'comm/comm',
		}),
	},

	beforeDestroy() {
		this.destroyMap()
	},

	methods: {
		/**
		 * 获取地图密钥
		 */
		async getMapKey() {
			try {
				const { data } = await this.$ut.api('homecarecomm/mapKey', {
					commKey: this.commKey,
				})
				if (data) {
					this.mapKey = data.mapKey
					return data.mapKey
				}
				throw new Error('未获取到地图密钥')
			} catch (error) {
				console.error('获取地图密钥失败:', error)
				this.$message.error('获取地图密钥失败')
				throw error
			}
		},

		/**
		 * 初始化地图
		 * @param {string} containerId - 地图容器ID
		 * @param {Object} options - 地图选项
		 * @param {string} instanceId - 实例ID，默认使用容器ID
		 */
		async initMap(containerId, options = {}, instanceId = null) {
			try {
				this.mapLoading = true

				// 设置实例ID
				this.mapInstanceId = instanceId || containerId

				// 获取地图密钥
				if (!this.mapKey) {
					await this.getMapKey()
				}

				// 加载地图API
				await mapService.loadMapAPI(this.mapKey)

				// 创建地图实例
				const mapOptions = {
					pitch: this.mapPitch,
					zoom: this.mapZoom,
					...options,
				}

				this.mapInstance = mapService.createMap(containerId, mapOptions, this.mapInstanceId)

				// 触发地图初始化完成事件
				this.$emit('map-ready', this.mapInstance)

				return this.mapInstance
			} catch (error) {
				console.error('地图初始化失败:', error)
				this.$message.error('地图初始化失败')
				throw error
			} finally {
				this.mapLoading = false
			}
		},

		/**
		 * 销毁地图
		 */
		destroyMap() {
			if (this.mapInstanceId) {
				mapService.destroyMap(this.mapInstanceId)
				this.mapInstance = null
				this.mapInstanceId = ''
			}
		},

		/**
		 * 添加标记
		 * @param {Array} markers - 标记数据数组
		 * @param {Object} styles - 标记样式配置
		 * @param {Object} options - 其他选项
		 */
		addMarkers(markers = [], styles = {}, options = {}) {
			if (!this.mapInstance || !markers.length) return null

			// 转换标记数据
			const geometries = markers.map((marker) => ({
				styleId: marker.type || 'default',
				position: new window.TMap.LatLng(marker.lat, marker.lng),
				content: marker.name || '',
				properties: {
					name: marker.name,
					address: marker.address,
					type: marker.type,
					...marker.properties,
				},
			}))

			// 创建默认样式
			const defaultStyles = {
				default: mapService.createMarkerStyle({
					color: '#369',
					src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/marker_blue.png',
				}),
			}

			// 合并样式
			const markerStyles = {}
			Object.keys({ ...defaultStyles, ...styles }).forEach((key) => {
				if (styles[key]) {
					markerStyles[key] = typeof styles[key] === 'object' && styles[key].constructor.name === 'MarkerStyle' ? styles[key] : mapService.createMarkerStyle(styles[key])
				} else {
					markerStyles[key] = defaultStyles[key]
				}
			})

			// 创建多标记图层
			const multiMarker = mapService.createMultiMarker(this.mapInstance, {
				styles: markerStyles,
				geometries,
				...options,
			})

			// 自适应边界
			if (geometries.length > 1) {
				const positions = geometries.map((g) => g.position)
				mapService.fitBounds(this.mapInstance, positions)
			} else if (geometries.length === 1) {
				this.mapInstance.setCenter(geometries[0].position)
			}

			return multiMarker
		},

		/**
		 * 创建信息窗
		 * @param {Object} options - 信息窗选项
		 */
		createInfoWindow(options = {}) {
			if (!this.mapInstance) return null
			return mapService.createInfoWindow(this.mapInstance, options)
		},

		/**
		 * 设置地图中心
		 * @param {number} lat - 纬度
		 * @param {number} lng - 经度
		 */
		setMapCenter(lat, lng) {
			if (this.mapInstance) {
				this.mapInstance.setCenter(new window.TMap.LatLng(lat, lng))
			}
		},

		/**
		 * 设置地图缩放级别
		 * @param {number} zoom - 缩放级别
		 */
		setMapZoom(zoom) {
			if (this.mapInstance) {
				this.mapInstance.setZoom(zoom)
			}
		},

		/**
		 * 自适应显示所有标记
		 * @param {Array} positions - 位置数组 [{lat, lng}, ...]
		 * @param {Object} options - 选项
		 */
		fitMapBounds(positions, options = {}) {
			if (this.mapInstance && positions.length) {
				mapService.fitBounds(this.mapInstance, positions, options)
			}
		},
	},
}

export default MapMixins
