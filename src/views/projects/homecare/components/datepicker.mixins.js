const DatePickerMixins={
    data(){
        return{
            pickerOptions: {
				shortcuts: [{
					text: '今天',
					onClick(picker) {
						const end = new Date()
						const start = new Date()
						picker.$emit('pick', [start, end])
					},
				},
				{
					text: '最近三天',
					onClick(picker) {
						const end = new Date()
						const start = new Date()
						start.setTime(start.getTime() - 3600 * 1000 * 24 * 2)
						picker.$emit('pick', [start, end])
					},
				},
				{
					text: '最近一周',
					onClick(picker) {
						const end = new Date()
						const start = new Date()
						start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
						picker.$emit('pick', [start, end])
					},
				},
				{
					text: '最近一月',
					onClick(picker) {
						let n=1
						const end = new Date()
						const datenow = new Date()
						datenow.setMonth(datenow.getMonth() - n);
						let dyear = datenow.getFullYear();
						let dmonth = datenow.getMonth() + 1;
						dmonth = dmonth < 10 ? 0 + dmonth : dmonth;
						let dday = datenow.getDate();
						const datestart =  dyear.toString() + '-' + dmonth.toString() + '-' + dday.toString();
						const start=new Date(datestart)
						start.setTime(start.getTime() + 3600 * 1000 * 24 * 1)
						picker.$emit('pick', [start, end])
					},
				},
				{
					text: '最近三月',
					onClick(picker) {
						let n=3
						const end = new Date()
						const datenow = new Date()
						datenow.setMonth(datenow.getMonth() - n);
						let dyear = datenow.getFullYear();
						let dmonth = datenow.getMonth() + 1;
						dmonth = dmonth < 10 ? 0 + dmonth : dmonth;
						let dday = datenow.getDate();
						const datestart =  dyear.toString() + '-' + dmonth.toString() + '-' + dday.toString();
						const start=new Date(datestart)
						start.setTime(start.getTime() + 3600 * 1000 * 24 * 1)
						picker.$emit('pick', [start, end])
					},
				},
				{
					text: '最近半年',
					onClick(picker) {
						let n=6
						const end = new Date()
						const datenow = new Date()
						datenow.setMonth(datenow.getMonth() - n);
						let dyear = datenow.getFullYear();
						let dmonth = datenow.getMonth() + 1;
						dmonth = dmonth < 10 ? 0 + dmonth : dmonth;
						let dday = datenow.getDate();
						const datestart =  dyear.toString() + '-' + dmonth.toString() + '-' + dday.toString();
						const start=new Date(datestart)
						start.setTime(start.getTime() + 3600 * 1000 * 24 * 1)
						picker.$emit('pick', [start, end])
					},
				},
				{
					text: '最近一年',
					onClick(picker) {
						let n=12
						const end = new Date()
						const datenow = new Date()
						datenow.setMonth(datenow.getMonth() - n);
						let dyear = datenow.getFullYear();
						let dmonth = datenow.getMonth() + 1;
						dmonth = dmonth < 10 ? 0 + dmonth : dmonth;
						let dday = datenow.getDate();
						const datestart =  dyear.toString() + '-' + dmonth.toString() + '-' + dday.toString();
						const start=new Date(datestart)
						start.setTime(start.getTime() + 3600 * 1000 * 24 * 1)
						picker.$emit('pick', [start, end])
					},
				},
				{
					text: '本月开始',
					onClick(picker) {
						const end = new Date()
						const datenow = new Date()
						let dyear = datenow.getFullYear();
						let dmonth = datenow.getMonth()+1;
						dmonth = dmonth < 10 ? 0 + dmonth : dmonth;
						let dday = '1';
						const datestart =  dyear.toString() + '-' + dmonth.toString() + '-' + dday;
						const start=new Date(datestart)
						picker.$emit('pick', [start, end])
					},
				},
				{
					text: '本年开始',
					onClick(picker) {
						const end = new Date()
						const datenow = new Date()
						let dyear = datenow.getFullYear();
						let dmonth = '01';
						let dday = '01';
						const datestart =  dyear.toString() + '-' + dmonth + '-' + dday;
						const start=new Date(datestart)
						picker.$emit('pick', [start, end])
					},
				}]
			},
			pickerMonthOptions: {
				shortcuts: [{
					text: '本月',
					onClick(picker) {
					  picker.$emit('pick', [new Date(), new Date()]);
					}
				  }, {
					text: '今年至今',
					onClick(picker) {
					  const end = new Date();
					  const start = new Date(new Date().getFullYear(), 0);
					  picker.$emit('pick', [start, end]);
					}
				  }, {
					text: '最近六个月',
					onClick(picker) {
					  const end = new Date();
					  const start = new Date();
					  start.setMonth(start.getMonth() - 6);
					  picker.$emit('pick', [start, end]);
					}
				  }]
			},
        }
    },
	methods:{
		setCurrDayRange(){
			const datenow = new Date()
			let dyear = datenow.getFullYear()
			let dmonth = datenow.getMonth()+1;
			dmonth = dmonth < 10 ? 0 + dmonth : dmonth;
			let dday = '01'
			const datestart =  dyear.toString() + '-' + dmonth + '-' + dday
			
			const dateend =  dyear.toString() + '-' + dmonth.toString() + '-' + datenow.getDate()

			return [datestart, dateend]
		},
		setCurrMonthRange(){
			const datenow = new Date()
			let dyear = datenow.getFullYear()
			let dmonth = datenow.getMonth()+1;
			dmonth = dmonth < 10 ? '0' + dmonth : dmonth;
			const datestart =  dyear.toString() + '-' + dmonth
			
			const dateend =  dyear.toString() + '-' + dmonth.toString()

			return [datestart, dateend]
		},
		setCurrYearMonthRange(){
			const datenow = new Date()
			let dyear = datenow.getFullYear()
			let dmonth = datenow.getMonth()+1;
			dmonth = dmonth < 10 ? '0' + dmonth : dmonth;
			const datestart =  dyear.toString() + '-' + '01'
			
			const dateend =  dyear.toString() + '-' + dmonth.toString()

			return [datestart, dateend]
		}
	}
}

export default DatePickerMixins
