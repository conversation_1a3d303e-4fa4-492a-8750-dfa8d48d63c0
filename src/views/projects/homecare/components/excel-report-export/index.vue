<template>
    <ut-dialog
		v-if="value"
        :confirm-btn-text="downloadTips"
        width="400px"
        title="导出"
        @close="closeHandle"
        @confirm="exportExcel"
    >
        <div class="line-text">
            导出时间由数据量大小决定，数据量大时请耐心等待。
        </div>
        <div class="line-text">
            <span style="margin-right: 8px;">导出后请在下载列表中查看（右上角</span>
			<ut-icon icon="download-2-fill" />
			<span style="margin-left: 8px;">图标这里）。</span>
        </div>
    </ut-dialog>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
// import { download } from '@/common/utils/file'

export default {
	props:{
		value:{
			type:Boolean,
			default:false,
		},
		simple:{
			type:Boolean,
			default:false,
		},
		title:{
			type:String,
			default:'',
		},
		dataUrl:{
			type:String,
			default:'',
		},
		arguments:{
			type:Array,
			default:()=>[],
		},
		columns:{
			type:Array,
			default:()=>[],
		},
		method:{
			type:String,
			default:'GET',
		},
		code:{
			type:String,
			default:'',
		},
		isList:{
			type:Boolean,
			default:false,
		},
		count:{
			type:Number,
			default:0
		},
		secondApis:{
			type:Array,
			default:()=>[],
		},
		fileName:{
			type:String,
			default:'',
		},
	},
    data(){
        return{
            exportVisible:false,
            downloadTips:'',
        }
    },
	computed: {
		...mapGetters({
			comm: 'comm/comm',
			commKey: 'comm/commKey',
		}),
	},
    methods:{
		...mapActions({
			downShow: 'download/downShow',
		}),
		closeHandle(){
			this.$emit('input',false)
		},
        async exportExcel() {
			if(this.count>60000){
				this.$emit('input',false)
				this.$baseMessage('数据大于6万条，不能导出，请缩小范围', 'error', 'ut-hey-message-error')
				return
			}
			this.downloadTips = '正在生成excel...'
			let excelUrl='comm/excel/download/reg'
			if(this.simple) excelUrl='comm/excel/download/normal_reg'
			await this.$ut.api(excelUrl, {
				commKey:this.commKey,
				baseId:this.comm.id,
				code:this.code?this.code:this.title,
				fileName: (this.fileName?this.fileName:this.title) + '.xlsx',
				title:this.title,
				isList:this.isList,
				url:this.$ut.apiUrl(this.dataUrl),
				method:this.method,
				arguments:this.arguments,
				header:this.simple?this.columns:'',
				secondApis:this.secondApis,
			}).then(()=>{

				this.downloadTips = '请稍后到下载列表中查看'
				this.downShow(true)
	
				
				setTimeout(() => {
					this.$emit('input',false)
					this.downloadTips = ''
				}, 500)
			})
		},
    }
}
</script>
<style lang="scss" scoped>


.line-text{
	line-height: 1.5;

	:deep(){
		[class*='ri']{
			font-size: 24px !important;
			color: $base-color-blue;
		}
	}
}
</style>