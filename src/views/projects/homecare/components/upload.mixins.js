import { mapGetters } from 'vuex'
const UploadMixins = {
    inject:['bind','unbind'],
    data() {
		return {
            uploadImage:'',
			uploadInfo: {},
			fileList: [],
			maxCount:0,
		}
	},
    beforeMount(){
        this.getAction()
		this.loadUploadImage()
		this.bind('uploadimage',this.uploadImageWebsocket)
	},
	destroyed(){
		this.unbind('uploadimage')
	},
    computed: {
        ...mapGetters({
			comm: 'comm/comm',
			commKey:'comm/commKey',
		}),
		uploadAction() { return `${this.uploadInfo.url}?token=${this.uploadInfo.token}` },
	},
	methods: {
        loadUploadImage(){
			if(!this.comm.id || this.comm.id==='xxx') return 
			this.$ut.api('homecarecomm/uploadImage',{communityId:this.comm.id}).then(res=>{
				this.uploadImage=res.data
			})
		},
        async getAction() {// 获得上传文件地址信息
			const { data } = await this.$ut.api('homecarecomm/uploadInfo', { communityId: this.comm.id})
			this.uploadInfo = data ?? {}
		},
		onSuccess(response) {
			if(this.maxCount>0 && this.fileList.length>=this.maxCount) return
			if (response[0] != undefined) {
				const { source, name,ext } = response[0]
				if (source != undefined && name != undefined) {
					this.fileList.push({
						id:'',
						name:source,
						url:`${this.uploadInfo.preview}?file=${name}${ext}`
					})
				}
			}
		},
		imageDelete(item,index){
            // this.handleDelete(item)
            this.fileList.splice(index,1)
		},
		uploadImageWebsocket(res){
			let that=this
			const content=JSON.parse(res.context)
			if(!content || !content.length) return
			content.forEach(item=>{
				if(that.maxCount>0 && that.fileList.length>=that.maxCount) return false
				this.fileList.push({
					name:'phone.jpg',
					url:item,
				})
			})
			
		},
	}
	
}

export default UploadMixins;
