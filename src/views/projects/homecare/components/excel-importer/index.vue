<template>
	<el-form class="excel-importer ut-form">
		<div v-if="title" class="title">{{ title }}</div>
		<div class="tool-bar">
			<div class="tool-bar-buttons">
				<slot v-if="diyBtns" name="diyBtns"></slot>
				<el-button v-else :loading="loadingFields" icon="el-icon-download" @click="download">下载模板</el-button>
				<el-upload
					v-if="uploadAction"
					class="uploader"
					:action="uploadAction"
					:multiple="false"
					:show-file-list="false"
					:file-list="fileList"
					:on-change="handleChangeFile"
					:before-upload="()=>uploading=true"
					:on-success="handleUpload"
					:on-error="handleError"
				>
					<el-button
						:loading="uploading" size="small" type="primary" icon="el-icon-upload" :disabled="loadingFields||uploading">
						上传文件
					</el-button>
				</el-upload>
			</div>
			<el-form-item v-if="selectSheet>=0" label="选择工作表：" label-width="100px">
				<el-select v-model="selectSheet">
					<el-option v-for="({sheetName},index) in fileHeaders" :key="sheetName" :label="sheetName" :value="index" />
				</el-select>
			</el-form-item>
			<el-button v-if="selectSheet>=0" :disabled="!canImport" icon="el-icon-plus" type="success" @click="startImport">
				确认导入
			</el-button>
		</div>

		<el-table :data="loadedFields" :height="height" border>
			<el-table-column prop="title" label="标题">
				<template slot-scope="scope">
					<span :class="{'required-title':!!scope.row.required}">{{ scope.row.title }}</span>
				</template>
			</el-table-column>
			<el-table-column label="选择Excel标题">
				<template slot-scope="scope">
					<el-select
						v-if="fileSelections.length" v-model="fileSelections[selectSheet][scope.$index]"
						clearable @clear="clearSelect(scope.$index)">
						<el-option v-for="(col,i) in fileHeaders[selectSheet].colums" :key="i" :label="col" :value="i" />
					</el-select>
					<el-select v-else :placeholder="scope.row.title" disabled :value="null">
						<el-option :value="null" />
					</el-select>
				</template>
			</el-table-column>
		</el-table>

	</el-form>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
	name: 'ExcelImporter',
	props: {
		title:{
			type: String,
			default: '',
		},
		height: {
			type: Number,
			default: 500,
		},
		fieldsApi: {
			type: String,
			default: '',
		},
		headersApi: {
			type: String,
			default: '',
		},
		importApi: {
			type: String,
			default: '',
		},
		extParams: {
			type: Object,
			default: () => ({}),
		},
		diyBtns:{
			type:Boolean,
			default:false,
		}
	},
	data() {
		return {
			loadingFields: false,
			loadedFields: [],
			uploadInfo: {},
			selectSheet: -1,
			fileList: [],
			uploading: false,
			fileUrl: '',
			fileHeaders: [],
			fileSelections: [],
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
			commKey: 'comm/commKey',
		}),
		uploadAction() { return `${this.uploadInfo.url}?token=${this.uploadInfo.token}` },
		canImport() {
			if (!this.loadedFields.length) return false
			for (let i = 0; i < this.loadedFields.length; i++) {
				if (this.loadedFields[i].required && this.fileSelections[this.selectSheet][i] == null) return false
			}
			return true
		},
	},
	beforeMount() {
		this.loadUploadInfo()
		this.loadFields()
	},
	methods: {
		async loadUploadInfo() {
			const { data } = await this.$ut.api('comm.uploadInfo', { commKey: this.commKey })
			this.uploadInfo = data ?? {}
		},
		async loadFields() {
			this.loadingFields = true
			try {
				const { data } = await this.$ut.api(this.fieldsApi)
				this.loadedFields = data ?? []
			} finally {
				this.loadingFields = false
			}
		},
		async download() {
			this.$baseConfirm('是否下载模板，下载后请在浏览器的下载目录寻找对应模板文件', null, async () => {
				this.loadingFields = true
				try {
					const excel = await import('@/common/utils/excel')
					const tHeader = this.loadedFields.map(({ title }) => title)
					excel.export_json_to_excel({
						filename: '模板',
						header: tHeader,
						data: [],
						autoWidth: false,
					})
				} finally {
					this.loadingFields = false
				}
			})
		},
		handleChangeFile(file, list) {
			if (list.length > 0) {
				this.fileList = [list[list.length - 1]]
			}
		},
		handleUpload([file]) {
			this.fileUrl = `${this.uploadInfo.preview}?file=${file?.name}&token=${this.uploadInfo.token}`
			this.loadHeaders()
		},
		handleError(err) {
			console.log(err)
			this.$baseMessage(`上传失败，请重试`, 'error', 'ut-hey-message-error')
		},
		async loadHeaders() {
			let { data } = await this.$ut.api(this.headersApi, { url: this.fileUrl })
			data = data ?? []
			this.fileHeaders = data
			if (data.length > 0) {
				this.selectSheet = 0
				this.fileSelections = this.fileHeaders.map((header) => {
					const cols = header.colums
					return this.loadedFields.map(({ title }) => {
						const idx = cols.indexOf(title)
						return idx >= 0 ? idx : null
					})
				})
			}
			this.uploading = false
		},
		clearSelect(i) {
			this.fileSelections[this.selectSheet][i] = null
		},
		async startImport() {
			const result = {
				url: this.fileUrl,
				sheetIndex: this.selectSheet,
				columns: this.loadedFields.map(({ title }, i) => ({
					title,
					index: this.fileSelections[this.selectSheet][i],
				})),
				communityId: this.comm.id,
				...this.extParams,
			}
			await this.$ut.api(this.importApi, result).then((res)=>{
				this.$emit('success',res)
			})
			
		},
	},
}
</script>

<style scoped lang="scss">
.excel-importer {

	.title{
		padding-bottom: 8px;
		font-weight: bold;
	}

	.tool-bar {
		display: flex;
		gap: $base-margin;
		margin-bottom: $base-margin;

		.tool-bar-buttons {
			display: flex;
			flex: 1;
			gap: $base-margin;
		}

		:deep(.el-form-item) {
			margin-bottom: 0;
		}
	}

	.uploader {
		display: flex;
		align-items: center;
		gap: 10px;

		:deep(.el-list-enter-active),
		:deep(.el-list-leave-active) {
			transition: none;
		}

		:deep(.el-upload-list__item:first-child) {
			margin-top: 0;
		}
	}

	.required-title:after {
		content: "*";
		margin-left: 4px;
		color: #f56c6c;
	}
}
</style>
