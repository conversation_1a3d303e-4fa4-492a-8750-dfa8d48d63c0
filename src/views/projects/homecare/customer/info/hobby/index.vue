<template>
	<div v-loading="listLoading" class="box-wrapper" :style="{height:height+'px'}">
		<div class="box">
			<div class="item">
				<template v-for="(item,index) in dataList">
					<el-checkbox :key="index" v-model="item.checked">{{item.name}}</el-checkbox>
				</template>
			</div>
		</div>
		<div class="footer">
			<el-button type="primary" @click="save">保存爱好</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import Vue from 'vue'
export default {
	name: 'LongCustomerSource',
	components: {
	},
	props: {
		height: {
			type: Number,
			default: () => Vue.prototype.$baseTableHeight(1),
		},
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
		parentData:{
			type: Object,
			default: () => ({}),
		},
		module:{
			type:String,
			default:'',
		},
	},
	data() {
		return {
			listLoading: false,
			dataList:[],
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),

	},
	created() {
		this.fetchData()
	},
	methods: {
		async fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true
			const {data} = await this.$ut.api('homecarecustomer/base/hobby/list', {
				communityId:this.comm.id,
				module:this.module,
            }).finally(()=>{this.listLoading=false})
            this.dataList=data
			this.getHobby()
		},
		async getHobby() {
			this.listLoading = true
			const {data} = await this.$ut.api('homecarecustomer/info/hobby/list', {
				communityId:this.comm.id,
				module:this.module,
				customerId:this.parentData.id,
            }).finally(()=>{this.listLoading=false})
			this.dataList.forEach(item=>{
				let obj=data.find(u=>u.hobbyId==item.id)
				if(obj){
					this.$set(item,'checked',true)
				}
			})
		},
		
		save() {
			const ids=this.dataList.filter(u=>u.checked).map(u=>u.id)
			this.listLoading = true
			this.$ut.api('homecarecustomer/info/hobby/save', {
				communityId:this.comm.id,
				module:this.module,
				customerId:this.parentData.id,
				hobbyIds:ids,
			}).then(()=>{
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')
			}).finally(()=>{this.listLoading = false})

	
		},
	},
}
</script>

<style lang="scss" scoped>
.item{
	font-size: 18px;
}

.box-wrapper{
	display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}

.footer{
	border-top: 1px dashed #ccc;
	padding-top: 16px;
	text-align: center;
}

:deep(.el-checkbox__label){
	font-size: 16px;
    padding: 10px 10px 10px 0;
}

</style>
