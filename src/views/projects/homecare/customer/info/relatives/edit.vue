<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-row>
				<el-col :span="12">							
					<el-form-item label="亲属姓名：" prop="name">
						<el-input v-model.trim="form.name" placeholder="请输入亲属姓名" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="关系：" prop="type">
						<el-select v-model="form.type">
							<el-option label="丈夫" value="丈夫" />
							<el-option label="妻子" value="妻子" />
							<el-option label="儿子" value="儿子" />
							<el-option label="女儿" value="女儿" />
							<el-option label="孙子" value="孙子" />
							<el-option label="孙女" value="孙女" />
							<el-option label="朋友" value="朋友" />
							<el-option label="其他" value="其他" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">		
					<el-form-item label="电话：" prop="phone">
						<el-input v-model.trim="form.phone" placeholder="请输入电话" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="证件号：" prop="idcard">
						<el-input v-model.trim="form.idcard" placeholder="请输入证件号" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="性别：" prop="sex">
						<el-select v-model="form.sex">
							<el-option label="不详" :value="0" />
							<el-option label="男" :value="1" />
							<el-option label="女" :value="2" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="出生日期：" prop="birthday">
						<el-date-picker v-model="form.birthday" type="date" />
					</el-form-item>
				</el-col>
				<el-col :span="12">		
					<el-form-item label="民族：" prop="nation">
						<el-input v-model.trim="form.nation" />
					</el-form-item>
				</el-col>
				<el-col :span="24">		
					<el-form-item label="地址：" prop="address">
						<el-input v-model.trim="form.address" />
					</el-form-item>
				</el-col>
				<el-col :span="24">		
					<el-form-item label="备注：" prop="remark">
						<el-input v-model.trim="form.remark" type="textarea" />
					</el-form-item>
				</el-col>
			</el-row>

		</el-form>


		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'

// import UploadMixins from '../../../components/upload.mixins.js'
export default {
	components:{
	},
	// mixins: [UploadMixins],
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
		parentData: {
			type: Object,
			default: () => ({}),
		},
		module:{
			type:String,
			default:'',
		}
	},
	
	data() {
		return {
			loading: false,
			form: {
				id: '',
				name: '',
				type: '',
				sex: '',
				phone: '',
				idcard:'',
				nation:'',
				address:'',
				imgHead:'',
				birthday:'',
				remark:'',
			},
			rules: {
				name: [{ required: true, trigger: 'blur', message: '请输入亲属姓名' }],
				type: [{ required: true, trigger: 'change', message: '请选择关系' }],
			},

			typeFormVisible:false,
			levelFormVisible:false,
			sourceFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarecustomer/info/relatives/info', {			
				communityId:this.comm.id,
				module:this.module,
				customerId:this.parentData.id,
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
		
		},
		handleSelectType(rows){
			this.typeFormVisible = false
			if (rows && rows[0]) {
				this.form.typeId = rows[0].id
				this.form.typeName = rows[0].name
			}
		},
		handleSelectLevel(rows){
			this.levelFormVisible = false
			if (rows && rows[0]) {
				this.form.levelId = rows[0].id
				this.form.levelName = rows[0].name
			}
		},
		handleSelectSource(rows){
			this.sourceFormVisible = false
			if (rows && rows[0]) {
				this.form.sourceId = rows[0].id
				this.form.sourceName = rows[0].name
			}
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
				await this.$ut.api('homecarecustomer/info/relatives/save', {
					communityId:this.comm.id,
					module:this.module,
					customerId:this.parentData.id,
					...this.form,
				}).finally(()=>{this.loading = false})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.color-box {
	display: inline-block;
	height: 32px;
	width: 150px;
	border: 1px solid #dcdfe6;
	padding-left: 20px;

	.color-box-text {
		color:#fff;
		mix-blend-mode: difference;
	}
}
.placeholder {
	height: 70px;
}

.face-body {
	display: flex;
	justify-content: flex-end;

	.face-top {
		padding-right: 10px;
		flex: 1;
	}

	.face-ewm {
		margin: 0 auto;
		text-align: center;
	}

	.ewm-tips {
		margin-top: 10px;
		font-size: 12px;
		text-align: center;
	}

	.face-bottom {
		text-align: center;

		.img-body {
			height: 100%;
			width: 100%;
		}

		.face-img {
			border: 1px solid #aaa;
			height: 220px;
			width: 90%;
			margin: 0 auto;
			padding: 10px 0;

			&:hover {
				cursor: pointer;
			}

		}


	}
}

el-upload-list__item-thumbnail {
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.disable {
	pointer-events: none;
	color: #e5e5e5;

	:deep(.el-input) {
		color: #e5e5e5;
	}

	:deep(.el-form-item__label) {
		color: #e5e5e5;
	}
}

:deep(.el-upload-list--picture-card) {

	li {
		width: 96px;
		height: 96px;
	}
}

:deep(.el-upload--picture-card) {
	width: 170px;
	height: 210px;
	display: flex;
	align-items: center;
}


:deep(.face-bottom) {
	.el-upload {
		border: 1px solid rgb(170, 170, 170);
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}
}
</style>
