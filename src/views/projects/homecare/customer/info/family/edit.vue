<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-row>
				<el-col :span="24">							
					<el-form-item label="家族史：" prop="name">
						<el-input v-model.trim="form.name" placeholder="请输入家族史" />
					</el-form-item>
				</el-col>
			</el-row>

		</el-form>

		<el-dialog v-if="diseaseFormVisible" v-dialog-drag class="ut-dialog-normal" :visible.sync="diseaseFormVisible" title="疾病选择" append-to-body width="500px" >
			<disease-select :select-window="true" :select-single="true" :height="400" @select="handleSelectDisease" @close="diseaseFormVisible=false"/>
		</el-dialog>

		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
import DiseaseSelect from '../../base/disease'

// import UploadMixins from '../../../components/upload.mixins.js'
export default {
	components:{
		DiseaseSelect,
	},
	// mixins: [UploadMixins],
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
		parentData: {
			type: Object,
			default: () => ({}),
		},
		module:{
			type:String,
			default:'',
		},
	},
	
	data() {
		return {
			loading: false,
			form: {
				id: '',
				diseaseId:'',
				diseaseAllName:'',
				title: '',
				remark:'',
			},
			rules: {
				title: [{ required: true, trigger: 'blur', message: '请输入疾病标题' }],
				diseaseId: [{ required: true, trigger: 'change', message: '请选择所属疾病' }],
			},

			diseaseFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarecustomer/info/family/info', {
				communityId:this.comm.id,
				module:this.module,
				customerId:this.parentData.id,
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
		
		},
		handleSelectDisease(rows){
			this.diseaseFormVisible = false
			if (rows && rows[0]) {
				this.form.diseaseId = rows[0].id
				this.form.diseaseAllName = rows[0].name
			}
		},
		
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
				await this.$ut.api('homecarecustomer/info/family/save', {					
					communityId:this.comm.id,
					module:this.module,
					customerId:this.parentData.id,
					...this.form,
				}).finally(()=>{this.loading = false})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.color-box {
	display: inline-block;
	height: 32px;
	width: 150px;
	border: 1px solid #dcdfe6;
	padding-left: 20px;

	.color-box-text {
		color:#fff;
		mix-blend-mode: difference;
	}
}
.placeholder {
	height: 70px;
}


</style>
