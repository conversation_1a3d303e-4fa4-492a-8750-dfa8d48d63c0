<template>
	<div v-loading="loading">
		<div src="https://apis.map.qq.com/tools/locpicker?search=1&type=1&key=SFABZ-WANWW-FISRY-3IGTF-HV7RE-YSFTI&referer=myapp"></div>
		<el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-row>
				<el-col :span="18">
					<el-row>
						<el-col :span="12">							
							<el-form-item label="姓名：" prop="name">
								<el-input v-model.trim="form.name" placeholder="请输入名称" />
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="出生日期：" prop="birthday">
								<el-date-picker v-model="form.birthday" type="date"  format="yyyy-MM-dd" value-format="yyyy-MM-dd"/>
							</el-form-item>
						</el-col>
						<el-col :span="12">		
							<el-form-item label="电话：" prop="phone">
								<el-input v-model.trim="form.phone" placeholder="请输入电话" />
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="证件号：" prop="idcard">
								<el-input v-model.trim="form.idcard" placeholder="请输入证件号" />
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="服务起时间：" prop="serviceTime">
								<el-date-picker v-model="form.serviceTime" type="date"  format="yyyy-MM-dd" value-format="yyyy-MM-dd" />
							</el-form-item>
						</el-col>
						<el-col :span="12">		
							<el-form-item label="档案号：" prop="archives">
								<el-input v-model.trim="form.archives" />
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="性别：" prop="sex">
								<el-select v-model="form.sex">
									<el-option label="不详" :value="0" />
									<el-option label="男" :value="1" />
									<el-option label="女" :value="2" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="8">		
							<el-form-item label="婚姻：" prop="marriage">
								<el-select v-model="form.marriage">
									<el-option label="未知" value="" />
									<el-option label="未婚" value="未婚" />
									<el-option label="已婚" value="已婚" />
									<el-option label="离异" value="离异" />
									<el-option label="丧偶" value="丧偶" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="8">		
							<el-form-item label="学历：" prop="education">
								<el-select v-model="form.education">
									<el-option label="不详" value="" />
									<el-option label="无学历" value="无学历" />
									<el-option label="小学" value="小学" />
									<el-option label="初中" value="初中" />
									<el-option label="中专" value="中专" />
									<el-option label="大专" value="大专" />
									<el-option label="本科" value="本科" />
									<el-option label="硕士" value="硕士" />
									<el-option label="博士" value="博士" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="12">		
							<el-form-item label="民族：" prop="nation">
								<el-input v-model.trim="form.nation" />
							</el-form-item>
						</el-col>
						<el-col :span="12">		
							<el-form-item label="居住状态：" prop="liveState">
								<el-select v-model="form.liveState">
									<el-option label="未知" value="" />
									<el-option label="独居" value="独居" />
									<el-option label="与配偶/伴侣居住" value="与配偶/伴侣居住" />
									<el-option label="与子女居住" value="与子女居住" />
									<el-option label="与父母居住" value="与父母居住" />
									<el-option label="与兄弟姐妹居住" value="与兄弟姐妹居住" />
									<el-option label="与其他亲属居住" value="与其他亲属居住" />
									<el-option label="与非亲属关系居住" value="与非亲属关系居住" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="12">		
							<el-form-item label="失能等级：" prop="disabilityLevelId">
								<!-- <el-select v-model="form.disabilityLevel">
									<el-option label="未知" value="" />
									<el-option label="能力完好" value="能力完好" />
									<el-option label="轻度失能" value="轻度失能" />
									<el-option label="中度失能" value="中度失能" />
									<el-option label="重度失能" value="重度失能" />
									<el-option label="完全失能" value="完全失能" />
								</el-select> -->
								<el-input v-model.trim="form.disabilityLevelName" placeholder="请选择失能等级" @focus="disabilityLevelFormVisible=true">
									<template slot="append">
										<i class="el-icon-search" @click="disabilityLevelFormVisible=true"></i>
									</template>
								</el-input>
							</el-form-item>
						</el-col>
						<el-col :span="12">		
							<el-form-item label="政治面貌：" prop="politics">
								<el-select v-model="form.politics">
									<el-option label="未知" value="" />
									<el-option label="群众" value="群众" />
									<el-option label="共青团员" value="共青团员" />
									<el-option label="中共党员" value="中共党员" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="12">		
							<el-form-item label="方言：" prop="dialect">
								<el-input v-model.trim="form.dialect" />
							</el-form-item>
						</el-col>
						
						<el-col :span="12">		
							<el-form-item label="宗教信仰：" prop="religion">
								<el-select v-model="form.religion">
									<el-option label="未知" value="" />
									<el-option label="佛教" value="佛教" />
									<el-option label="道教" value="道教" />
									<el-option label="天主教" value="天主教" />
									<el-option label="基督教" value="基督教" />
									<el-option label="伊斯兰教" value="伊斯兰教" />
									<el-option label="其他" value="其他" />
								</el-select>
							</el-form-item>
						</el-col>
						
						<el-col :span="24">		
							<el-form-item label="地址：" prop="address">
								<el-input v-model.trim="form.address" />
							</el-form-item>
						</el-col>						
					</el-row>
					<el-row>
							<el-col :span="10">
								<el-form-item label="经度" prop="longitude">
									<el-input v-model.trim="form.longitude" />
								</el-form-item>
							</el-col>
							<el-col :span="10">
								<el-form-item label="纬度" prop="latitude">
									<el-input v-model.trim="form.latitude" />
								</el-form-item>
							</el-col>
							<el-col :span="4">
								<el-button @click="qqmapDialog=true">坐标拾取</el-button>
							</el-col>
							<div class="location-address">{{form.locationAddress}}</div>			
					</el-row>
					<el-row>
						
						<el-col :span="24">		
							<el-form-item label="备注：" prop="remark">
								<el-input v-model.trim="form.remark" type="textarea" />
							</el-form-item>
						</el-col>
					</el-row>
				</el-col>
				<el-col :span="6">
					<div class="face-body">
						<!-- <div class="face-top">
							<div class="face-ewm"><img class="upload-image" :src="uploadImage" alt="" /></div>
							<div class="ewm-tips">微信扫一扫，协同操作</div>
						</div> -->
						<div class="face-bottom">
							<ut-media :url-list="fileList" :width="172" :height="212" :show-title="false" @delete="imageDelete">
								<template v-if="!fileList.length" slot="add">
									<el-upload
										ref="upload" :action="uploadAction" :show-file-list="false" list-type="picture-card"
										:file-list="fileList" :on-success="onSuccess">
										<!-- <i slot="default" class="el-icon-plus"></i> -->
										<img class="img-body" :src="faceUrl" alt="" />
									</el-upload>
								</template>
							</ut-media>

							<div style="margin: 8px 0 0 0; display: flex">
								<el-button
									style="height: 28px" :disabled="!fileList.length" type="primary" size="mini"
									icon="el-icon-delete" @click="clearImage">删除
								</el-button>
							</div>

						</div>
					</div>
					
				</el-col>
	
			</el-row>
			
			<el-divider />
			<el-row :gutter="20">
				<el-col :span="12">
					<el-form-item label="类型：" prop="typeId">
						<el-input v-model.trim="form.typeName" placeholder="请选择类型" @focus="typeFormVisible=true">
							<template slot="append">
								<i class="el-icon-search" @click="typeFormVisible=true"></i>
							</template>
						</el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="护理级别：" prop="levelId">
						<el-input v-model.trim="form.levelName" placeholder="请选择级别" @focus="levelFormVisible=true">
							<template slot="append">
								<i class="el-icon-search" @click="levelFormVisible=true"></i>
							</template>
						</el-input>
					</el-form-item>
				</el-col>
				
				<el-col :span="12">
					<el-form-item label="来源：" prop="sourceId">
						<el-input v-model.trim="form.sourceName" placeholder="请选择来源" @focus="sourceFormVisible=true">
							<template slot="append">
								<i class="el-icon-search" @click="sourceFormVisible=true"></i>
							</template>
						</el-input>
					</el-form-item>
				</el-col>

				<el-col :span="12">
					<el-form-item label="医保类型：" prop="insuranceTypeId">
						<el-input v-model.trim="form.insuranceTypeName" placeholder="请选择医保类型" @focus="insuranceFormVisible=true">
							<template slot="append">
								<i class="el-icon-search" @click="insuranceFormVisible=true"></i>
							</template>
						</el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>

		<!-- 类型 -->
		<el-dialog v-if="typeFormVisible" v-dialog-drag class="ut-dialog-normal" :visible.sync="typeFormVisible" title="类型选择" append-to-body width="600px" >
			<type-select :select-window="true" :select-single="true" :height="400" @select="handleSelectType" @close="typeFormVisible=false"/>
		</el-dialog>

		<!-- 级别 -->
		<el-dialog v-if="levelFormVisible" v-dialog-drag class="ut-dialog-normal" :visible.sync="levelFormVisible" title="级别选择" append-to-body width="600px" >
			<level-select :select-window="true" :select-single="true" :height="400" @select="handleSelectLevel" @close="levelFormVisible=false"/>
		</el-dialog>

		<!-- 来源 -->
		<el-dialog v-if="sourceFormVisible" v-dialog-drag class="ut-dialog-normal" :visible.sync="sourceFormVisible" title="来源选择" append-to-body width="600px" >
			<source-select :select-window="true" :select-single="true" :height="400" @select="handleSelectSource" @close="sourceFormVisible=false"/>
		</el-dialog>

		<!-- 医保类型 -->
		<el-dialog v-if="insuranceFormVisible" v-dialog-drag class="ut-dialog-normal" :visible.sync="insuranceFormVisible" title="医保类型选择" append-to-body width="600px" >
			<insurance-select :select-window="true" :select-single="true" :height="400" @select="handleSelectInsurance" @close="insuranceFormVisible=false"/>
		</el-dialog>
		
		<!-- 失能等级 -->
		<el-dialog v-if="disabilityLevelFormVisible" v-dialog-drag class="ut-dialog-normal" :visible.sync="disabilityLevelFormVisible" title="失能等级" append-to-body width="700px" >
			<disability-level :select-window="true" :select-single="true" :height="400" @select="handleDisabilityLevel" @close="disabilityLevelFormVisible=false"/>
		</el-dialog>


		<el-dialog  v-if="qqmapDialog" v-dialog-drag  append-to-body :close-on-click-modal="true" title="坐标拾取" top="3vh" :visible.sync="qqmapDialog" width="1000px">
			<div style="width:100%;height:800px">
				<qq-map :map-key="qqmap.mapKey" :key-name="qqmap.keyName" :lng="form.longitude" :lat="form.latitude" @callback="callback"  @close="qqmapDialog = false"/>
			</div>
		</el-dialog>


		<div class="ut-edit-footer">
			<el-button type="primary" :disabled="!isEdit" @click="save" >{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
import TypeSelect from '../base/type'
import LevelSelect from '../base/level'
import SourceSelect from '../base/source'
import InsuranceSelect from '../base/insurance'
import DisabilityLevel from '../base/disabilityLevel'
import QqMap from '@/views/components/qq-map' 
import UploadMixins from '../../components/upload.mixins.js'
export default {
	components:{
		QqMap,
		TypeSelect,
		LevelSelect,
		SourceSelect,
		InsuranceSelect,
		DisabilityLevel,
	},
	mixins: [UploadMixins],
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
		module:{
			type:String,
			default:'',
		}
	},
	
	data() {
		return {
			loading: false,
			originalData:{},
			form: {
				id: '',
				name: '',
				sex:0,
				phone:'',
				idcard:'',
				birthday:'',
				nation:'',
				education:'',
				marriage:'',
				liveState:'',
				disabilityLevelId:'',
				politics:'',
				dialect:'',
				religion:'',
				archives:'',
				address:'',
				longitude:'',
				latitude:'',
				remark:'',
				typeId:'',
				levelId:'',
				sourceId:'',
				insuranceTypeId:'',
			},
			faceUrl: require('../../static/personface.jpg'),
			rules: {
				name: [{ required: true, trigger: 'blur', message: '请输入名称' }],
				disabilityLevelId:[{ required: true, trigger: ['blur','change'], message: '请选择失能等级' }],
			},

			typeFormVisible:false,
			levelFormVisible:false,
			sourceFormVisible:false,
			insuranceFormVisible:false,
			disabilityLevelFormVisible:false,
			qqmapDialog:false,
			qqmap:{
				mapKey:'SFABZ-WANWW-FISRY-3IGTF-HV7RE-YSFTI',
				keyName:'myapp',
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
		isEdit(){
			if(JSON.stringify(this.originalData)!= JSON.stringify(this.form)){
				return true
			}else{
				return false
			}
		}
	},
	beforeMount(){

		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarecustomer/info/info', {
				communityId:this.comm.id,
				module:this.module,
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
			if (data.imgHead) {
				this.fileList = [{url: data.imgHead}]
			}
			this.$set(this.form,'disabilityLevelId',data.disabilityLevelId?data.disabilityLevelId:'')	
			this.$set(this.form,'typeId',data.typeId?data.typeId:'')	
			this.$set(this.form,'levelId',data.levelId?data.levelId:'')	
			this.$set(this.form,'sourceId',data.sourceId?data.sourceId:'')	
			this.$set(this.form,'insuranceTypeId',data.insuranceTypeId?data.insuranceTypeId:'')	
			this.originalData={...this.form}
		},
		clearImage() {
			this.fileList = []
		},
		handleSelectType(rows){
			this.typeFormVisible = false
			if (rows && rows[0]) {
				this.form.typeId = rows[0].id
				this.form.typeName = rows[0].name
			}
		},
		handleSelectLevel(rows){
			this.levelFormVisible = false
			if (rows && rows[0]) {
				this.form.levelId = rows[0].id
				this.form.levelName = rows[0].name
			}
		},
		handleSelectSource(rows){
			this.sourceFormVisible = false
			if (rows && rows[0]) {
				this.form.sourceId = rows[0].id
				this.form.sourceName = rows[0].name
			}
		},
		handleSelectInsurance(rows){
			this.insuranceFormVisible = false
			if (rows && rows[0]) {
				this.form.insuranceTypeId = rows[0].id
				this.form.insuranceTypeName = rows[0].name
			}
		},
		handleDisabilityLevel(rows){
			this.disabilityLevelFormVisible=false
			if (rows && rows[0]) {
				this.form.disabilityLevelId = rows[0].id
				this.form.disabilityLevelName = rows[0].name
			}
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true

				await this.$ut.api('homecarecustomer/info/save', {
					communityId:this.comm.id,
					module:this.module,
					...this.form,
					imgHead:this.fileList.length?this.fileList[0].url:'',
				}).finally(()=>{this.loading = false})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
		callback(loc){
			this.form.latitude=loc.latlng.lat
			this.form.longitude=loc.latlng.lng
			this.form.locationAddress=loc.poiaddress
		},
	},
}
</script>
<style lang="scss" scoped>
.color-box {
	display: inline-block;
	height: 32px;
	width: 150px;
	border: 1px solid #dcdfe6;
	padding-left: 20px;

	.color-box-text {
		color:#fff;
		mix-blend-mode: difference;
	}
}
.placeholder {
	height: 70px;
}

.face-body {
	display: flex;
	justify-content: flex-end;

	.face-top {
		padding-right: 10px;
		flex: 1;
	}

	.face-ewm {
		margin: 0 auto;
		text-align: center;
	}

	.ewm-tips {
		margin-top: 10px;
		font-size: 12px;
		text-align: center;
	}

	.face-bottom {
		text-align: center;

		.img-body {
			height: 100%;
			width: 100%;
		}

		.face-img {
			border: 1px solid #aaa;
			height: 220px;
			width: 90%;
			margin: 0 auto;
			padding: 10px 0;

			&:hover {
				cursor: pointer;
			}

		}


	}
}

el-upload-list__item-thumbnail {
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.disable {
	pointer-events: none;
	color: #e5e5e5;

	:deep(.el-input) {
		color: #e5e5e5;
	}

	:deep(.el-form-item__label) {
		color: #e5e5e5;
	}
}

:deep(.el-upload-list--picture-card) {

	li {
		width: 96px;
		height: 96px;
	}
}

:deep(.el-upload--picture-card) {
	width: 170px;
	height: 210px;
	display: flex;
	align-items: center;
}


:deep(.face-bottom) {
	.el-upload {
		border: 1px solid rgb(170, 170, 170);
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}
}

.location-address{
	position: absolute;
    margin-top: 32px;
    margin-left: 110px;
    color: gray;
    font-size: 12px;
}
</style>
