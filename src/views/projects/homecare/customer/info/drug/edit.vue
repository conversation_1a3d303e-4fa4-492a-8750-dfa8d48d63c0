<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-row>
				<el-col :span="12">
					<el-form-item label="药名：" prop="name">
						<el-input v-model.trim="form.name" placeholder="请输入药名" />
					</el-form-item>
				</el-col>
				<el-col :span="12">	
					<el-form-item label="条码：" prop="barCode">
						<el-input v-model.trim="form.barCode" placeholder="请输入条码" />
					</el-form-item>
				</el-col>
				<el-col :span="12">	
					<el-form-item label="频率：" prop="dayCount">
						<el-input v-model.trim="form.dayCount" placeholder="几天吃药" />
					</el-form-item>
				</el-col>
				<el-col :span="12">		
					<el-form-item label="每天次数：" prop="timesCount">
						<el-input v-model.trim="form.timesCount" placeholder="1天吃几次" />
					</el-form-item>
				</el-col>
				
				<el-col :span="12">
					<el-form-item label="药品图片：">
						<ut-media :url-list="fileList" :width="120" :height="120" :class="{lock}" @delete="imageDelete">
							<template v-if="!fileList.length" slot="add">
								<el-upload
									ref="upload" :action="uploadAction" :show-file-list="false" list-type="picture-card"
									:file-list="fileList" :on-success="onSuccess" :disabled="lock">
									<i slot="default" class="el-icon-plus"></i>
								</el-upload>
							</template>
						</ut-media>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<div class="qr">
						<img class="img" :src="uploadImage" alt=""/>
						<div class="text">
							<div>使用微信扫一扫</div>
							<div class="bold">协同操作(限本人账号)</div>
						</div>
					</div>
				</el-col>
			</el-row>

		</el-form>


		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'

import UploadMixins from '../../../components/upload.mixins.js'
export default {
	components:{
	},
	mixins: [UploadMixins],
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
		lock: {
			type: Boolean,
			default: false,
		},
		parentData: {
			type: Object,
			default: () => ({}),
		},
		module:{
			type:String,
			default:'',
		},
	},
	
	data() {
		return {
			loading: false,
			form: {
				id: '',
				name: '',
				barCode: '',
				dayCount: '1',
				timesCount: '',
				imgHead:'',
			},
			rules: {
				name: [{ required: true, trigger: 'blur', message: '请输入药名' }],
			},

			typeFormVisible:false,
			levelFormVisible:false,
			sourceFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			
			const { data } = await this.$ut.api('homecarecustomer/info/drug/info', {
				communityId:this.comm.id,
				module:this.module,
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
			if (data.imgHead) this.fileList = [{ url: data.imgHead }]
		},
		
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
				
				await this.$ut.api('homecarecustomer/info/drug/save', {
					communityId:this.comm.id,
					module:this.module,
					customerId:this.parentData.id,
					...this.form,
					imgHead: this.fileList.length ? this.fileList[0].url : '',
				}).finally(()=>{this.loading = false})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.color-box {
	display: inline-block;
	height: 32px;
	width: 150px;
	border: 1px solid #dcdfe6;
	padding-left: 20px;

	.color-box-text {
		color:#fff;
		mix-blend-mode: difference;
	}
}
:deep(.el-upload--picture-card) {
	width: 120px;
	height: 120px;
	line-height: 120px;

}
.qr {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;

	.img {
		width: 100px;
	}

	.text {
		flex: 1;
		text-align: center;
	}

	.bold {
		font-weight: bold;
	}
}

.placeholder {
	height: 70px;
}

.lock {
	:deep(.el-upload--picture-card), :deep(.viewer-delete) {
		display: none !important;
	}
}

.txt-2{
	margin-bottom: 4px !important;
}


</style>
