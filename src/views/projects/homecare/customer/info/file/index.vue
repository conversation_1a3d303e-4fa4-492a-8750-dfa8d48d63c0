<template>
	<div class="table-container" :class="{ 'ut-fullscreen': fullscreen }">
		<div style="margin-bottom: 10px;">
			<el-button @click="handleAdd">增加附件</el-button>
		</div>
		<div class="list-box">
			<template v-for="(item,index) in dataList.info">
				<div :key="index" class="list-item">
					
					<ut-media :url-list="[{url:item.url}]" :width="160" :height="120" />
					<div>
						<div class="title">{{ item.title }}</div>
					</div>
					<div class="close" @click="handleDelete(item)">删除</div>
				</div>
			</template>
		</div>
		<div class="qr">
			<!-- <img class="img" :src="uploadImage" alt=""/> -->
			<div class="text">
				<div>使用微信扫一扫</div>
				<div class="bold">协同操作(限本人账号)</div>
			</div>
		</div>

		<el-dialog	v-if="dialogFormVisible" v-dialog-drag class="ut-dialog-normal" :title="selectRow.id?'附件编辑':'增加附件'" :visible.sync="dialogFormVisible" append-to-body width="600px" :destroy-on-close="true" :close-on-click-modal="false" :close-on-press-escape="false"	>
			<edit :parent-data="parentData" :init-data="selectRow" :module="module" @fetchData="fetchData" @close="dialogFormVisible=false" />
		</el-dialog>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import Vue from 'vue'
import Edit from './edit'
// import UploadMixins from '../../../../components/upload.mixins.js'

export default {
	components:{
		Edit,
	},
	// mixins: [UploadMixins],
	props: {
		height: {
			type: Number,
			default: () => Vue.prototype.$baseTableHeight(1),
		},
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
		parentData:{
			type: Object,
			default: () => ({}),
		},
		module:{
			type:String,
			default:'',
		},
	},
	data() {
		return {
			listLoading: false,
			fullscreen: false,
			
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 20,
				order: '',
			},
			dialogFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	created() {
		this.fetchData()
	},
	methods: {
		async fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true
			const {data} = await this.$ut.api('homecarecustomer/info/file/listpg', {
				communityId:this.comm.id,
				module:this.module,
				customerId:this.parentData.id,
                ...this.page,
            }).finally(()=>{this.listLoading=false})
            this.dataList=data
		},
		handleSelect(rows) {
			this.$emit('select', rows)
		},
		onFullscreen(v) {
			this.fullscreen = v
		},
		onSelectRows(rows) {
			this.selectRows = rows
		},
		handleAdd() {
			this.selectRow = {}
			this.dialogFormVisible = true
		},
		handleEdit(row) {
			this.selectRow = row
			this.dialogFormVisible = true
		},
		handleDelete(row) {
			let ids = []
			if (row.id) {
				ids = [row.id]
			} else {
				if (this.selectRows.length > 0) {
					ids = this.selectRows.map((item) => item.id)
				} else {
					this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
					return
				}
			}
			console.log(ids)
			this.$baseConfirm('你确定要删除吗', null, async () => {
				
				this.$ut.api('homecarecustomer/info/file/delete', {
                    communityId: this.comm.id,
					module:this.module,
					customerId:this.parentData.id,
                    ids: ids,
                }).then(() => {
                    this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
                    this.fetchData()
                })
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.color-box {
	display: flex;
	align-items: center;
	justify-content: center;

	.color-preview {
		width: 20px;
		height: 20px;
		margin-right: 10px;
        border-radius: 2px;
	}
}

.list-box{
	display: flex;
}
.list-item{
	width:160px;
	height: 160px;	
	display: flex;
    flex-direction: column;
    align-items: center;
	border: 1px dashed #a0a0a0;
	border-radius: 4px;
	overflow: hidden;
	margin: 0 10px 10px 0;
	position: relative;
	&:last-child{
		margin-right: 0;
	}

	.image{
		height: 120px;
	}

	.title{
		line-height: 2;
	}

	&:hover .close{
		display: block;
	}

}

.close{
	position: absolute;
	right: 0;
	top: 0;
	padding: 4px;
	display: none;
	cursor: pointer;
	color: #fff;
}

.qr {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;

	.img {
		width: 100px;
	}

	.text {
		flex: 1;
		text-align: center;
	}

	.bold {
		font-weight: bold;
	}
}

</style>
