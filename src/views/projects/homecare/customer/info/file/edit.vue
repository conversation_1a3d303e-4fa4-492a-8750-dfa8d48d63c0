<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-row>
				<el-col :span="24">							
					<el-form-item label="标题：" prop="title">
						<el-input v-model.trim="form.title" placeholder="请输入名称" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="附件：">
						<ut-media :url-list="fileList" :class="{lock}" @delete="imageDelete">
							<template v-if="!fileList.length" slot="add">
								<el-upload
									ref="upload" :action="uploadAction" :show-file-list="false" list-type="picture-card"
									:file-list="fileList" :on-success="onSuccess" :disabled="lock">
									<i slot="default" class="el-icon-plus"></i>
								</el-upload>
							</template>
						</ut-media>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<div class="qr">
						<img class="img" :src="uploadImage" alt=""/>
						<div class="text">
							<div>使用微信扫一扫</div>
							<div class="bold">协同操作(限本人账号)</div>
						</div>
					</div>
				</el-col>
				<el-col :span="24">							
					<el-form-item label="备注：" prop="remark">
						<el-input v-model.trim="form.remark" type="textarea" />
					</el-form-item>
				</el-col>
			</el-row>

		</el-form>


		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'

import UploadMixins from '../../../components/upload.mixins.js'
export default {
	components:{
	},
	mixins: [UploadMixins],
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
		parentData: {
			type: Object,
			default: () => ({}),
		},
		lock: {
			type: Boolean,
			default: false,
		},
		module:{
			type:String,
			default:'',
		},
	},
	
	data() {
		return {
			loading: false,
			form: {
				id: '',
				title: '',
				url: '',
				remark:'',
			},
			rules: {
				title: [{ required: true, trigger: 'blur', message: '请输入标题' }],
			},

			typeFormVisible:false,
			levelFormVisible:false,
			sourceFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarecustomer/info/file/info', {
				communityId:this.comm.id,
				module:this.module,
				customerId:this.parentData.id,
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
		
		},
		handleSelectType(rows){
			this.typeFormVisible = false
			if (rows && rows[0]) {
				this.form.typeId = rows[0].id
				this.form.typeName = rows[0].name
			}
		},
		handleSelectLevel(rows){
			this.levelFormVisible = false
			if (rows && rows[0]) {
				this.form.levelId = rows[0].id
				this.form.levelName = rows[0].name
			}
		},
		handleSelectSource(rows){
			this.sourceFormVisible = false
			if (rows && rows[0]) {
				this.form.sourceId = rows[0].id
				this.form.sourceName = rows[0].name
			}
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
				await this.$ut.api('homecarecustomer/info/file/save', {
					communityId:this.comm.id,
					module:this.module,
					customerId:this.parentData.id,
					...this.form,
					url:this.fileList.length ? this.fileList[0].url : '',
				}).finally(()=>{this.loading = false})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.color-box {
	display: inline-block;
	height: 32px;
	width: 150px;
	border: 1px solid #dcdfe6;
	padding-left: 20px;

	.color-box-text {
		color:#fff;
		mix-blend-mode: difference;
	}
}
.placeholder {
	height: 70px;
}


</style>
