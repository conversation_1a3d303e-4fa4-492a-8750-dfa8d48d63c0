<template>
	<div v-loading="loading" class="ut-body">
        <div class="info-box">
            <el-row>
                <el-col :span="8">
                    <span>姓名：</span>
                    <span>{{ form.name }}</span>
                </el-col>
                <el-col :span="8">
                    <span>身份证：</span>
                    <span>{{ form.idcard }}</span>
                </el-col>
                <el-col :span="8">
                    <span>电话：</span>
                    <span>{{ form.phone }}</span>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <span>性别：</span>
                    <span v-if="form.sex==0">不详</span>
                    <span v-if="form.sex==1">男</span>
                    <span v-if="form.sex==2">女</span>
                </el-col>
                <el-col :span="8">
                    <span>出生日期：</span>
                    <span>{{ form.birthday }}</span>
                </el-col>
                <el-col :span="8">
                    <span>民族：</span>
                    <span>{{ form.nation }}</span>
                </el-col>
            </el-row>
        </div>
        <div>
            <el-tabs v-model="tabName" type="border-card" tab-position="left" style="height: 600px;">
                <el-tab-pane name="relatives">
                    <span slot="label">
                        <i class="el-icon-set-up"></i>
                        亲属
                    </span>
                    <relatives v-if="tabName=='relatives'" :parent-data="initData" :module="module" :height="490" />
                </el-tab-pane>
                <el-tab-pane name="disease">
                    <span slot="label">
                        <i class="el-icon-set-up"></i>
                        疾病
                    </span>
                    <disease v-if="tabName=='disease'" :parent-data="initData" :module="module" :height="490" />
                </el-tab-pane>
                <el-tab-pane name="drug">
                    <span slot="label">
                        <i class="el-icon-set-up"></i>
                        用药
                    </span>
                    <drug v-if="tabName=='drug'" :parent-data="initData" :module="module" :height="490" />
                </el-tab-pane>
                <el-tab-pane name="food">
                    <span slot="label">
                        <i class="el-icon-set-up"></i>
                        饮食
                    </span>
                    <food v-if="tabName=='food'" :parent-data="initData" :module="module" :height="490" />
                </el-tab-pane>
                <el-tab-pane name="symptom" class="ut-fullheight">
                    <span slot="label">
                        <i class="el-icon-set-up"></i>
                        症状
                    </span>
                    <symptom v-if="tabName=='symptom'" :parent-data="initData" :module="module" :height="570" />
                </el-tab-pane>
                <el-tab-pane name="hobby">
                    <span slot="label">
                        <i class="el-icon-set-up"></i>
                        爱好
                    </span>
                    <hobby v-if="tabName=='hobby'" :parent-data="initData" :module="module" :height="570" />
                </el-tab-pane>
                <el-tab-pane name="dataSet">
                    <span slot="label">
                        <i class="el-icon-set-up"></i>
                        资料
                    </span>
                    <data-set v-if="tabName=='dataSet'" :parent-data="initData" :module="module" :height="570" />
                </el-tab-pane>
                <el-tab-pane name="file">
                    <span slot="label">
                        <i class="el-icon-set-up"></i>
                        附件
                    </span>
                    <file v-if="tabName=='file'" :parent-data="initData" :module="module" :height="490" />
                </el-tab-pane>
                <el-tab-pane name="allergy">
                    <span slot="label">
                        <i class="el-icon-set-up"></i>
                        过敏史
                    </span>
                    <allergy v-if="tabName=='allergy'" :parent-data="initData" :module="module" :height="570" />
                </el-tab-pane>
                <el-tab-pane name="family">
                    <span slot="label">
                        <i class="el-icon-set-up"></i>
                        家族史
                    </span>
                    <family v-if="tabName=='family'" :parent-data="initData" :module="module" :height="570" />
                </el-tab-pane>
                <el-tab-pane name="past">
                    <span slot="label">
                        <i class="el-icon-set-up"></i>
                        既往史
                    </span>
                    <past v-if="tabName=='past'" :parent-data="initData" :module="module" :height="570" />
                </el-tab-pane>
            </el-tabs>
        </div>
		



	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'

import Relatives from './relatives'
import Disease from './disease'
import Drug from './drug'
import Food from './food'
import File from './file'
import Symptom from './symptom'
import Hobby from './hobby'
import DataSet from './data-set'
import Allergy from './allergy'
import Family from './family'
import Past from './past'

export default {
	components:{
        Relatives,
        Disease,
        Drug,
        Food,
        File,
        Symptom,
        Hobby,
        DataSet,
        Allergy,
        Family,
        Past,
	},
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},		
		module:{
			type:String,
			default:'',
		}
	},
	
	data() {
		return {
			loading: false,
            tabName:'relatives',
			form: {
				id: '',
				name: '',
				code: '',
				color: '',
				showIndex: '',
				valid:true,
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarecustomer/info/info', {
				communityId:this.comm.id,
                module:this.module,
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
		
		},
	},
}
</script>
<style lang="scss" scoped>
.info-box{
    line-height: 30px;
    padding: 0 10px 10px 10px;
}


</style>
