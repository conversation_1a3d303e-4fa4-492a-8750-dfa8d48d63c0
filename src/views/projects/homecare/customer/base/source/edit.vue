<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-form-item label="名称：" prop="name">
				<el-input v-model.trim="form.name" placeholder="请输入名称" />
			</el-form-item>
			<el-form-item label="编号：" prop="code">
				<el-input v-model.trim="form.code" placeholder="请输入编号" />
			</el-form-item>
			<el-row>
				<!-- <el-col :span="16">
					<el-form-item :label="lang('颜色')+'：'" prop="color">
						<div style="display: flex">
							<div class="color-box" :style="{ backgroundColor: form.color }">
								<span class="color-box-text">
									{{ form.color || '点击选择颜色→' }}
								</span>
							</div>
							<el-color-picker v-model="form.color" :predefine="preColors" />
						</div>
					</el-form-item>
				</el-col> -->
				<el-col :span="12">
					<el-form-item label="显示索引：" prop="showIndex">
						<el-input v-model.trim="form.showIndex" />
					</el-form-item>
				</el-col>
			</el-row>
			
			

			<el-form-item label="启用：" prop="valid">
				<el-switch v-model="form.valid" />
			</el-form-item>

		</el-form>
		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
import ColorMixins from '../../../components/color.mixins'
export default {
	
	mixins:[ColorMixins],
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			loading: false,
			form: {
				id: '',
				name: '',
				code: '',
				color: '',
				showIndex: '',
				valid:true,
			},
			rules: {
				name: [{ required: true, trigger: 'blur', message: '请输入名称' }],
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarecustomer/base/source/info', {
				communityId:this.comm.id,
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
		
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
		
				await this.$ut.api('homecarecustomer/base/source/save', {
					communityId:this.comm.id,
					...this.form,
				})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.color-box {
	display: inline-block;
	height: 32px;
	width: 150px;
	border: 1px solid #dcdfe6;
	padding-left: 20px;

	.color-box-text {
		color:#fff;
		mix-blend-mode: difference;
	}
}
.placeholder {
	height: 70px;
}
</style>
