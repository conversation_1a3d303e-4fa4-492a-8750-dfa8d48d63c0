<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="100"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			table-name="house-type"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button">
				<el-button icon="el-icon-plus" type="primary" @click="handleAdd">添加</el-button>
				<el-button icon="el-icon-delete" type="danger" :disabled="!selectRows.length" @click="handleDelete($event)">删除</el-button>
			</template>
			<template #op="{ row }">
				<el-button type="text" @click="handleEdit(row)">编辑</el-button>
				<el-button type="text" @click="handleDelete(row)">删除</el-button>
			</template>
			<template #cell="{ row, item }">
				<div v-if="item.prop === 'color'" class="color-box">
					<span class="color-preview" :style="{ backgroundColor: row[item.prop] }"></span>
					{{ row[item.prop] }}
				</div>
				<span v-else>{{ row[item.prop] }}</span>
			</template>
		</form-list>
		<el-dialog
			v-if="dialogFormVisible" v-dialog-drag :title="selectRow.id?'类型编辑':'增加类型'"
			append-to-body width="500px" :destroy-on-close="true" :close-on-click-modal="false" :close-on-press-escape="false"
			:visible.sync="dialogFormVisible" @close="dialogFormVisible=false">
			<edit :init-data="selectRow" @fetchData="fetchData" @close="dialogFormVisible=false" />
		</el-dialog>
	</div>
</template>

<script>
import Edit from './edit'
import FormList from '@/views/components/form-list'
import { mapGetters } from 'vuex'
import Vue from 'vue'
export default {
	name: 'Type',
	components: {
		Edit,
		FormList,
	},
	props: {
		height: {
			type: Number,
			default: () => Vue.prototype.$baseTableHeight(1),
		},
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			listLoading: false,
			fullscreen: false,
			columns: [
				{
					label: '名称',
					align: 'left',
					prop: 'name',
					sortable: false,
					disableCheck: true,
					show: true,
				},
				{
					align: 'center',
					label: '颜色',
					prop: 'color',
					sortable: false,
					show: true,
				},
				{
					label: '备注',
					prop: 'remark',
					width: 'auto',
					align: 'left',
					show: true,
				},
			],
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 20,
				order: '',
			},
			selectRows: [],
			selectRow: {},
			dialogFormVisible: false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	created() {
		this.fetchData()
	},
	methods: {
		async fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true
			const {data} = await this.$ut.api('homecarecustomer/base/type/listpg', {
				communityId:this.comm.id,
                ...this.page,
            }).finally(()=>{this.listLoading=false})
            this.dataList=data
		},
		handleSelect(rows) {
			this.$emit('select', rows)
		},
		onFullscreen(v) {
			this.fullscreen = v
		},
		onSelectRows(rows) {
			this.selectRows = rows
		},
		handleAdd() {
			this.selectRow = {}
			this.dialogFormVisible = true
		},
		handleEdit(row) {
			this.selectRow = row
			this.dialogFormVisible = true
		},
		handleDelete(row) {
			let ids = []
			if (row.id) {
				ids = [row.id]
			} else {
				if (this.selectRows.length > 0) {
					ids = this.selectRows.map((item) => item.id)
				} else {
					this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
					return
				}
			}
			this.$baseConfirm('你确定要删除吗', null, async () => {
				this.$ut
					.api('homecarecustomer/base/type/delete', {
						communityId:this.comm.id,
						ids: ids,
					})
					.then(() => {
						this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
						this.fetchData()
					})
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.color-box {
	display: flex;
	align-items: center;
	justify-content: center;
	.color-preview {
		width: 30px;
		height: 30px;
		margin-right: 10px;
	}
}
</style>
