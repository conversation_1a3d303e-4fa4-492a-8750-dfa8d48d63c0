<template>
	<div class="ut-fullscreen'">
		<div v-if="showTool" style="margin-bottom:10px">
            <el-button type="primary"  @click="treeAdd">增加</el-button>
            <el-button type="primary" :disabled="!treeSelectNode.id" @click="treeEdit">修改</el-button>
            <el-button type="primary" :disabled="!treeSelectNode.id" @click="treeDelete">删除</el-button>
		</div>
		<div v-loading="loading">
			<ut-tree :tree="treeData" :expanded-keys="expandedKeys" @nodeClick="treeNodeClick">
				<template #icon></template>
			</ut-tree>
		</div>
        <el-dialog v-if="dialogFormVisible" v-dialogDrag append-to-body :close-on-click-modal="false" :title="title" :visible.sync="dialogFormVisible" width="500px">
            <tree-edit :init-data="currNode" @fetchData="getTreeData" @close="dialogFormVisible=false" />
        </el-dialog>
	</div>
</template>

<script>

import { mapGetters } from 'vuex'
import TreeEdit from './tree-edit.vue'

export default {
	components: {
		TreeEdit,
	},
	props: {
		showTool:{
			type:Boolean,
			default:false,
		}
	},
	data() {
		return {
            type:'',
			treeData: [],
			loading: false,
			currNode:{},
			treeSelectNode: {},
			dialogFormVisible:false,
			expandedKeys:[],
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
		showEditAndDelete() {
			if (!this.treeSelectNode.id) return false
			if (this.treeSelectNode && this.treeSelectNode.id != undefined) return this.treeSelectNode.id !== this.comm.id
			return true
		},
		title(){
			if(this.type==='add') return '增加资料名称'
			else if(this.type==='edit') return '修改资料名称'
			return ''
		}
	},
	beforeMount(){
		this.getTreeData()
	},
	created() {
	},
	methods: {
		async getTreeData() {
			this.loading = true
			let arr = []

			const {data}= await this.$ut.api('homecarecustomer/base/dataSet/type/list', {
				communityId: this.comm.id,
			}).finally(()=>{this.loading = false})
			if(data){
				data.forEach((item) => {
					arr.push({
						...item,
						label: item.title,
						id: item.id,
						pId: item.pId ? item.pId : '',
					})
				})
			}
			this.treeData = arr
			if(this.treeData.length){
				this.$set(this.treeData[0],'select',true)
				this.$emit('node-click', {id:this.treeData[0]})
			}

		},
		treeNodeClick(node) {
			this.treeSelectNode = node
			let temp = Object.assign({}, node)
			this.$set(this.treeData[0],'select',false)
			// if (node.level==1) {
			// 	temp.id = ''
			// 	temp.isRoot = true
			// }else{
			// 	this.$set(this.treeData[0],'select',false)
			// }
			this.$emit('node-click', temp)
		},
		treeAdd() {
            this.type='add'
			this.currNode={}
            this.dialogFormVisible=true
		},
		treeEdit() {
			this.type='edit'
			this.currNode=this.treeSelectNode
            this.dialogFormVisible=true
		},
		treeDelete() {

			this.$baseConfirm('你确定要删除吗', null, async () => {
				this.$ut.api('homecarecustomer/base/dataSet/type/delete', {
						communityId: this.comm.id,
						ids: [this.treeSelectNode.id],
					})
					.then(() => {
						this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
						const treeDataIndex = this.treeData.findIndex((u) => u.id === this.treeSelectNode.id)
						this.treeData.splice(treeDataIndex, 1)
						this.treeSelectNode = {}
					})
			})
		},
		onTreeNodeEdit(node, parent) {
			const treeDataObj = this.treeData.find((u) => u.id == node.id)
			if (treeDataObj) {
				treeDataObj.label = node.label
			} else {
				this.treeData.push({ label: node.label, id: node.id, pId: parent.id })
			}
		},
	},
}
</script>
