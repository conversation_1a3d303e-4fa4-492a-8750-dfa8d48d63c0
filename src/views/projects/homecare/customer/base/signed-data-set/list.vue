<template>
    <div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
        <form-list
            :loading="listLoading"
            :columns="columns"
            :height="height"
            :op-width="100"
            :op-fixed="true"
            :data-list="dataList"
            :select-window="selectWindow"
            :select-single="selectSingle"
            :show-list="showList"
            :show-search="false"
            table-name="long-customer-type"
            @select="handleSelect"
            @fetchData="fetchData"
            @fullscreen="onFullscreen"
            @selectRows="onSelectRows"
        >
            <template #button>
                <el-button icon="el-icon-plus" type="primary" :disabled="!treeNode.id" @click="handleAdd">增加</el-button>
                <el-button v-if="treeNode.id != -1 && treeNode.id" icon="el-icon-delete" type="danger" :disabled="!selectRows.length" @click="handleDelete($event)">删除</el-button>
            </template>
            <template #op="{ row }">
                <el-button type="text" @click="handleEdit(row)">编辑</el-button>
                <el-button type="text" @click="handleDelete(row)">删除</el-button>
            </template>
            <template #cell="{ row, item }">
                <div v-if="item.prop === 'color'" class="color-box">
                    <span class="color-preview" :style="{ backgroundColor: row.color }"></span>
                    {{ row[item.prop] }}
                </div>
                <div v-else-if="item.prop === 'require'">
                    <span v-if="row.require">必须有</span>
                    <span v-else>可选填</span>
                </div>
                <span v-else>{{ row[item.prop] }}</span>
            </template>
        </form-list>

        <!-- 基本信息 -->
        <ut-modal v-model="dialogFormVisible" :title="selectRow.id ? '内容编辑' : '增加内容'" width="600px">
            <edit :init-data="selectRow" :parent-data="treeNode" @fetchData="fetchData" @close="dialogFormVisible = false" />
        </ut-modal>
    </div>
</template>

<script>
import Edit from './edit'
import FormList from '@/views/components/form-list'
import { mapGetters } from 'vuex'
import Vue from 'vue'

export default {
    name: 'LongCustomerSource',
    components: {
        Edit,
        FormList,
    },
    props: {
        height: {
            type: Number,
            default: () => Vue.prototype.$baseTableHeight(1),
        },
        selectWindow: {
            type: Boolean,
            default: false,
        },
        selectSingle: {
            type: Boolean,
            default: false,
        },
        showList: {
            type: Boolean,
            default: false,
        },
        treeNode: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            listLoading: false,
            fullscreen: false,
            columns: [
                {
                    label: '资料名称',
                    align: 'left',
                    prop: 'dataTypeTitle',
                    width: '120',
                    show: true,
                },
                {
                    label: '标题',
                    align: 'left',
                    prop: 'title',
                    width: '120',
                    show: true,
                },
                {
                    label: '数量',
                    align: 'center',
                    prop: 'count',
                    width: '60',
                    show: true,
                },
                {
                    label: '必须有',
                    align: 'center',
                    prop: 'require',
                    width: '120',
                    show: true,
                },
                {
                    label: '文件扩展名',
                    align: 'center',
                    prop: 'ext',
                    minWidth: '220',
                    show: true,
                },
            ],
            dataList: {
                info: [],
                page: 0,
                record: 0,
            },
            page: {
                key: '',
                pageindex: 1,
                pagesize: 20,
                order: '',
            },
            selectRow: {},
            selectRows: [],
            dialogFormVisible: false,
        }
    },
    computed: {
        ...mapGetters({
            comm: 'comm/comm',
        }),
    },
    watch: {
        treeNode: {
            deep: true,
            handler() {
                this.fetchData()
            },
        },
    },
    created() {
        // this.fetchData()
    },
    methods: {
        async fetchData(pageReq) {
            if (pageReq) this.page = pageReq
            this.listLoading = true
            const { data } = await this.$ut
                .api('homecarecustomer/base/dataSet/item/listpg', {
                    communityId: this.comm.id,
                    dataTypeId: this.treeNode.id,
                    ...this.page,
                })
                .finally(() => (this.listLoading = false))
            this.dataList = data
        },
        handleSelect(rows) {
            this.$emit('select', rows)
        },
        onFullscreen(v) {
            this.fullscreen = v
        },
        onSelectRows(rows) {
            this.selectRows = rows
        },
        handleAdd() {
            this.selectRow = {}
            this.dialogFormVisible = true
        },
        handleEdit(row) {
            this.selectRow = row
            this.dialogFormVisible = true
        },
        handleDelete(row) {
            let ids = []
            if (row.id) {
                ids = [row.id]
            } else {
                if (this.selectRows.length > 0) {
                    ids = this.selectRows.map((item) => item.id)
                } else {
                    this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
                    return
                }
            }
            this.$baseConfirm('你确定要删除吗', null, async () => {
                await this.$ut.api('homecarecustomer/base/dataSet/item/delete', {
                    communityId: this.comm.id,
                    dataTypeId: this.treeNode.id,
                    ids: ids,
                })
                this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
                return this.fetchData()
            })
        },
    },
}
</script>

<style lang="scss" scoped>
.color-box {
    display: flex;
    align-items: center;
    justify-content: center;

    .color-preview {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        border-radius: 2px;
    }
}
</style>
