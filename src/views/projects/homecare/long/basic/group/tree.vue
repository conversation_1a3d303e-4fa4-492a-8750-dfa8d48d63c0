<template>
    <group-tree module="long" :height="height" @node-click="nodeClick" />
</template>

<script>
import Vue from 'vue'
import GroupTree from '../../../customer/group/tree.vue'
export default {
    components: {
        GroupTree
    },
    props: {
        height:{
            type:[String,Number],
            default: () => Vue.prototype.$baseTableHeight(1),
        }
    },
    data() {
        return {
            
        }
    },
    computed: {
    },
    watch: {
    },
    created() {
    },
    beforeMount() {
    },
    destroyed() {
    },
    methods: {
        nodeClick(node){
            this.$emit('node-click', node)
        }
    },
}
</script>
<style lang='scss' scoped>

</style>