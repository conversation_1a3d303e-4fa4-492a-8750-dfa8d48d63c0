<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-row>
				<el-col :span="24">							
					<el-form-item label="说明：" prop="title">
						<el-input v-model.trim="form.title" placeholder="请输入说明" />
					</el-form-item>
				</el-col>
				<el-col :span="12">							
					<el-form-item label="数量：" prop="count">
						<el-input v-model.trim="form.count" placeholder="请输入数量，0表示很多" />
					</el-form-item>
				</el-col>
				<el-col :span="12">							
					<el-form-item label="必有：" prop="require">
						<el-switch v-model="form.require" />
					</el-form-item>
				</el-col>
				<el-col :span="24">							
					<el-form-item label="文件扩展名：" prop="ext">
						<el-input v-model.trim="form.ext" placeholder="请输入文件扩展名" />
						<div style="color:#aaa;">没有表示不限,多个之间用 | 隔开</div>
						<el-button type="text" @click="quickImage">点击这里快速填入图标格式</el-button>
					</el-form-item>
				</el-col>
			</el-row>

		</el-form>

		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'

export default {
	components:{
	},
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
		parentData: {
			type: Object,
			default: () => ({}),
		},
	},
	
	data() {
		return {
			loading: false,
			form: {
				id: '',
				name: '',
				count:'',
				require:false,
				ext:'',
			},
			rules: {
				title: [{ required: true, trigger: 'blur', message: '请输入内容名称' }],
				count: [{ required: true, trigger: 'blur', message: '请输入数量' }],
			},

			typeFormVisible:false,
			levelFormVisible:false,
			sourceFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarelong/basic/commData/item/info', {
				communityId:this.comm.id,
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
		
		},
		
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
		
				await this.$ut.api('homecarelong/basic/commData/item/save', {
					communityId:this.comm.id,
					dataTypeId:this.parentData.id,
					...this.form,
				}).finally(()=>{this.loading = false})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
		quickImage(){
			this.form.ext='jpg|jpeg|bmp|gif|webp|png|tiff'
		}
	},
}
</script>
<style lang="scss" scoped>
.color-box {
	display: inline-block;
	height: 32px;
	width: 150px;
	border: 1px solid #dcdfe6;
	padding-left: 20px;

	.color-box-text {
		color:#fff;
		mix-blend-mode: difference;
	}
}
.placeholder {
	height: 70px;
}


</style>
