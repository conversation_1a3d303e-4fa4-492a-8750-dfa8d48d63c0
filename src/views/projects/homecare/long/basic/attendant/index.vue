<template>
	<form-tree-list :tree-width="220" :class="{ 'ut-fullscreen': fullscreen }" >
		<template #tree>
			<tree :height="height" @node-click="nodeClick"/>
		</template>
		<template #list>
			<list :tree-node="treeNode" :height="height" :select-single="selectSingle" :select-window="selectWindow" @select="handleSelect"/>
		</template>
	</form-tree-list>
	<!-- <div class="treelist ut-body" :style="{height:height+'px'}">
		<div class="tree">
			<tree @node-click="nodeClick"/>
		</div>
		<div class="list">
			<list :tree-node="treeNode"  :select-single="selectSingle" :select-window="selectWindow" @select="handleSelect"/>
		</div>
	</div> -->
</template>

<script>

import Vue from 'vue'
import FormTreeList from '@/views/components/form-tree-list'
import List from './list.vue'
import Tree from '../group/tree.vue'
export default {
	name:'LongBasicAttendant',
	components: {
		FormTreeList,
		List,
		Tree,
	},
	props: {
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		height:{
			type:[Number,String],
			default: () => Vue.prototype.$baseTableHeight(1),
		}
	},
	data() {
		return {
			fullscreen: false,
			treeNode:{},
		}
	},
	computed: {},
	created() {},
	methods: {
		nodeClick(node) {
			this.treeNode=node
		},

		handleSelect(rows){
			this.$emit('select',rows)
		}

	},
}
</script>
<style lang="scss" scoped>
// .treelist{
// 	display: flex;
// }
// .tree{
// 	width:220px;
// 	box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
// 	padding: 8px;
// 	border-radius: 4px;
// 	margin-right: 4px;
// }
// .list{
// 	flex: 1;
// 	padding: 8px;
// 	overflow: hidden;
// 	background: #fff;
// 	border-radius: 4px;
// 	margin-left: 4px;
// }

// .ut-fullscreen.ut-body{
// 	background: #fff;
// 	padding: 8px;;
// }
</style>