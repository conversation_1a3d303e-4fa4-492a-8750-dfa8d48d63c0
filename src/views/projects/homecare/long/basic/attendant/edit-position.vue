<template>
	<div>
		<div src="https://apis.map.qq.com/tools/locpicker?search=1&type=1&key=SFABZ-WANWW-FISRY-3IGTF-HV7RE-YSFTI&referer=myapp"></div>
		<el-form ref="form" label-width="80px" :model="form" :rules="rules" class="ut-form">
			
			<el-row :gutter="10">
                <el-col :span="20">		
                    <el-form-item label="地址：" prop="address">
                        <el-input v-model.trim="form.address" />
                    </el-form-item>
                </el-col>
				<el-col :span="4">
					<el-button @click="qqmapDialog=true">坐标拾取</el-button>
				</el-col>
				<el-col :span="12">
					<el-form-item label="经度" prop="longitude">
						<el-input v-model.trim="form.longitude" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="纬度" prop="latitude">
						<el-input v-model.trim="form.latitude" />
					</el-form-item>
				</el-col>
			</el-row>
			
		</el-form>
		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">确 定</el-button>
			<el-button @click="$emit('close')">取 消</el-button>
		</div>
		<el-dialog  v-if="qqmapDialog" v-dialog-drag  append-to-body :close-on-click-modal="true" title="坐标拾取" top="3vh" :visible.sync="qqmapDialog" width="1000px">
			<div style="width:100%;height:800px">
				<qq-map :map-key="qqmap.mapKey" :key-name="qqmap.keyName" :lng="form.longitude" :lat="form.latitude" @callback="callback"  @close="qqmapDialog = false"/>
			</div>
		</el-dialog>
	</div>
</template>

<script>

import { mapGetters } from 'vuex'
import QqMap from '@/views/components/qq-map'
export default {
	name: 'CommunityEdit',
	components:{
		QqMap,
	},
	props:{
		initData:{
			type:Object,
			default:()=>{},
		},
	},
	data() {
		return {
			uploadImage:'',
			uploadInfo: {},
			fileList: [],
			maxCount:0,

			qqmapDialog:false,
			qqmap:{
				mapKey:'SFABZ-WANWW-FISRY-3IGTF-HV7RE-YSFTI',
				keyName:'myapp',
			},
			form: {
				id: '',
				address: '',
				longitude: '',
				latitude: '',
			},
			rules: {
			},
		}
	},
	computed:{
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	created() {},
	beforeMount(){
		this.getInfo()
	},

	methods: {
		async getInfo() {
			if (!this.initData.id) return
			this.loading=true
			const { data } = await this.$ut.api('homecarecustomer/group/attendant/location/info', {
				communityId: this.comm.id,
				module:'long',
				id: this.initData.attendantId,
			}).finally(()=>{this.loading = false})
			this.form = data || {}
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
		
				await this.$ut.api('homecarecustomer/group/attendant/location/save', {
					communityId:this.comm.id,
					module:'long',
					...this.form,
					id: this.initData.attendantId,
				}).finally(()=>{this.loading = false})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
		callback(loc){
			console.log(loc)
			this.form.latitude=loc.latlng.lat
			this.form.longitude=loc.latlng.lng
			this.form.address=loc.poiaddress
		}
	},
}
</script>
