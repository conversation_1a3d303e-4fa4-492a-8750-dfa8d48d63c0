<template>
	<div v-loading="loading" style="height: calc(100vh - 166px)">
		<div class="customer-wrapper ut-fullheight">
			<div class="left-box">
				<el-card>
					<template v-for="(item, index) in customerList">
						<div :key="index" class="customer-item" @click="customerClick(item)">
							<div>{{ item.name }}</div>
							<div class="distance">{{ item.distance }}km</div>
						</div>
					</template>
				</el-card>
			</div>
			<div id="container" class="map-box" style="height: 100%"></div>
		</div>
	</div>
</template>

<script>
	import { MapMixins } from '@/views/projects/homecare/components/map'

	export default {
		mixins: [MapMixins],
		components: {},
		props: {
			attendantId: {
				type: String,
			},
			newCustomer: {
				type: Object,
				default: () => {},
			},
		},
		data() {
			return {
				loading: false,
				customerList: [],
				pageAttendantId: '',
				multiMarker: null,
				infoWindow: null,
			}
		},
		computed: {},
		watch: {},
		created() {
			if (this.$route.query && this.$route.query.attendantId) {
				this.pageAttendantId = this.$route.query.attendantId
			} else {
				this.pageAttendantId = this.attendantId
			}

			this.initMapAndData()
		},
		mounted() {},

		methods: {
			async initMapAndData() {
				this.loading = true
				try {
					await this.getList()

					// 确定地图中心点
					let center = null
					let obj = this.mapMarkers.find((u) => u.type == 'attendant')
					if (obj) {
						center = { lat: obj.lat, lng: obj.lng }
					} else {
						obj = this.mapMarkers.find((u) => u.type == 'new')
						if (obj) {
							center = { lat: obj.lat, lng: obj.lng }
						} else if (this.mapMarkers.length > 0) {
							center = { lat: this.mapMarkers[0].lat, lng: this.mapMarkers[0].lng }
						}
					}

					await this.initMap('container', {
						zoom: 13,
						pitch: 0,
						center: center,
					})
					this.setupMap()
				} catch (error) {
					console.error('初始化地图和数据失败:', error)
					this.$message.error('初始化失败')
				} finally {
					this.loading = false
				}
			},
			async getList() {
				const { data } = await this.$ut.api('homecarecustomer/group/attendant/customer/locationAll', {
					communityId: this.comm.id,
					module: 'long',
					attendantId: this.pageAttendantId,
				})
				this.customerList = data.customers || []
				this.mapMarkers = []
				if (data) {
					this.mapMarkers.push({
						type: 'attendant',
						name: data.name,
						lat: data.latitude,
						lng: data.longitude,
					})
				}
				if (data && data.customers) {
					data.customers.forEach((item) => {
						if (item.longitude && item.latitude) {
							this.mapMarkers.push({
								id: item.id,
								type: 'customer',
								name: item.name,
								lat: item.latitude,
								lng: item.longitude,
								properties: {
									customerName: item.name,
									imgHead: item.imgHead,
								},
							})
						}
					})
				}
				if (data && data.newCustomer) {
					this.mapMarkers.push({
						type: 'new',
						name: '新客户',
						lat: data.newCustomer.latitude,
						lng: data.newCustomer.longitude,
					})
				}

				if (this.newCustomer && this.newCustomer.longitude && this.newCustomer.latitude) {
					this.mapMarkers.push({
						type: 'new',
						name: this.newCustomer.name ? this.newCustomer.name : '新客户',
						lat: this.newCustomer.latitude,
						lng: this.newCustomer.longitude,
					})
				}
			},
			setupMap() {
				if (!this.mapInstance || !this.mapMarkers.length) return

				// 创建标记样式
				const styles = {
					default: {
						color: '#369',
						src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/marker_blue.png',
						width: 32,
						height: 40,
						anchor: { x: 16, y: 48 },
						// 文字标签配置
						offset: { x: 0, y: -8 },
						size: 12,
						direction: 'bottom',
						strokeColor: '#fff',
						strokeWidth: 2,
					},
					customer: {
						src: 'https://mapapi.qq.com/web/lbs/visualizationApi/demo/img/big.png',
						width: 32,
						height: 40,
						anchor: { x: 16, y: 48 },
						// 文字标签配置
						offset: { x: 0, y: -8 },
						size: 12,
						direction: 'bottom',
						strokeColor: '#fff',
						strokeWidth: 2,
					},
					new: {
						color: '#000',
						src: 'https://mapapi.qq.com/web/lbs/visualizationApi/demo/img/small.png',
						width: 32,
						height: 40,
						anchor: { x: 16, y: 48 },
						// 文字标签配置
						offset: { x: 0, y: -8 },
						size: 12,
						direction: 'bottom',
						strokeColor: '#fff',
						strokeWidth: 2,
					},
					attendant: {
						color: '#369',
						src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/marker_blue.png',
						width: 32,
						height: 40,
						anchor: { x: 16, y: 48 },
						// 文字标签配置
						offset: { x: 0, y: -8 },
						size: 12,
						direction: 'bottom',
						strokeColor: '#fff',
						strokeWidth: 2,
					},
				}

				// 添加标记
				this.multiMarker = this.addMarkers(this.mapMarkers, styles)

				// 创建信息窗
				this.infoWindow = this.createInfoWindow({
					offset: { x: 0, y: -20 },
				})
				this.infoWindow.close()

				// 添加点击事件
				if (this.multiMarker) {
					this.multiMarker.on('click', (evt) => {
						const customer = evt.geometry.properties
						if (!customer || !customer.customerName) return

						this.infoWindow.open()
						this.infoWindow.setPosition(evt.geometry.position)

						let imgHead = customer.imgHead
						if (imgHead) {
							if (imgHead.indexOf('oss') >= 0) {
								imgHead += '&width=64'
							}
						} else {
							imgHead = ''
						}
						const content = `<div><div class='le'><img src='${imgHead}' onerror="this.style.display='none'"></div><div class='ri'>${customer.customerName}</div></div>`
						this.infoWindow.setContent(content)
					})

					this.multiMarker.on('mouseleave', () => {
						this.infoWindow.close()
					})
				}
			},
			customerClick(item) {
				this.setMapCenter(item.latitude, item.longitude)

				if (this.infoWindow && this.mapInstance && window.TMap) {
					this.infoWindow.open()
					this.infoWindow.setPosition(new window.TMap.LatLng(item.latitude, item.longitude))

					let imgHead = item.imgHead
					if (imgHead) {
						if (imgHead.indexOf('oss') >= 0) {
							imgHead += '&width=64'
						}
					} else {
						imgHead = ''
					}
					const content = `<div><div class='le'><img src='${imgHead}' onerror="this.style.display='none'"></div><div class='ri'>${item.name}</div></div>`
					this.infoWindow.setContent(content)
				}
			},
		},
	}
</script>
<style lang="scss">
	.map-box div {
		box-sizing: initial !important;
	}

	.clusterBubble {
		border-radius: 50%;
		color: #fff;
		font-weight: 500;
		text-align: center;
		opacity: 0.88;
		background-image: linear-gradient(139deg, #4294ff 0%, #295bff 100%);
		box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2);
		position: absolute;
		top: 0px;
		left: 0px;
	}

	.logo-text {
		display: none !important;
	}

	.le img {
		width: 64px;
		height: 64px;
	}
</style>
<style lang="scss" scoped>
	#container {
		position: relative;
		height: 100%;
		width: 100%;
	}

	.customer-wrapper {
		display: flex;
		flex-direction: row;

		.left-box {
			width: 200px;
			margin-right: 10px;
		}
		.map-box {
			flex: 1;
		}
	}

	.customer-item {
		display: flex;
		justify-content: space-between;
		padding: 2px 5px;
		cursor: pointer;

		.distance {
			font-size: 12px;
			color: #ccc;
		}
	}

	:deep(.el-card) {
		height: 100%;
	}
</style>
