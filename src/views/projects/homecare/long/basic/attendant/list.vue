<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="160"
			:op-fixed="true"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			table-name="long-customer-type"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button">
				<el-button icon="el-icon-plus" type="primary" :disabled="!treeNode.id" @click="handleAdd">增加护理人员</el-button>
				<el-button icon="el-icon-delete" type="danger" :disabled="!selectRows.length" @click="handleDelete($event)">删除</el-button>
			</template>
			<template #op="{ row }">
				<!-- <el-button type="text" @click="handleEdit(row)">编辑</el-button> -->
				<el-button type="text" @click="handleDelete(row)">删除</el-button>
				<el-button :type="row.longitude?'primary':'text'"  @click="handlePosition(row)">定位</el-button>
				<el-button type="text" @click="handleCustomer(row)">客户</el-button>
			</template>
			<template #cell="{ row, item }">
				<div v-if="item.prop === 'color'" class="color-box">
					<span class="color-preview" :style="{ backgroundColor: row.color }"></span>
					{{ row[item.prop] }}
				</div>
                <div v-else-if="item.prop === 'sex'">
                    <span v-if="row.sex==1">男</span>
                    <span v-if="row.sex==2">女</span>
				</div>
				<span v-else>{{ row[item.prop] }}</span>
			</template>
		</form-list>

		<!-- 基本信息 -->
		<!-- <el-dialog	v-if="dialogFormVisible" v-dialog-drag  :title="selectRow.id?'客户编辑':'增加客户'" :visible.sync="dialogFormVisible"	append-to-body width="800px" :destroy-on-close="true" :close-on-click-modal="false" :close-on-press-escape="false"	>
			<edit :init-data="selectRow" @fetchData="fetchData" @close="dialogFormVisible=false" />
		</el-dialog> -->

		<ut-modal v-model="employeeFormVisible" title="选择员工" width="700px">
			<employee-list :select-window="true" :select-single="true" :height="400" @select="handleEmployee" @close="employeeFormVisible=false"/>
		</ut-modal>

		<!-- 位置 -->
		<ut-modal v-model="positionFormVisible"  title="客户定位" width="600px">
			<edit-position :init-data="selAttendant" @close="positionFormVisible=false" />
		</ut-modal>

	</div>
</template>

<script>
import EditPosition from './edit-position'
import EmployeeList from '../../../personnel/employee'
import FormList from '@/views/components/form-list'
import { mapGetters } from 'vuex'
import Vue from 'vue'
export default {
	name: 'LongCustomerSource',
	components: {
		EditPosition,
		FormList,
		EmployeeList,
	},
	props: {
		height: {
			type: [String,Number],
			default: () => Vue.prototype.$baseTableHeight(1),
		},
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
        treeNode:{
            type:Object,
            default:()=>{},
        }
	},
	data() {
		return {
			listLoading: false,
			fullscreen: false,
			columns: [
				// {
				// 	label: '序',
				// 	align: 'center',
				// 	prop: 'index',
				// 	width:'50',
				// 	fixed:'left',
				// 	show: true,
				// },
				{
					label: '分组',
					align: 'left',
					prop: 'groupName',
					width:'120',
					show: true,
				},
				{
					label: '姓名',
					align: 'left',
					prop: 'name',
					width:'120',
					show: true,
				},
				{
					label: '性别',
					align: 'center',
					prop: 'sex',
					width:'60',
					show: true,
				},
				{
					label: '电话',
					align: 'center',
					prop: 'phone',
					width:'120',
					show: true,
				},
				{
					label: '证件号码',
					align: 'center',
					prop: 'idcard',
					minWidth:'220',
					show: true,
				},
				
				{
					label: '民族',
					align: 'center',
					prop: 'nation',
					width:'120',
					show: true,
				},
				{
					label: '出生日期',
					align: 'center',
					prop: 'birthday',
					width:'120',
					show: true,
				},
				{
					label: '电子邮箱',
					align: 'center',
					prop: 'email',
					width:'180',
					show: true,
				},				

			],
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 20,
				order: '',
			},
			selectRow: {},
			selectRows: [],
			employeeFormVisible:false,
			selAttendant:{},
			// dialogFormVisible: false,
			// otherFormVisible:false,
			positionFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
    watch:{
        treeNode:{
            deep:true,
            handler(){
                this.fetchData()
            }
        }
    },
	created() {
		// this.fetchData()
	},
	methods: {
		async fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true
			const {data} = await this.$ut.api('homecarecustomer/group/attendant/listpg', {
				communityId:this.comm.id,
				module:'long',
                groupId:this.treeNode.id,
                ...this.page,
            }).finally(()=>{this.listLoading=false})
            this.dataList=data
			//this.getEmployeeInfo()
		},
		// async getEmployeeInfo() {
		// 	const ids=this.dataList.info.map(o=>o.employeeId)
		// 	if(!ids.length) return
			
		// 	this.listLoading = true
		// 	const {data} = await this.$ut.api('homecarepersonnel/employee/listByIds', { 
		// 		communityId:this.comm.id,
		// 		ids: ids 
		// 	}).finally(()=>{this.listLoading=false})
		// 	if(data){
		// 		this.dataList.info.forEach(item=>{
		// 			let obj=data.find(u=>u.id==item.employeeId)
		// 			if(obj){
		// 				this.$set(item,'name',obj.name)
		// 				this.$set(item,'phone',obj.phone)
		// 				this.$set(item,'sex',obj.sex)
		// 				this.$set(item,'idcard',obj.idcard)
		// 				this.$set(item,'nation',obj.nation)
		// 				this.$set(item,'birthday',obj.birthday)
		// 				this.$set(item,'email',obj.email)
		// 				this.$set(item,'education',obj.education)
		// 				this.$set(item,'address',obj.address)
		// 			}
		// 		})
		// 	}
		// },
		handleSelect(rows) {
			this.$emit('select', rows)
		},
		onFullscreen(v) {
			this.fullscreen = v
		},
		onSelectRows(rows) {
			this.selectRows = rows
		},
		handleAdd() {
			this.selectRow = {}
			this.employeeFormVisible=true
		},
		handleEdit(row) {
			this.selectRow = row
			this.dialogFormVisible = true
		},
		handleDelete(row) {
			let ids = []
			if (row.id) {
				ids = [row.id]
			} else {
				if (this.selectRows.length > 0) {
					ids = this.selectRows.map((item) => item.id)
				} else {
					this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
					return
				}
			}
			this.$baseConfirm('你确定要删除吗', null, async () => {
				this.$ut.api('homecarecustomer/group/attendant/delete', {
                    communityId: this.comm.id,
					module:'long',
                    ids: ids,
                }).then(() => {
                    this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
                    this.fetchData()
                })
			})
		},
		handleEmployee(rows){
			this.employeeFormVisible = false
			if (!rows || !rows[0]) return

			this.loading=true
			this.$ut.api('homecarecustomer/group/attendant/add', {
				communityId: this.comm.id,
				module:'long',
                groupId:this.treeNode.id,
				employeeId: rows[0].id
			}).then(()=>{
				 this.fetchData()
			}).finally(()=>{this.loading = false})
			
			
		},
		handleCustomer(row){
			this.selAttendant=row
			this.$router.push({path:'/long/basic/attendant/customer',query:{attendantId:row.attendantId}})
		},
		handlePosition(row){
			this.selAttendant = row
			this.positionFormVisible = true
		},
	},
}
</script>

<style lang="scss" scoped>
.color-box {
	display: flex;
	align-items: center;
	justify-content: center;

	.color-preview {
		width: 20px;
		height: 20px;
		margin-right: 10px;
        border-radius: 2px;
	}
}

</style>
