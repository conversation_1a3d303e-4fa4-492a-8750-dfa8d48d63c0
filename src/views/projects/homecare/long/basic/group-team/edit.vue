<template>
    <div class="edit-container">
        <!-- 基本信息编辑 -->
        <el-form ref="form" v-loading="loading" class="ut-body" :model="form" :rules="rules" label-width="80px" @submit.native.prevent>
            <el-form-item label="名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入名称" />
            </el-form-item>
            <el-form-item label="排序" prop="showIndex">
                <el-input-number v-model="form.showIndex" :min="0" />
            </el-form-item>
            <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" />
            </el-form-item>
            <el-form-item class="ut-edit-footer">
                <el-button type="primary" @click="submit">保 存</el-button>
                <el-button @click="close">取 消</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
    components: {},
    props: {
        initData: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            loading: false,
            form: {
                id: undefined,
                groupId: '',
                name: '',
                remark: '',
                showIndex: 0,
            },
            rules: {
                name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
            },
        }
    },
    computed: {
        ...mapGetters({
            comm: 'comm/comm',
        }),
    },
    mounted() {
        this.getInfo()
    },
    methods: {
        async getInfo() {
            this.form = Object.assign(this.form, this.initData)
            if (!this.form.id) return
            this.loading = true
            const { data } = await this.$ut
                .api('homecarelong/basic/groupTeam/info', {
                    communityId: this.comm.id,
                    id: this.initData.id,
                })
                .finally(() => (this.loading = false))
            this.form = Object.assign(this.form, data)
        },
        submit() {
            this.$refs.form.validate(async (valid) => {
                if (!valid) return
                this.loading = true
                const params = {
                    communityId: this.comm.id,
                    ...this.form,
                }
                await this.$ut.api('homecarelong/basic/groupTeam/save', params).finally(() => (this.loading = false))
                this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
                this.$emit('fetchData')
                this.close()
            })
        },
        close() {
            this.$refs.form.resetFields()
            this.$emit('close')
        },
    },
}
</script>

<style lang="scss" scoped></style>
