<template>
    <div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
        <form-list
            :loading="listLoading"
            :columns="columns"
            :height="height"
            :op-width="150"
            :op-fixed="true"
            :data-list="dataList"
            :select-window="selectWindow"
            :select-single="selectSingle"
            :show-list="showList"
            table-name="group-team-attendant"
            @select="handleSelect"
            @fetchData="fetchData"
            @fullscreen="onFullscreen"
            @selectRows="onSelectRows"
        >
            <template #cell="{ row, item }">
                <div v-if="item.prop === 'color'" class="color-box">
                    <span class="color-preview" :style="{ backgroundColor: row.color }"></span>
                    {{ row[item.prop] }}
                </div>
                <div v-else-if="item.prop === 'valid'">
                    <span v-if="row.valid"><i class="el-icon-check" /></span>
                    <span v-else>未启用</span>
                </div>
                <div v-else-if="item.prop === 'sex'">
                    <span v-if="row.sex == 1">男</span>
                    <span v-if="row.sex == 2">女</span>
                </div>
                <span v-else>{{ row[item.prop] }}</span>
            </template>
        </form-list>
    </div>
</template>

<script>
import FormList from '@/views/components/form-list'
import { mapGetters } from 'vuex'
import Vue from 'vue'

export default {
    name: 'PersonnelEmployee',
    components: {
        FormList,
    },
    props: {
        height: {
            type: Number,
            default: () => Vue.prototype.$baseTableHeight(1),
        },
        selectWindow: {
            type: Boolean,
            default: false,
        },
        selectSingle: {
            type: Boolean,
            default: false,
        },
        showList: {
            type: Boolean,
            default: false,
        },
        treeNode: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            listLoading: false,
            fullscreen: false,
            columns: [
                {
                    label: '序',
                    align: 'center',
                    prop: 'index',
                    width: '50',
                    show: true,
                },
                {
                    label: '姓名',
                    align: 'left',
                    prop: 'name',
                    width: '120',
                    show: true,
                },
                {
                    label: '性别',
                    align: 'center',
                    prop: 'sex',
                    width: '60',
                    show: true,
                },
                {
                    label: '电话',
                    align: 'center',
                    prop: 'phone',
                    width: '120',
                    show: true,
                },
                {
                    label: '证件号码',
                    align: 'center',
                    prop: 'idcard',
                    minWidth: '220',
                    show: true,
                },

                {
                    label: '民族',
                    align: 'center',
                    prop: 'nation',
                    width: '120',
                    show: true,
                },
                {
                    label: '出生日期',
                    align: 'center',
                    prop: 'birthday',
                    width: '120',
                    show: true,
                },
                {
                    label: '学历',
                    align: 'center',
                    prop: 'education',
                    width: '120',
                    show: true,
                },
                {
                    label: '电子邮箱',
                    align: 'center',
                    prop: 'email',
                    width: '200',
                    show: true,
                },
            ],
            dataList: {
                info: [],
                page: 0,
                record: 0,
            },
            page: {
                key: '',
                pageindex: 1,
                pagesize: 20,
                order: '',
            },
            selectRow: {},
            selectRows: [],
            dialogFormVisible: false,
            otherFormVisible: false,
        }
    },
    computed: {
        ...mapGetters({
            comm: 'comm/comm',
        }),
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        async fetchData(pageReq) {
            if (pageReq) this.page = pageReq
            this.listLoading = true
            const { data } = await this.$ut
                .api('homecarecustomer/group/attendant/listpg', {
                    communityId: this.comm.id,
                    groupId: this.treeNode.pId,
                    module: 'long',
                    ...this.page,
                })
                .finally(() => (this.listLoading = false))
            this.dataList = data
        },
        handleSelect(rows) {
            this.$emit('select', rows)
        },
        onFullscreen(v) {
            this.fullscreen = v
        },
        onSelectRows(rows) {
            this.selectRows = rows
        },
    },
}
</script>

<style lang="scss" scoped>
.color-box {
    display: flex;
    align-items: center;
    justify-content: center;

    .color-preview {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        border-radius: 2px;
    }
}
</style>
