<template>
    <div class="tree-box" :style="{ height: height }">
        <div v-if="showTool" style="margin-bottom: 10px">
            <el-button type="primary" :disabled="!canAdd" @click="treeAdd">增加</el-button>
            <el-button type="primary" :disabled="!canEdit" @click="treeEdit">修改</el-button>
            <el-button type="primary" :disabled="!canDelete" @click="treeDelete">删除</el-button>
        </div>
        <div v-loading="loading">
            <ut-tree :tree="treeData" :expanded-keys="expandedKeys" @nodeClick="treeNodeClick">
                <template #icon></template>
            </ut-tree>
        </div>
        <ut-modal v-model="dialogFormVisible" :title="title" width="500px">
            <edit :init-data="editData" @fetchData="getTreeData" @close="dialogFormVisible = false" />
        </ut-modal>
    </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Edit from './edit.vue'

export default {
    components: {
        Edit,
    },
    props: {
        showTool: {
            type: Boolean,
            default: true,
        },
        height: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            treeData: [],
            loading: false,
            treeSelectNode: {},
            editData: {},
            dialogFormVisible: false,
            expandedKeys: [],
        }
    },
    computed: {
        ...mapGetters({
            comm: 'comm/comm',
        }),
        title() {
            if (this.type === 'add') {
                return '增加小组'
            } else if (this.type === 'edit') return '修改小组'
            return ''
        },
        canAdd() {
            if (!this.treeSelectNode.id) return false
            const nodeType = this.treeSelectNode.type
            return nodeType === 'group' || nodeType === 'team'
        },
        canEdit() {
            if (!this.treeSelectNode.id) return false
            const nodeType = this.treeSelectNode.type
            return nodeType === 'team'
        },
        canDelete() {
            if (!this.treeSelectNode.id) return false
            const nodeType = this.treeSelectNode.type
            return nodeType === 'team'
        },
    },
    created() {
        this.getTreeData()
    },
    methods: {
        async getTreeData() {
            this.loading = true
            let arr = []
            const { data } = await this.$ut
                .api('homecarelong/basic/groupTeam/groupList', {
                    communityId: this.comm.id,
                })
                .finally(() => (this.loading = false))
            if (data) {
                for (const item of data) {
                    arr.push({
                        ...item,
                        label: item.name,
                        id: item.id,
                        pId: item.pId ? item.pId : '',
                        type: item.type,
                    })
                }
            }
            this.treeData = arr
            if (this.treeData.length) {
                this.treeSelectNode = this.treeData[0]
                this.$set(this.treeData[0], 'select', true)
                let temp = Object.assign({}, this.treeData[0])
                this.$emit('node-click', temp)
            }
        },
        treeNodeClick(node) {
            this.treeSelectNode = node
            let temp = Object.assign({}, node)
            if (this.treeData.length > 0) {
                this.$set(this.treeData[0], 'select', false)
            }
            this.$emit('node-click', temp)
        },
        treeAdd() {
            const data = {}
            if (this.treeSelectNode.type === 'group') {
                data.groupId = this.treeSelectNode.id
            } else if (this.treeSelectNode.type === 'team') {
                data.groupId = this.treeSelectNode.pid
            }
            this.editData = data
            this.dialogFormVisible = true
        },
        treeEdit() {
            console.log(this.treeSelectNode)
            this.editData = {
                id: this.treeSelectNode.id,
                name: this.treeSelectNode.name,
                groupId: this.treeSelectNode.pid,
            }
            this.dialogFormVisible = true
        },
        treeDelete() {
            this.$baseConfirm('你确定要删除当前项吗', null, async () => {
                this.loading = true
                await this.$ut
                    .api('homecarelong/basic/groupTeam/delete', {
                        communityId: this.comm.id,
                        ids: [this.treeSelectNode.id],
                    })
                    .finally(() => (this.loading = false))
                this.$baseMessage('删除成功', 'success', 'ut-hey-message-success')
                this.getTreeData()
            })
        },
    },
}
</script>
