<template>
    <form-tree-list :tree-width="220" :class="{ 'ut-fullscreen': fullscreen }">
        <template #tree>
            <tree :height="height" @node-click="nodeClick" />
        </template>
        <template #list>
            <list :tree-node="treeNode" :height="height" @select="handleSelect" />
        </template>
    </form-tree-list>
</template>

<script>
import Vue from 'vue'
import FormTreeList from '@/views/components/form-tree-list'
import List from './list.vue'
import Tree from './tree.vue'

export default {
    name: 'GroupTeam',
    components: {
        FormTreeList,
        List,
        Tree,
    },
    props: {
        selectWindow: {
            type: Boolean,
            default: false,
        },
        selectSingle: {
            type: Boolean,
            default: false,
        },
        height: {
            type: [Number, String],
            default: () => Vue.prototype.$baseTableHeight(1),
        },
    },
    data() {
        return {
            fullscreen: false,
            treeNode: {},
        }
    },
    computed: {},
    created() {},
    methods: {
        nodeClick(node) {
            this.treeNode = node
        },
        handleSelect(rows) {
            this.$emit('select', rows)
        },
    },
}
</script>
