<template>
    <div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
        <div v-if="shouldShowMemberList">
            <form-list
                :loading="listLoading"
                :columns="columns"
                :height="height"
                :op-width="140"
                :op-fixed="true"
                :data-list="dataList"
                :select-window="selectWindow"
                :select-single="selectSingle"
                :show-list="showList"
                table-name="group-team-member-list"
                @select="handleSelect"
                @fetchData="fetchData"
                @fullscreen="onFullscreen"
                @selectRows="onSelectRows"
            >
                <template #button>
                    <el-button icon="el-icon-plus" type="primary" :disabled="!canAddMember" @click="handleAddMember">新增成员</el-button>
                    <el-button icon="el-icon-delete" type="danger" :disabled="!selectRows.length" @click="handleDeleteMembers">删除成员</el-button>
                </template>
                <template #op="{ row }">
                    <el-button v-if="row.isHead" type="text" @click="handleCancelHead(row)">取消组长</el-button>
                    <el-button v-else type="text" @click="handleSetHead(row)">设为组长</el-button>
                    <el-button type="text" @click="handleDeleteMember(row)">删除</el-button>
                </template>
                <template #cell="{ row, item }">
                    <span v-if="item.prop === 'sex'">
                        <span v-if="row.sex === 1">男</span>
                        <span v-if="row.sex === 2">女</span>
                    </span>
                    <span v-else-if="item.prop === 'groupTeam'">
                        <span>{{ row.groupName }}-{{ row.teamName }}</span>
                    </span>
                    <span v-else-if="item.prop === 'isHead'">
                        <el-tag v-if="row.isHead" type="primary">组长</el-tag>
                        <el-tag v-else type="info">组员</el-tag>
                    </span>
                    <span v-else>{{ row[item.prop] }}</span>
                </template>
            </form-list>
        </div>
        <!-- 当选中其他类型节点时显示提示信息 -->
        <div v-else class="empty-state">
            <el-empty description="请选择组或团队节点查看成员信息" />
        </div>

        <!-- 新增成员选择窗口 -->
        <ut-modal v-model="employeeDialogVisible" title="选择员工" width="700px">
            <attendant-list
                v-if="canAddMember"
                :tree-node="treeNode"
                :select-window="true"
                :select-single="false"
                :height="400"
                @select="handleEmployeeSelect"
                @close="employeeDialogVisible = false"
            />
        </ut-modal>
    </div>
</template>

<script>
import AttendantList from './attendant.vue'
import FormList from '@/views/components/form-list'
import { mapGetters } from 'vuex'
import Vue from 'vue'

export default {
    name: 'GroupTeamList',
    components: {
        AttendantList,
        FormList,
    },
    props: {
        height: {
            type: [String, Number],
            default: () => Vue.prototype.$baseTableHeight(1),
        },
        selectWindow: {
            type: Boolean,
            default: false,
        },
        selectSingle: {
            type: Boolean,
            default: false,
        },
        showList: {
            type: Boolean,
            default: false,
        },
        treeNode: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            listLoading: false,
            fullscreen: false,
            columns: [
                {
                    label: '姓名',
                    align: 'left',
                    prop: 'name',
                    width: '120',
                    show: true,
                },
                {
                    label: '性别',
                    align: 'center',
                    prop: 'sex',
                    width: '80',
                    show: true,
                },
                {
                    label: '分组',
                    align: 'center',
                    prop: 'groupTeam',
                    minWidth: '120',
                    show: true,
                },
                {
                    label: '电话',
                    align: 'center',
                    prop: 'phone',
                    width: '120',
                    show: true,
                },
                {
                    label: '是否组长',
                    align: 'center',
                    prop: 'isHead',
                    width: '100',
                    show: true,
                },
            ],
            dataList: {
                info: [],
                page: 0,
                record: 0,
            },
            page: {
                key: '',
                pageindex: 1,
                pagesize: 20,
            },
            selectRows: [],
            employeeDialogVisible: false,
        }
    },
    computed: {
        ...mapGetters({
            comm: 'comm/comm',
        }),
        nodeType() {
            return this.getNodeType(this.treeNode)
        },
        shouldShowMemberList() {
            if (!this.treeNode || !this.treeNode.id) return false
            return this.nodeType === 'group' || this.nodeType === 'team'
        },
        canAddMember() {
            return this.nodeType === 'team'
        },
    },
    watch: {
        treeNode: {
            handler() {
                if (this.shouldShowMemberList) {
                    this.fetchData()
                }
            },
            deep: true,
        },
    },
    created() {},
    methods: {
        async fetchData(pageReq) {
            if (!this.shouldShowMemberList) return
            if (pageReq) this.page = pageReq
            this.listLoading = true
            const params = {
                communityId: this.comm.id,
                ...this.page,
            }
            if (this.nodeType === 'team') {
                params.groupTeamId = this.treeNode.id
            } else if (this.nodeType === 'group') {
                params.groupId = this.treeNode.id
            }
            const { data } = await this.$ut.api('homecarelong/basic/groupTeam/attendant/listpg', params).finally(() => (this.listLoading = false))
            this.dataList = data || { info: [], page: 0, record: 0 }
        },
        handleSelect(rows) {
            this.$emit('select', rows)
        },
        onFullscreen(v) {
            this.fullscreen = v
        },
        onSelectRows(rows) {
            this.selectRows = rows
        },
        handleAddMember() {
            this.employeeDialogVisible = true
        },
        async handleEmployeeSelect(rows) {
            if (!rows.length) {
                this.$baseMessage('请选择要添加的员工', 'warning', 'ut-hey-message-warning')
                return
            }
            this.employeeDialogVisible = false
            const requests = rows.map((employee) => {
                return this.$ut.api('homecarelong/basic/groupTeam/attendant/add', {
                    communityId: this.comm.id,
                    groupTeamId: this.treeNode.id,
                    attendantId: employee.attendantId,
                })
            })
            this.listLoading = true
            await Promise.all(requests).finally(() => (this.listLoading = false))
            this.$baseMessage('添加成功', 'success', 'ut-hey-message-success')
            this.fetchData()
        },
        handleDeleteMembers() {
            const ids = this.selectRows.map((item) => item.id)
            this.deleteMembersByIds(ids)
        },
        handleDeleteMember(row) {
            this.deleteMembersByIds([row.id])
        },
        deleteMembersByIds(ids) {
            this.$baseConfirm('你确定要删除选中的成员吗', null, async () => {
                this.listLoading = true
                await this.$ut
                    .api('homecarelong/basic/groupTeam/attendant/delete', {
                        communityId: this.comm.id,
                        ids: ids,
                    })
                    .finally(() => (this.listLoading = false))
                this.$baseMessage('删除成功', 'success', 'ut-hey-message-success')
                this.fetchData()
            })
        },
        async handleSetHead(row) {
            this.listLoading = true
            await this.$ut.api('homecarelong/basic/groupTeam/attendant/headSet', {
                communityId: this.comm.id,
                ids: [row.id],
            })
            this.$baseMessage('设置成功', 'success', 'ut-hey-message-success')
            this.fetchData()
        },
        async handleCancelHead(row) {
            this.listLoading = true
            await this.$ut
                .api('homecarelong/basic/groupTeam/attendant/headCancel', {
                    communityId: this.comm.id,
                    ids: [row.id],
                })
                .finally(() => (this.listLoading = false))
            this.$baseMessage('取消成功', 'success', 'ut-hey-message-success')
            this.fetchData()
        },
        getNodeType(node) {
            if (!node || !node.id) return 'comm'
            if (node.id === this.comm.id) {
                return 'comm'
            }
            return node.type
        },
    },
}
</script>

<style lang="scss" scoped>
.empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;
}
</style>
