<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-row>
				<el-col :span="24">							
					<el-form-item label="资料名称：" prop="title">
						<el-input v-model.trim="form.title" placeholder="请输入资料名称" />
					</el-form-item>
				</el-col>
			
                <el-col :span="12">		
					<el-form-item label="显示索引：" prop="showIndex">
						<el-input v-model.trim="form.showIndex"/>
					</el-form-item>
				</el-col>
                <el-col :span="12">		
					<el-form-item label="启用：" prop="valid">
						<el-switch v-model.trim="form.valid"/>
					</el-form-item>
				</el-col>
			</el-row>
			
			
		</el-form>


		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'

export default {
	components:{
	},
	mixins: [],
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
		readonly: {
			type: Boolean,
			default: false,
		},
	},
	
	data() {
		return {
			loading: false,
			form: {
				id: '',
				title: '',
                valid:true,
                showIndex:'',
			},
			rules: {
				title: [{ required: true, trigger: 'blur', message: '请输入资料名称' }],
			},

		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarelong/basic/commData/type/info', {
				communityId:this.comm.id,
				type:'second',
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
		},
		
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
		
				await this.$ut.api('homecarelong/basic/commData/type/save', {
					communityId:this.comm.id,
					type:'second',
					...this.form,
				}).finally(()=>{this.loading = false})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
	},
}
</script>
<style lang="scss" scoped>
</style>
