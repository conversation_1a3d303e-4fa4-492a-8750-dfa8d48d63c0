<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<el-button icon="el-icon-plus" type="primary" @click="importCustomer=true">客户信息导入</el-button>
		<el-button icon="el-icon-plus" type="primary" @click="importEmployee=true">员工信息导入</el-button>

		<!-- 导入创建、修改 -->
		<ut-modal v-model="importCustomer" title="导入客户" width="800px">
			<excel-importer
				:height="500"
				fields-api="homecarelong/basic/import/customer/fields"
				headers-api="homecarelong/basic/import/headers"
				import-api="homecarelong/basic/import/customer/import"
				:ext-params="importParams" 
			/>
		</ut-modal>
		<ut-modal v-model="importEmployee" title="导入员工" width="800px">
			<excel-importer
				:height="500"
				fields-api="homecarelong/basic/import/employee/fields"
				headers-api="homecarelong/basic/import/headers"
				import-api="homecarelong/basic/import/employee/import"
				:ext-params="importParams" 
			/>
		</ut-modal>
		
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import ExcelImporter from '../../../components/excel-importer/index.vue'
export default {
	name: 'Batch',
	components: {
		ExcelImporter,
	},
	data() {
		return {
			fullscreen: false,
			treeloading: true,
			treeData: [],
			importCustomer: false,
			importEmployee: false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
		importParams() {
			return {
				module: 'long',
			}
		},
	},
	created() {
	},
	methods: {
		
	},
}
</script>

<style lang="scss" scoped>
</style>
