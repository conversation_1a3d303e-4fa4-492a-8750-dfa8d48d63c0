<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="80"
			:op-fixed="true"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			table-name="long-customer-type"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button">
				<el-button icon="el-icon-check" type="primary" :disabled="!selectRows.length" @click="handleMultiAudit($event)">审核</el-button>
			</template>
			<template #op="{ row }">
				<!-- <el-button type="text" @click="handleInfo(row)">详细</el-button> -->
				<el-button v-if="row.auditState == 0" type="text" @click="handleAudit(row)">审核</el-button>
			</template>
			<template #cell="{ row, item }">
				<div v-if="item.prop === 'color'" class="color-box">
					<span class="color-preview" :style="{ backgroundColor: row.color }"></span>
					{{ row[item.prop] }}
				</div>
				<div v-else-if="item.prop === 'customerSex'">
					<span v-if="row.customerSex == 1">男</span>
					<span v-if="row.customerSex == 2">女</span>
				</div>
				<div v-else-if="item.prop === 'changeType'">
					<span v-if="!row.customerSchedulingId">增加排班</span>
					<span v-else-if="row.destWorkDate">调至某日</span>
					<span v-else-if="row.editTime">调整时长</span>
					<!-- <span v-else>待定</span> -->
				</div>
				<div v-else-if="item.prop === 'workTime'">
					<span>{{ row.currWorkDate }} {{ row.currBegin }} ~ {{ row.currEnd }}</span>
				</div>
				<div v-else-if="item.prop === 'destTime'">
					<span v-if="row.destWorkDate">{{ row.destWorkDate }} {{ row.destBegin }} ~ {{ row.destEnd }}</span>
				</div>
				<div v-else-if="item.prop === 'isAudit'">
					<span v-if="row.auditState == 1">通过</span>
					<span v-else-if="row.auditState == 2">拒绝</span>
				</div>
				<span v-else>{{ row[item.prop] }}</span>
			</template>
		</form-list>

		<ut-modal v-model="auditFormVisible" title="审核" width="800px">
			<audit :id="selIds[0]" @close="auditFormVisible = false" @fetchData="fetchData" />
		</ut-modal>

		<ut-modal v-model="multiAuditFormVisible" title="审核" width="500px">
			<multi-audit :ids="selIds" @close="multiAuditFormVisible = false" @fetchData="fetchData" />
		</ut-modal>
	</div>
</template>

<script>
	import { mapGetters } from 'vuex'
	import Vue from 'vue'
	import FormList from '@/views/components/form-list'
	import MultiAudit from './multi-audit.vue'
	import Audit from './audit.vue'

	export default {
		name: 'Works',
		components: {
			FormList,
			MultiAudit,
			Audit,
		},
		props: {
			height: {
				type: Number,
				default: () => Vue.prototype.$baseTableHeight(1),
			},
			selectWindow: {
				type: Boolean,
				default: false,
			},
			selectSingle: {
				type: Boolean,
				default: false,
			},
			showList: {
				type: Boolean,
				default: false,
			},
		},
		data() {
			return {
				listLoading: false,
				fullscreen: false,
				columns: [
					{
						label: '序',
						align: 'center',
						prop: 'index',
						width: '50',
						fixed: 'left',
						show: true,
					},
					{
						label: '申请时间',
						align: 'center',
						prop: 'createTime',
						width: '160',
						fixed: 'left',
						show: true,
					},
					{
						label: '申请人',
						align: 'left',
						prop: 'attendantName',
						width: '110',
						fixed: 'left',
						show: true,
					},
					{
						label: '客户姓名',
						align: 'left',
						prop: 'customerName',
						width: '110',
						fixed: 'left',
						show: true,
					},
					{
						label: '性别',
						align: 'center',
						prop: 'customerSex',
						width: '60',
						show: true,
					},
					{
						label: '电话',
						align: 'center',
						prop: 'customerPhone',
						width: '120',
						show: true,
					},
					// {
					// 	label: '证件号码',
					// 	align: 'center',
					// 	prop: 'customerIdcard',
					// 	width:'180',
					// 	show: true,
					// },
					// {
					// 	label: '地址',
					// 	align: 'left',
					// 	prop: 'customerAddress',
					// 	minWidth:'220',
					// 	show: true,
					// },
					{
						label: '服务日期',
						align: 'center',
						prop: 'workDate',
						width: '120',
						show: true,
					},
					{
						label: '异常说明',
						align: 'left',
						prop: 'reason',
						width: '200',
						show: true,
					},
					// {
					// 	label: '调班时间',
					// 	align: 'center',
					// 	prop: 'workTime',
					// 	width:'200',
					// 	show: true,
					// },
					// {
					// 	label: '变更为',
					// 	align: 'center',
					// 	prop: 'destTime',
					// 	width:'200',
					// 	show: true,
					// },
					// {
					// 	label: '变更时长(分)',
					// 	align: 'center',
					// 	prop: 'editDuration',
					// 	width:'110',
					// 	show: true,
					// },
					{
						label: '审核状态',
						align: 'center',
						prop: 'isAudit',
						width: '80',
						show: true,
					},
					{
						label: '审核人',
						align: 'center',
						prop: 'auditor',
						width: '160',
						show: true,
					},
					{
						label: '审核时间',
						align: 'center',
						prop: 'auditTime',
						width: '160',
						show: true,
					},
					{
						label: '审核备注',
						align: 'left',
						prop: 'auditRemark',
						minWidth: '120',
						show: true,
					},
				],
				dataList: {
					info: [],
					page: 0,
					record: 0,
				},
				page: {
					key: '',
					pageindex: 1,
					pagesize: 20,
				},
				search: {
					isAudit: '',
					isSubmit: '',
				},
				selectRow: {},
				selectRows: [],
				auditFormVisible: false,
				multiAuditFormVisible: false,
				selIds: [],
			}
		},
		computed: {
			...mapGetters({
				comm: 'comm/comm',
			}),
		},
		created() {
			this.fetchData()
		},
		methods: {
			async fetchData(pageReq) {
				if (pageReq) this.page = pageReq
				this.listLoading = true
				const { data } = await this.$ut
					.api('homecarelong/care/work/error/listpg', {
						communityId: this.comm.id,
						...this.page,
						...this.search,
						isSubmit: true,
					})
					.finally(() => {
						this.listLoading = false
					})
				this.dataList = data
			},
			handleSelect(rows) {
				this.$emit('select', rows)
			},
			onFullscreen(v) {
				this.fullscreen = v
			},
			onSelectRows(rows) {
				this.selectRows = rows
			},
			handleAudit(row) {
				this.selIds = [row.id]
				this.auditFormVisible = true
			},
			handleMultiAudit() {
				if (this.selectRows.length == 0) {
					this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
					return
				}
				this.selIds = this.selectRows.map((item) => item.id)
				this.multiAuditFormVisible = true
			},
		},
	}
</script>

<style lang="scss" scoped>
	.color-box {
		display: flex;
		align-items: center;
		justify-content: center;

		.color-preview {
			width: 20px;
			height: 20px;
			margin-right: 10px;
			border-radius: 2px;
		}
	}
</style>
