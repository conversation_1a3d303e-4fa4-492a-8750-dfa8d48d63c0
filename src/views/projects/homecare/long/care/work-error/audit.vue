<template>
	<div v-loading="loading" class="ut-body">
		<!-- 审核信息展示 -->
		<div v-if="auditData.id" class="audit-info">
			<el-descriptions :column="6" border>
				<el-descriptions-item span="2" label="护理员">{{ auditData.attendantName }}</el-descriptions-item>
				<el-descriptions-item span="2" label="客户姓名">{{ auditData.customerName }}</el-descriptions-item>
				<el-descriptions-item span="2" label="客户电话">{{ auditData.customerPhone }}</el-descriptions-item>
				<el-descriptions-item span="2" label="服务日期">{{ auditData.workDate }}</el-descriptions-item>
				<el-descriptions-item span="2" label="签到时间">{{ auditData.newCheckInTime }}</el-descriptions-item>
				<el-descriptions-item span="2" label="签退时间">{{ auditData.newCheckOutTime }}</el-descriptions-item>
				<el-descriptions-item span="6" label="异常说明">{{ auditData.reason }}</el-descriptions-item>
			</el-descriptions>
		</div>

		<!-- 详细信息展示 -->
		<el-tabs v-if="auditData.id" v-model="tabName" type="border-card" class="info-tabs">
			<el-tab-pane name="main">
				<span slot="label">服务项目</span>
				<div>
					<ProjectList :projects="auditData.projects || []" :can-edit="false" :height="240" />
				</div>
			</el-tab-pane>
			<el-tab-pane name="dataSet" lazy>
				<span slot="label">服务资料</span>
				<div>
					<data-set :datas="auditData.datas || []" :height="280" readonly />
				</div>
			</el-tab-pane>
			<el-tab-pane name="applyDataSet" lazy>
				<span slot="label">证明材料</span>
				<div style="height: 280px;">
					<ut-media :url-list="applyDataUrl" :width="126" :height="126" :show-delete="false" />
				</div>
			</el-tab-pane>
		</el-tabs>

		<el-form ref="form" label-width="80px" :model="form" :rules="{ ...rules }" class="ut-form">
			<el-row>
				<el-col>
					<el-form-item label="审核：" prop="auditState">
						<el-radio-group v-model="form.auditState">
							<el-radio-button :label="1">通过</el-radio-button>
							<el-radio-button :label="2">不通过</el-radio-button>
						</el-radio-group>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="备注：" prop="remark">
						<el-input v-model.trim="form.remark" type="textarea" :rows="2" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>

		<div class="ut-edit-footer">
			<el-button type="primary" :disabled="form.auditState != 1 && form.auditState != 2" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
	import { mapGetters } from 'vuex'
	import { lang } from '@/common/utils/i18n'
	import ProjectList from './project-list.vue'
	import dataSet from './data-set.vue'
	export default {
		components: {
			ProjectList,
			dataSet,
		},
		mixins: [],
		props: {
			initData: {
				type: Object,
				default: () => ({}),
			},
			id: {
				type: String,
				default: '',
			},
		},
		data() {
			return {
				loading: false,
				tabName: 'main',
				auditData: {},
				form: {
					auditState: null,
					remark: '',
				},
			}
		},
		computed: {
			...mapGetters({
				comm: 'comm/comm',
			}),
			rules() {
				return { auditState: [{ required: true, trigger: ['blur', 'change'], message: '请选择审核状态' }] }
			},
			applyDataUrl() {
				return this.auditData.applyDatas || []
			},
		},
		beforeMount() {},
		mounted() {
			this.loadData()
		},
		methods: {
			lang,
			async loadData() {
				if (!this.id) return
				this.loading = true
				const { data } = await this.$ut
					.api('homecarelong/care/work/error/info', {
						communityId: this.comm.id,
						id: this.id,
					})
					.finally(() => {
						this.loading = false
					})
				this.auditData = data
			},
			save() {
				this.$refs['form'].validate(async (valid) => {
					if (!valid) return
					this.loading = true
					await this.$ut
						.api('homecarelong/care/work/error/audit', {
							communityId: this.comm.id,
							...this.form,
							ids: [this.id],
						})
						.finally(() => {
							this.loading = false
						})
					this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
					this.$emit('fetchData')
					this.$emit('close')
				})
			},
		},
	}
</script>
<style lang="scss" scoped>
	.audit-info {
		padding-bottom: 8px;
		line-height: 1.5;
	}
	.info-tabs {
		box-shadow: none;
	}
	.sign {
		margin: 10px 0;

		.form-item {
			display: flex;
			font-size: 16px;
			margin-bottom: 10px;
			.label {
				width: 100px;
				color: #666;
			}
			.value {
				color: #333;
			}
		}
	}
</style>
