<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="120px" :model="form" :rules="{...rules}" class="ut-form">
			<el-row>
                <el-col>	
                    <el-form-item label="审核：" prop="auditState">
                        <el-radio-group v-model="form.auditState">
                            <el-radio-button :label="1">通过</el-radio-button>
                            <el-radio-button :label="2">不通过</el-radio-button>
                        </el-radio-group>
					</el-form-item>	
                </el-col>
				<el-col :span="24">
					<el-form-item label="备注：" prop="remark">
						<el-input v-model.trim="form.remark" type="textarea" :rows="3"/>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>


		<div class="ut-edit-footer">
			<el-button type="primary" :disabled="form.auditState!=1 && form.auditState!=2" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>

	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
export default {
	components:{
	},
	mixins: [],
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
		ids:{
			type:Array,
			default: () => [],
		}
	},
	
	data() {
		return {
			loading: false,
			form: {
				auditState:null,
                remark:'',
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
		rules(){
            return {auditState: [{ required: true, trigger: ['blur','change'], message: '请选择审核状态' }]}
        },
	},
	beforeMount(){

	},
	mounted() {
	},
	methods: {
		lang,
		save() {
            this.$refs['form'].validate(async (valid) => {
                if (!valid) return
                this.loading = true
                this.$ut.api('homecarelong/care/work/error/audit', {
                    communityId: this.comm.id,
                    ...this.form,
                    ids:this.ids,
                }).then(()=>{
                    this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
                    this.$emit('fetchData')
                    this.$emit('close')
                }).finally(()=>{this.loading = false})
            })
        },
	},
}
</script>
<style lang="scss" scoped>
</style>
