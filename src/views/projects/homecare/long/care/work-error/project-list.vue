<template>
	<div>
		<form-list
			class="form-list"
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:show-op="false"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			:show-checkbox="false"
			:show-corner="false"
			:show-search="false"
		>
			<template #cell="{ row, item }">
				<span>{{ row[item.prop] }}</span>
			</template>
		</form-list>
	</div>
</template>
<script>
	import Vue from 'vue'
	import { mapGetters } from 'vuex'
	import FormList from '@/views/components/form-list'

	export default {
		name: 'WorkErrorProjects',
		components: {
			FormList,
		},
		props: {
			height: {
				type: [Number, String],
				default: () => Vue.prototype.$baseTableHeight(1),
			},
			selectWindow: {
				type: Boolean,
				default: false,
			},
			selectSingle: {
				type: Boolean,
				default: false,
			},
			showList: {
				type: Boolean,
				default: false,
			},
			projects: {
				type: Array,
				default: () => [],
			},
			canEdit: {
				type: <PERSON><PERSON>an,
				default: false,
			},
		},
		data() {
			return {
				listLoading: false,
				fullscreen: false,
				columns: [
					{
						label: '项目名称',
						align: 'left',
						prop: 'projectName',
						minWidth: 120,
						width: 'auto',
						show: true,
					},
					{
						label: '社保编号',
						align: 'center',
						prop: 'projectGovCode',
						width: '120',
						show: true,
					},
				],
				dataList: {
					info: [],
					page: 0,
					record: 0,
				},
				page: {
					key: '',
					pageindex: 1,
					pagesize: 20,
				},
				selectRow: {},
				selectRows: [],
			}
		},
		computed: {
			...mapGetters({
				comm: 'comm/comm',
			}),
		},
		watch: {
			projects: {
				handler(newVal) {
					this.dataList.info = newVal || []
					this.dataList.record = newVal ? newVal.length : 0
				},
				immediate: true,
			},
		},
		created() {
			this.dataList.info = this.projects || []
			this.dataList.record = this.projects ? this.projects.length : 0
		},
		methods: {},
	}
</script>
<style lang="scss" scoped>
	.form-list {
		:deep(.left-panel) {
			display: none;
		}
	}
</style>
