<template>
	<div v-loading="listLoading" class="box-wrapper ut-body" :style="{ height: height + 'px' }">
		<div class="box">
			<div v-for="(item, index) in dataList" :key="index" class="item">
				<div class="title">
					<span>{{ item.title }}</span>
				</div>
				<div class="content">
					<div v-for="(subItem, subIndex) in item.details" :key="subIndex" class="sub-item">
						<div class="sub-title">
							<span>{{ subItem.title }}</span>
							<span v-if="subItem.require" class="required">必填</span>
						</div>
						<div class="sub-content">
							<ut-media :url-list="subItem.files" :width="160" :height="120" delete-color="#efefef" :class="{ readonly }" :max-count="1" :show-delete="false" />
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	import { mapGetters } from 'vuex'
	import Vue from 'vue'

	export default {
		components: {},
		props: {
			height: {
				type: Number,
				default: () => Vue.prototype.$baseTableHeight(1),
			},
			datas: {
				type: Array,
				default: () => [],
			},
			readonly: {
				type: Boolean,
				default: true,
			},
		},
		data() {
			return {
				listLoading: false,
				dataList: [],
			}
		},
		computed: {
			...mapGetters({
				comm: 'comm/comm',
			}),
		},
		watch: {
			datas: {
				handler(newVal) {
					this.dataList = newVal || []
				},
				immediate: true,
			},
		},
		created() {
			this.dataList = this.datas || []
		},
		methods: {},
	}
</script>

<style lang="scss" scoped>
	.item {
		border-top: 1px solid #a0a0a0;
		border-right: 1px solid #a0a0a0;
		border-left: 1px solid #a0a0a0;
		display: flex;
		&:last-child {
			border-bottom: 1px solid #a0a0a0;
		}
		.title {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100px;
			border-right: 1px solid #a0a0a0;
			padding: 10px;
			font-weight: bold;
		}
		.content {
			padding: 10px;
			display: flex;
			.sub-item {
				margin: 5px;
			}
		}
	}

	.sub-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		border: 1px dashed #eee;
		border-radius: 4px;
		&:hover {
			border: 1px dashed #aaa;
			.sub-title {
				border-bottom: 1px dashed #aaa;
			}
		}
		.sub-title {
			font-weight: bold;
			border-bottom: 1px dashed #eee;
			width: 100%;
			text-align: center;
			padding: 4px;
			.required {
				color: #a0a0a0;
				font-weight: normal;
				font-size: 12px;
				padding-left: 10px;
			}
		}
	}

	.box-wrapper {
		display: flex;
		flex-direction: column;
		justify-content: space-between;

		.box {
			overflow-y: auto;
			padding-bottom: 8px;
		}
	}

	.footer {
		border-top: 1px dashed #ccc;
		padding-top: 16px;
		text-align: center;
	}

	:deep(.el-checkbox__label) {
		font-size: 16px;
		padding: 10px 10px 10px 0;
	}

	:deep(.el-upload--picture-card) {
		width: 160px;
		height: 120px;
		line-height: 120px;
	}

	.readonly {
		:deep(.el-upload--picture-card),
		:deep(.viewer-delete) {
			display: none !important;
		}
	}
</style>
