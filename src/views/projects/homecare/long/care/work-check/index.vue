<template>
    <form-tree-list :tree-width="220" :class="{ 'ut-fullscreen': fullscreen }">
        <template #tree>
            <tree :height="height" @node-click="nodeClick" />
        </template>
        <template #list>
            <div class="table-container">
                <form-list
                    :loading="listLoading"
                    :columns="columns"
                    :height="height"
                    :op-width="80"
                    :op-fixed="true"
                    :data-list="dataList"
                    :select-window="selectWindow"
                    :select-single="selectSingle"
                    :show-list="showList"
                    table-name="long-work-check"
                    @select="handleSelect"
                    @fetchData="fetchData"
                    @fullscreen="onFullscreen"
                    @selectRows="onSelectRows"
                >
                    <template #button>
                        <el-date-picker
                            v-model="search.month"
                            type="month"
                            placeholder="选择月份"
                            format="yyyy年MM月"
                            value-format="yyyy-MM"
                            style="width: 140px; margin-right: 10px"
                            :clearable="false"
                            @change="handleQuery"
                        />
                        <el-select v-model="search.state" placeholder="校对状态" style="width: 120px" @change="handleQuery">
                            <el-option label="全部" :value="0" />
                            <el-option label="未校对" :value="1" />
                            <el-option label="已校对" :value="2" />
                            <el-option label="无法校对" :value="3" />
                        </el-select>
                    </template>
                    <template #op="{ row }">
                        <el-button type="text" :disabled="isProofreadDisabled" @click="handleCheck(row)">校对</el-button>
                    </template>
                    <template #cell="{ row, item }">
                        <div v-if="item.prop === 'workDate'">
                            {{ row[item.prop] }}
                            <el-tag v-if="row.isJudan" type="danger" size="mini">拒单</el-tag>
                        </div>
                        <div v-else-if="item.prop === 'sex'">
                            <span v-if="row.sex == 1">男</span>
                            <span v-if="row.sex == 2">女</span>
                            <span v-if="row.sex == 0">未知</span>
                        </div>
                        <div v-else-if="item.prop === 'proofreadErrorRemark'">
                            <template v-if="row.isManual">
                                <span v-if="row.proofreadError" style="color: #f56c6c">{{ row.proofreadErrorRemark }}</span>
                                <span v-else style="color: #67c23a">无异常</span>
                            </template>
                            <span v-else>-</span>
                        </div>
                        <div v-else-if="item.prop === 'isJudan'">
                            <span v-if="row.isJudan" style="color: #f56c6c">是</span>
                            <span v-else style="color: #67c23a">否</span>
                        </div>
                        <div v-else-if="item.prop === 'checkInTime'">
                            <span v-if="row.checkInTime" style="color: #303133">{{ row.checkInTime }}</span>
                            <span v-else style="color: #f56c6c">未签到</span>
                        </div>
                        <div v-else-if="item.prop === 'checkOutTime'">
                            <span v-if="row.checkOutTime" style="color: #303133">{{ row.checkOutTime }}</span>
                            <span v-else style="color: #f56c6c">未签退</span>
                        </div>
                        <div v-else-if="item.prop === 'projectCount'">
                            <span v-if="row.projectCount && row.projectCount > 0" style="color: #303133">{{ row.projectCount }}</span>
                            <span v-else style="color: #f56c6c">无</span>
                        </div>
                        <div v-else-if="item.prop === 'dataCount'">
                            <span v-if="row.dataCount && row.dataCount > 0" style="color: #303133">{{ row.dataCount }}</span>
                            <span v-else style="color: #f56c6c">无</span>
                        </div>
                        <span v-else>{{ row[item.prop] }}</span>
                    </template>
                </form-list>

                <el-dialog
                    v-if="checkVisible"
                    v-dialog-drag
                    :visible.sync="checkVisible"
                    title="护理项目校对"
                    width="min(1800px, 98%)"
                    top="2%"
                    :close-on-click-modal="false"
                    append-to-body
                    :destroy-on-close="true"
                    :close-on-press-escape="true"
                >
                    <check :parent-data="selectRow" :month="search.month" @fetchData="fetchData" @close="checkVisible = false" />
                </el-dialog>
            </div>
        </template>
    </form-tree-list>
</template>

<script>
import { mapGetters } from 'vuex'
import Vue from 'vue'
import FormTreeList from '@/views/components/form-tree-list'
import FormList from '@/views/components/form-list'
import Tree from './tree.vue'
import Check from './check.vue'

export default {
    name: 'WorkCheck',
    components: {
        FormTreeList,
        FormList,
        Tree,
        Check,
    },
    props: {
        height: {
            type: Number,
            default: () => Vue.prototype.$baseTableHeight(1),
        },
        selectWindow: {
            type: Boolean,
            default: false,
        },
        selectSingle: {
            type: Boolean,
            default: false,
        },
        showList: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            listLoading: false,
            fullscreen: false,
            selectRows: [],
            selectRow: {},
            checkVisible: false,
            treeNode: {},
            page: {
                pageNum: 1,
                pageSize: 20,
            },
            search: {
                month: (() => {
                    const now = new Date()
                    const year = now.getFullYear()
                    const month = (now.getMonth() + 1).toString().padStart(2, '0')
                    return `${year}-${month}`
                })(),
                state: 0,
            },
            dataList: {
                record: 0,
                page: 0,
                info: [],
            },
            columns: [
                {
                    label: '护理日期',
                    align: 'center',
                    prop: 'workDate',
                    width: '150',
                    fixed: 'left',
                    show: true,
                },
                {
                    label: '护理员',
                    align: 'center',
                    prop: 'attendantName',
                    width: '120',
                    // fixed: 'left',
                    show: true,
                },
                {
                    label: '客户姓名',
                    align: 'center',
                    prop: 'name',
                    width: '120',
                    // fixed: 'left',
                    show: true,
                },
                {
                    label: '分组',
                    align: 'center',
                    prop: 'groupName',
                    width: '120',
                    show: true,
                },
                {
                    label: '性别',
                    align: 'center',
                    prop: 'sex',
                    width: '60',
                    show: true,
                },
                {
                    label: '电话',
                    align: 'center',
                    prop: 'phone',
                    width: '120',
                    show: true,
                },
                // {
                //     label: '证件号码',
                //     align: 'center',
                //     prop: 'idcard',
                //     width: '180',
                //     show: true,
                // },
                {
                    label: '签到时间',
                    align: 'center',
                    prop: 'checkInTime',
                    width: '160',
                    show: true,
                },
                {
                    label: '签退时间',
                    align: 'center',
                    prop: 'checkOutTime',
                    width: '160',
                    show: true,
                },
                {
                    label: '排班时长',
                    align: 'center',
                    prop: 'schedulingDuration',
                    width: '80',
                    show: true,
                },
                {
                    label: '实际时长',
                    align: 'center',
                    prop: 'totalDuration',
                    width: '80',
                    show: true,
                },
                {
                    label: '项目数量',
                    align: 'center',
                    prop: 'projectCount',
                    width: '80',
                    show: true,
                },
                {
                    label: '文件数量',
                    align: 'center',
                    prop: 'dataCount',
                    width: '80',
                    show: true,
                },
                {
                    label: '校对时间',
                    align: 'center',
                    prop: 'lastManualTime',
                    width: '160',
                    show: true,
                },
                {
                    label: '校对人',
                    align: 'center',
                    prop: 'lastManualUserName',
                    width: '120',
                    show: true,
                },
                {
                    label: '校对信息',
                    align: 'center',
                    prop: 'proofreadErrorRemark',
                    width: '170',
                    fixed: 'right',
                    show: true,
                },
            ],
        }
    },
    computed: {
        ...mapGetters({
            comm: 'comm/comm',
        }),
        isProofreadDisabled() {
            return this.search.state === 3
        },
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        async fetchData(pageReq) {
            if (pageReq) this.page = pageReq
            if(!this.treeNode.id) return
            this.listLoading = true
            const queryParams = {
                communityId: this.comm.id,
                ...this.page,
                ...this.search,
            }
            if (this.treeNode.type) {
                if (this.treeNode.type === 'group') {
                    queryParams.groupId = this.treeNode.id
                } else if (this.treeNode.type === 'team') {
                    queryParams.groupId = this.treeNode.pid || this.treeNode.pId
                    queryParams.groupTeamId = this.treeNode.id
                }
            }
            const { data } = await this.$ut.api('homecarelong/care/work/proofread/listpg', queryParams).finally(() => (this.listLoading = false))
            this.dataList = data
        },
        handleSelect(rows) {
            this.$emit('select', rows)
        },
        onFullscreen(v) {
            this.fullscreen = v
        },
        onSelectRows(rows) {
            this.selectRows = rows
        },
        handleCheck(row) {
            this.selectRow = row
            this.checkVisible = true
        },
        handleQuery() {
            this.page.pageNum = 1
            this.fetchData()
        },
        nodeClick(node) {
            this.treeNode = node
            this.page.pageNum = 1
            this.fetchData()
        },
    },
}
</script>
