<template>
    <div class="tree-box" :style="{ height: height + 'px' }">
        <div v-loading="loading">
            <ut-tree :tree="treeData" :expanded-keys="expandedKeys" @nodeClick="treeNodeClick">
                <template #icon></template>
            </ut-tree>
        </div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
    props: {
        height: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            treeData: [],
            loading: false,
            treeSelectNode: {},
            expandedKeys: [],
        }
    },
    computed: {
        ...mapGetters({
            comm: 'comm/comm',
        }),
    },
    created() {
        this.getTreeData()
    },
    methods: {
        async getTreeData() {
            this.loading = true

            try {
                const { data } = await this.$ut.api('homecarelong/basic/groupTeam/groupList', {
                    communityId: this.comm.id,
                })

                let treeNodes = []
                if (data) {
                    treeNodes = data.map(item => ({
                        ...item,
                        label: item.name,
                        id: item.id,
                        pId: item.pId || '',
                        type: item.type,
                    }))
                }
                const groupNodes = treeNodes.filter(item => item.type === 'group')
                const teamNodes = treeNodes.filter(item => item.type === 'team')
                groupNodes.forEach(group => {
                    group.children = teamNodes.filter(team => team.pId === group.id)
                })
                const rootNode = {
                    id: 'root',
                    label: '全部',
                    type: 'root',
                    pId: '',
                    children: groupNodes,
                }
                this.treeData = [rootNode]
                if (this.treeData.length) {
                    this.treeSelectNode = this.treeData[0]
                    this.$set(this.treeData[0], 'select', true)
                    this.expandedKeys = ['root']
                    let temp = Object.assign({}, this.treeData[0])
                    this.$emit('node-click', temp)
                }
            } finally {
                this.loading = false
            }
        },
        treeNodeClick(node) {
            this.clearSelection(this.treeData)
            this.treeSelectNode = node
            this.$set(node, 'select', true)
            let temp = Object.assign({}, node)
            this.$emit('node-click', temp)
        },
        clearSelection(nodes) {
            nodes.forEach(node => {
                this.$set(node, 'select', false)
                if (node.children && node.children.length > 0) {
                    this.clearSelection(node.children)
                }
            })
        },
    },
}
</script>

<style lang="scss" scoped>
.tree-box {
    overflow: auto;
}
</style>
