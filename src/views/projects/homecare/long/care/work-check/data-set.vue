<template>
    <div class="box">
        <div v-if="datas" class="sub-item">
            <div class="sub-title">
                <span>{{ datas.title }}</span>
            </div>
            <div v-if="datas.files?.length" class="sub-content" :style="{ height: baseTableHeight(2) }">
                <ut-media class="readonly" :url-list="datas.files" :width="350" :height="baseTableHeight(3)" delete-color="#efefef" :max-count="1" :show-delete="false" />
            </div>
            <el-empty v-else description="图片资料为空" />
        </div>
    </div>
</template>

<script>
export default {
    components: {},
    props: {
        datas: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {}
    },
    computed: {},
    watch: {},
    created() {},
    methods: {
        baseTableHeight(i) {
            return `${this.$baseTableHeight(i)}px`
        },
    },
}
</script>

<style lang="scss" scoped>
.box {
    padding-left: 8px;
}

.sub-item {
    border: 1px solid #ebeef5;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-y: auto;

    .sub-title {
        font-weight: bold;
        width: 100%;
        text-align: center;
        padding: 4px;
    }
}

.sub-content :deep(.viewer-box) {
    align-items: center;

    .el-image__inner {
        object-fit: contain !important;
    }
}
</style>
