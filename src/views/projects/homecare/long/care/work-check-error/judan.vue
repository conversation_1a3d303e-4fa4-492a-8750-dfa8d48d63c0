<template>
    <div v-loading="loading">
        <el-form ref="form" label-width="80px" :model="form" :rules="{ ...rules }" class="ut-form">
            <!-- <el-row>
            <el-col :span="24">
                <el-form-item label="服务时长：" prop="duration">
                    <el-input-number v-model="form.duration" :min="1" :precision="0" controls-position="right" style="width: 200px" />
                    <span style="margin-left: 8px; color: #909399">分钟</span>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="拒单原因：" prop="reason">
                    <el-input v-model.trim="form.reason" type="textarea" :rows="4" placeholder="请输入拒单原因" />
                </el-form-item>
            </el-col>
        </el-row> -->
            <el-form-item label="拒单原因：" prop="reason">
                <el-input v-model.trim="form.reason" type="textarea" :rows="4" placeholder="请输入拒单原因" />
            </el-form-item>
        </el-form>

        <div class="ut-edit-footer">
            <el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
            <el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
        </div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'

export default {
    components: {},
    mixins: [],
    props: {
        parentData: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            loading: false,
            form: {
                // duration: 0,
                reason: '',
            },
        }
    },
    computed: {
        ...mapGetters({
            comm: 'comm/comm',
        }),
        rules() {
            return {
                // duration: [
                //     { required: true, trigger: ['change'], message: '请输入服务时长' },
                //     { type: 'number', min: 0, trigger: ['blur', 'change'], message: '服务时长不能为负数' },
                // ],
            }
        },
    },
    beforeMount() {
        this.form = Object.assign(this.form, this.parentData)
    },
    mounted() {
        // const totalDuration = this.parentData?.totalDuration || 0
        // const schedulingDuration = this.parentData?.schedulingDuration || 0
        // const maxDuration = Math.min(totalDuration, schedulingDuration)
        // this.form.duration = maxDuration > 0 ? maxDuration : null
    },
    methods: {
        lang,
        save() {
            this.$refs['form'].validate(async (valid) => {
                if (!valid) return
                this.loading = true
                await this.$ut
                    .api('homecarelong/care/work/judan/save', {
                        communityId: this.comm.id,
                        ...this.form,
                    })
                    .finally(() => (this.loading = false))
                this.$baseMessage('拒单成功！', 'success', 'ut-hey-message-success')
                this.$emit('fetchData')
                this.$emit('close')
            })
        },
    },
}
</script>
<style lang="scss" scoped></style>
