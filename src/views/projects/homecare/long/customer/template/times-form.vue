<template>
	<div>
		<el-form ref="form" :model="form" :rules="rules" label-width="80px" class="ut-form">
			<el-form-item label="班次：" prop="serviceTimes">
				<el-input-number v-model="form.serviceTimes" :disabled="!!form.id" :min="1" :max="99" placeholder="请输入班次序号" />
			</el-form-item>

			<project-list :projects="form.projects" :parent-data="parentData" :height="300" :can-edit="false" @update:projects="updateProjects" />
		</el-form>

		<div class="ut-edit-footer">
			<el-button type="primary" :loading="loading" @click="handleSave">保存</el-button>
			<el-button @click="handleClose">取消</el-button>
		</div>
	</div>
</template>

<script>
	import { mapGetters } from 'vuex'
	import ProjectList from './project-list.vue'

	export default {
		name: 'TimesForm',
		components: {
			ProjectList,
		},
		props: {
			initData: {
				type: Object,
				default: () => ({}),
			},
			parentData: {
				type: Object,
				default: () => ({}),
			},
		},
		data() {
			return {
				loading: false,
				projectDialogVisible: false,
				form: {
					id: '',
					serviceTimes: 1,
					projects: [],
				},
				projectSelectData: {},
				rules: {
					serviceTimes: [
						{ required: true, message: '请输入班次序号', trigger: 'blur' },
						{ type: 'number', min: 1, max: 99, message: '班次序号必须在1-99之间', trigger: 'blur' },
					],
				},
			}
		},
		computed: {
			...mapGetters({
				comm: 'comm/comm',
			}),
		},
		watch: {
			initData: {
				handler(val) {
					this.form = {
						id: val.id || '',
						serviceTimes: val.serviceTimes || 1,
						projects: val.projects || [],
					}
				},
				immediate: true,
				deep: true,
			},
		},
		methods: {
			handleAddProject() {
				this.projectDialogVisible = true
			},
			handleProjectSelect(projects) {
				this.projectDialogVisible = false
				if (projects && projects.length > 0) {
					const existingIds = this.form.projects.map((p) => p.id)
					const newProjects = projects
						.filter((p) => !existingIds.includes(p.id))
						.map((item) => ({
							id: item.id,
							projectName: item.name,
							projectCode: item.code,
							createTime: item.createTime,
							govCode: item.govCode,
							requireMinDuration: item.minDuration || item.requireMinDuration || 0,
							requireMaxDuration: item.maxDuration || item.requireMaxDuration || 0,
							remark: item.remark,
						}))
					this.form.projects = [...this.form.projects, ...newProjects]
				}
			},
			updateProjects(projects) {
				this.form.projects = projects || []
			},
			handleSave() {
				this.$refs.form.validate(async (valid) => {
					if (!valid) return
					if (!this.form.id && this.initData.existingServiceTimes.includes(this.form.serviceTimes)) {
						this.$baseMessage('班次已存在', 'error', 'ut-hey-message-error')
						return
					}
					if (!this.form.projects || this.form.projects.length === 0) {
						this.$baseMessage('请至少选择一个服务项目', 'error', 'ut-hey-message-error')
						return
					}
					this.loading = true
					try {
						const saveData = {
							communityId: this.comm.id,
							templateId: this.parentData.id,
							id: this.form.id,
							serviceTimes: this.form.serviceTimes,
							projectIds: this.form.projects.map((project) => project.projectId),
						}
						await this.$ut.api('homecarelong/customer/project/template/times/save', saveData)
						this.$baseMessage('保存成功', 'success', 'ut-hey-message-success')
						this.$emit('save')
					} catch (error) {
						console.error(error)
					} finally {
						this.loading = false
					}
				})
			},
			handleClose() {
				this.$emit('close')
			},
		},
	}
</script>

<style lang="scss" scoped></style>
