<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="160"
			:op-fixed="true"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			:show-search="true"
			table-name="long-customer-project-template"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button">
				<el-button icon="el-icon-plus" type="primary" :disabled="!treeNode.id || treeNode.isRoot" @click="handleAdd">新增模板</el-button>
				<el-button icon="el-icon-delete" type="danger" :disabled="!selectRows.length" @click="handleDelete()">删除</el-button>
			</template>
			<template #op="{ row }">
				<el-button type="text" @click="handleEdit(row)">编辑</el-button>
				<el-button type="text" @click="handleTimes(row)">班次</el-button>
				<el-button type="text" @click="handleDelete(row)">删除</el-button>
			</template>
			<template #cell="{ row, item }">
				<div v-if="item.prop === 'sex'">
					<span v-if="row.sex == 1">男</span>
					<span v-else-if="row.sex == 2">女</span>
					<span v-else>-</span>
				</div>
				<div v-else-if="item.prop === 'imgHead'" class="avatar-cell">
					<el-avatar v-if="row.imgHead" :src="row.imgHead" :size="30" />
					<span v-else>-</span>
				</div>
				<span v-else>{{ row[item.prop] }}</span>
			</template>
		</form-list>

		<!-- 新增/编辑模板 -->
		<ut-modal v-model="dialogFormVisible" :title="selectRow.id ? '编辑模板' : '新增模板'" width="400px">
			<template-form :init-data="selectRow" @save="handleSave" @close="dialogFormVisible = false" />
		</ut-modal>

		<!-- 班次管理 -->
		<ut-modal v-model="timesFormVisible" title="班次管理" width="800px">
			<times-list :parent-data="selectRow" :height="450" @close="timesFormVisible = false" />
		</ut-modal>
	</div>
</template>

<script>
	import FormList from '@/views/components/form-list'
	import { mapGetters } from 'vuex'
	import Vue from 'vue'
	import TemplateForm from './template-form.vue'
	import TimesList from './times-list.vue'

	export default {
		name: 'CustomerProjectTemplateList',
		components: {
			FormList,
			TemplateForm,
			TimesList,
		},
		props: {
			height: {
				type: Number,
				default: () => Vue.prototype.$baseTableHeight(1),
			},
			selectWindow: {
				type: Boolean,
				default: false,
			},
			selectSingle: {
				type: Boolean,
				default: false,
			},
			showList: {
				type: Boolean,
				default: false,
			},
			treeNode: {
				type: Object,
				default: () => {},
			},
		},
		data() {
			return {
				listLoading: false,
				fullscreen: false,
				columns: [
					{
						label: '客户姓名',
						align: 'center',
						prop: 'name',
						width: '120',
						show: true,
					},
					{
						label: '性别',
						align: 'center',
						prop: 'sex',
						width: '60',
						show: true,
					},
					{
						label: '电话',
						align: 'center',
						prop: 'phone',
						width: '120',
						show: true,
					},
					{
						label: '证件号码',
						align: 'center',
						prop: 'idcard',
						minWidth: '180',
						show: true,
					},
					{
						label: '头像',
						align: 'center',
						prop: 'imgHead',
						width: '80',
						show: true,
					},
					{
						label: '班次数',
						align: 'center',
						prop: 'timesCount',
						width: '100',
						show: true,
					},
					{
						label: '备注',
						align: 'left',
						prop: 'remark',
						minWidth: '150',
						show: true,
					},
					// {
					// 	label: '护理员',
					// 	align: 'center',
					// 	prop: 'attendantName',
					// 	width: '100',
					// 	show: true,
					// },
					// {
					// 	label: '护理员电话',
					// 	align: 'center',
					// 	prop: 'attendantPhone',
					// 	width: '120',
					// 	show: true,
					// },
				],
				dataList: {
					info: [],
					page: 0,
					record: 0,
				},
				page: {
					key: '',
					pageindex: 1,
					pagesize: 20,
					order: '',
				},
				selectRow: {},
				selectRows: [],
				dialogFormVisible: false,
				timesFormVisible: false,
			}
		},
		computed: {
			...mapGetters({
				comm: 'comm/comm',
			}),
		},
		watch: {
			treeNode: {
				deep: true,
				handler() {
					this.fetchData()
				},
			},
		},
		methods: {
			async fetchData(pageReq) {
				if (pageReq) this.page = pageReq
				const { id, type } = this.treeNode
				const idData = {}
				if (type == 'group') {
					idData.groupId = id
				} else if (type == 'attendant') {
					idData.attendantId = id
				}
				this.listLoading = true
				const { data } = await this.$ut
					.api('homecarelong/customer/project/template/listpg', {
						communityId: this.comm.id,
						...idData,
						...this.page,
					})
					.finally(() => {
						this.listLoading = false
					})
				this.dataList = data
			},
			handleSelect(rows) {
				this.$emit('select', rows)
			},
			onFullscreen(v) {
				this.fullscreen = v
			},
			onSelectRows(rows) {
				this.selectRows = rows
			},
			handleAdd() {
				this.selectRow = {}
				this.dialogFormVisible = true
			},
			handleEdit(row) {
				this.selectRow = row
				this.dialogFormVisible = true
			},
			handleTimes(row) {
				this.selectRow = row
				this.timesFormVisible = true
			},
			handleDelete(row) {
				let ids = []
				if (row && row.id) {
					ids = [row.id]
				} else {
					if (this.selectRows.length > 0) {
						ids = this.selectRows.map((item) => item.id)
					} else {
						this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
						return
					}
				}
				this.$baseConfirm('你确定要删除选中的模板吗？', null, async () => {
					this.$ut
						.api('homecarelong/customer/project/template/delete', {
							communityId: this.comm.id,
							ids: ids,
						})
						.then(() => {
							this.$baseMessage('删除成功', 'success', 'ut-hey-message-success')
							this.fetchData()
						})
				})
			},
			handleSave() {
				this.dialogFormVisible = false
				this.fetchData()
			},
		},
	}
</script>

<style lang="scss" scoped>
	.avatar-cell {
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>
