<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:columns="columns"
			:height="height"
			:op-width="80"
			:op-fixed="true"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-checkbox="true"
			:show-corner="false"
			:show-search="false"
			:show-pagination="false"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button">
				<el-button v-if="!selectWindow" icon="el-icon-plus" type="primary" @click="handleAdd">添加</el-button>
				<el-button v-if="!selectWindow" icon="el-icon-delete" type="danger" :disabled="!selectRows.length" @click="handleDelete($event)">删除</el-button>
				<el-button v-if="selectWindow" icon="el-icon-plus" type="primary" :disabled="!selectRows.length" @click="$emit('handleSelect', selectRows)">选择</el-button>
			</template>
			<template #op="{ row }">
				<el-button type="text" @click="handleDelete(row)">删除</el-button>
			</template>
			<template #cell="{ row, item }">
				<span>{{ row[item.prop] }}</span>
			</template>
		</form-list>

		<ut-modal v-model="dialogFormVisible" width="700px" title="服务项目选择" top="90px">
			<all-project-list :customer-id="parentData.customerId" :height="400" :select-window="true" :select-single="false" @select="handleSelect" @close="dialogFormVisible = false" />
		</ut-modal>
	</div>
</template>
<script>
	import Vue from 'vue'
	import { mapGetters } from 'vuex'
	import AllProjectList from '../../customer/project/sel-list.vue'
	import FormList from '@/views/components/form-list'

	export default {
		name: 'WorkProjectList',
		components: {
			AllProjectList,
			FormList,
		},
		props: {
			height: {
				type: Number,
				default: () => Vue.prototype.$baseTableHeight(1),
			},
			selectWindow: {
				type: Boolean,
				default: false,
			},
			selectSingle: {
				type: Boolean,
				default: false,
			},
			parentData: {
				type: Object,
				default: () => ({}),
			},
			projects: {
				type: Array,
				default: () => [],
			},
		},
		data() {
			return {
				listLoading: true,
				fullscreen: false,
				columns: [
					{
						label: '名称',
						align: 'left',
						width: '300',
						prop: 'name',
						show: true,
					},
					{
						label: '对应社保编号',
						align: 'center',
						width: '160',
						prop: 'govCode',
						show: true,
					},
					{
						label: '最小时长',
						align: 'center',
						width: '90',
						prop: 'minDuration',
						show: true,
					},
					{
						label: '最大时长',
						align: 'center',
						width: '90',
						prop: 'maxDuration',
						show: true,
					},
				],
				page: {
					key: '',
					pageindex: 1,
					pagesize: 20,
					order: '',
				},
				selectRows: [],
				selectRow: {},
				dialogFormVisible: false,
			}
		},
		computed: {
			...mapGetters({
				comm: 'comm/comm',
			}),
			dataList: {
				get() {
					return {
						info: this.projects,
						page: 0,
						record: this.projects.length,
					}
				},
			},
		},
		methods: {
			onFullscreen(v) {
				this.fullscreen = v
			},
			onSelectRows(rows) {
				this.selectRows = rows
			},
			handleAdd() {
				this.selectRow = {}
				this.dialogFormVisible = true
			},
			handleSelect(rows) {
				const projectList = [
					...this.projects,
					...rows.map((item) => ({
						...item,
						name: item.projectName,
						code: item.projectCode,
					})),
				].filter((item, index, arr) => arr.findIndex((i) => i.projectId === item.projectId) === index)
				this.$emit('update:projects', projectList)
				this.dialogFormVisible = false
			},
			handleDelete(row) {
				let ids = []
				if (row.id) {
					ids = [row.id]
				} else {
					if (this.selectRows.length > 0) {
						ids = this.selectRows.map((item) => item.id)
					} else {
						this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
						return
					}
				}
				this.$emit(
					'update:projects',
					this.projects.filter((item) => !ids.includes(item.id))
				)
			},
			fetchData() {},
		},
	}
</script>

<style lang="scss" scoped>
	.img-box {
		width: 30px;
		height: 30px;
		border-radius: 3px;
		overflow: hidden;
		img {
			width: 100%;
			height: 100%;
		}
	}
</style>
