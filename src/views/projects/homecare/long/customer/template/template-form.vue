<template>
	<div>
		<el-form ref="form" :model="form" :rules="rules" label-width="100px" class="ut-form">
			<el-form-item label="客户：" prop="customerName">
				<el-input v-model="form.customerName" :disabled="!!form.id" placeholder="请选择客户" readonly @focus="customerFormVisible = true" />
			</el-form-item>
			<el-form-item label="有效期开始：" prop="validBegin">
				<el-date-picker v-model="form.validBegin" :disabled="!customerSelected" type="date" placeholder="选择开始日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%" />
			</el-form-item>
			<el-form-item label="有效期结束：" prop="validEnd">
				<el-date-picker v-model="form.validEnd" :disabled="!customerSelected" type="date" placeholder="选择结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%" />
			</el-form-item>
		</el-form>

		<div class="ut-edit-footer">
			<el-button type="primary" :loading="loading" @click="handleSave">保存</el-button>
			<el-button @click="handleClose">取消</el-button>
		</div>

		<!-- 客户选择弹窗 -->
		<ut-modal v-model="customerFormVisible" title="选择客户" width="600px">
			<user-select :select-window="true" :select-single="true" :height="400" module="long" @select="handleSelectCustomer" @close="customerFormVisible = false" />
		</ut-modal>
	</div>
</template>

<script>
	import { mapGetters } from 'vuex'
	import UserSelect from '../../care/work-edit/user.vue'

	export default {
		name: 'TemplateForm',
		components: {
			UserSelect,
		},
		props: {
			initData: {
				type: Object,
				default: () => ({}),
			},
		},
		data() {
			return {
				loading: false,
				customerFormVisible: false,
				customer: {
					id: '',
					name: '',
				},
				form: {
					id: '',
					customerName: '',
					validBegin: '',
					validEnd: '',
				},
				rules: {
					validBegin: [{ required: true, message: '请选择有效期开始日期', trigger: 'change' }],
					validEnd: [{ required: true, message: '请选择有效期结束日期', trigger: 'change' }],
				},
			}
		},
		computed: {
			...mapGetters({
				comm: 'comm/comm',
			}),
			customerSelected() {
				return !!this.customer.id
			},
		},
		watch: {
			initData: {
				handler(val) {
					this.form = {
						id: val.id || '',
						customerName: val.name || '',
						validBegin: val.validBegin || '',
						validEnd: val.validEnd || '',
					}
					this.customer = {
						id: val.customerId || '',
						name: val.customerName || '',
					}
				},
				immediate: true,
				deep: true,
			},
		},
		methods: {
			handleSelectCustomer(rows) {
				this.customerFormVisible = false
				if (!rows || !rows[0]) return
				this.customer.id = rows[0].id
				this.customer.name = rows[0].name
				this.form.customerName = rows[0].name
			},
			handleSave() {
				this.$refs.form.validate(async (valid) => {
					if (!valid) return

					if (!this.customer.id) {
						this.$baseMessage('请选择客户', 'error', 'ut-hey-message-error')
						return
					}

					if (this.form.validBegin && this.form.validEnd) {
						if (new Date(this.form.validBegin) >= new Date(this.form.validEnd)) {
							this.$baseMessage('有效期开始日期必须小于结束日期', 'error', 'ut-hey-message-error')
							return
						}
					}

					this.loading = true
					try {
						await this.$ut.api('homecarelong/customer/project/template/save', {
							communityId: this.comm.id,
							customerId: this.customer.id,
							...this.form,
						})
						this.$baseMessage('保存成功', 'success', 'ut-hey-message-success')
						this.$emit('save')
					} finally {
						this.loading = false
					}
				})
			},
			handleClose() {
				this.$emit('close')
			},
		},
	}
</script>

<style lang="scss" scoped></style>
