<template>
	<div>
		<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
			<form-list
				:loading="listLoading"
				:columns="columns"
				:height="height"
				:op-width="120"
				:op-fixed="true"
				:data-list="dataList"
				:show-search="true"
				table-name="customer-project-template-times"
				@fetchData="fetchData"
				@selectRows="onSelectRows"
			>
				<template slot="button">
					<el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增班次</el-button>
					<el-button icon="el-icon-delete" type="danger" :disabled="!selectRows.length" @click="handleDelete()">删除</el-button>
				</template>
				<template #op="{ row }">
					<el-button type="text" @click="handleEdit(row)">编辑</el-button>
					<el-button type="text" @click="handleDelete(row)">删除</el-button>
				</template>
				<template #cell="{ row, item }">
					<div v-if="item.prop === 'serviceTimes'">第{{ row.serviceTimes }}次</div>
					<div v-else-if="item.prop === 'projects'" class="tag-container">
						<el-tag v-for="project in row.projects" :key="project.id" size="small" type="info">
							{{ project.govCode }}
						</el-tag>
					</div>
					<div v-else-if="item.prop === 'duration'">{{ getDurationRange(row) }}</div>
					<span v-else>{{ row[item.prop] }}</span>
				</template>
			</form-list>
		</div>

		<div class="ut-edit-footer">
			<el-button @click="handleClose">关闭</el-button>
		</div>

		<!-- 新增/编辑班次 -->
		<ut-modal v-model="dialogFormVisible" :title="selectRow.id ? '编辑班次' : '新增班次'" width="750px">
			<times-form :init-data="selectRow" :parent-data="parentData" @save="handleSave" @close="dialogFormVisible = false" />
		</ut-modal>
	</div>
</template>

<script>
	import FormList from '@/views/components/form-list'
	import { mapGetters } from 'vuex'
	import Vue from 'vue'
	import TimesForm from './times-form.vue'

	export default {
		name: 'TimesList',
		components: {
			FormList,
			TimesForm,
		},
		props: {
			height: {
				type: Number,
				default: () => Vue.prototype.$baseTableHeight(1),
			},
			parentData: {
				type: Object,
				default: () => ({}),
			},
		},
		data() {
			return {
				listLoading: false,
				fullscreen: false,
				columns: [
					{
						label: '班次',
						align: 'center',
						prop: 'serviceTimes',
						width: '100',
						show: true,
					},
					{
						label: '项目',
						align: 'left',
						prop: 'projects',
						minWidth: '200',
						show: true,
					},
					{
						label: '时长',
						align: 'center',
						prop: 'duration',
						width: '150',
						show: true,
					},
				],
				dataList: {
					info: [],
					page: 0,
					record: 0,
				},
				page: {
					key: '',
					pageindex: 1,
					pagesize: 20,
					order: '',
				},
				selectRow: {},
				selectRows: [],
				dialogFormVisible: false,
			}
		},
		computed: {
			...mapGetters({
				comm: 'comm/comm',
			}),
		},
		created() {
			this.fetchData()
		},
		methods: {
			getDurationRange(row) {
				if (!row.projects || row.projects.length === 0) return '0分钟'
				const minDuration = row.projects.reduce((total, project) => {
					return total + (project.minDuration || 0)
				}, 0)
				const maxDuration = row.projects.reduce((total, project) => {
					return total + (project.maxDuration || 0)
				}, 0)
				if (minDuration === maxDuration) {
					return `${minDuration}分钟`
				} else {
					return `${minDuration}分钟-${maxDuration}分钟`
				}
			},
			async fetchData(pageReq) {
				if (pageReq) this.page = pageReq
				if (!this.parentData.id) {
					this.dataList = { info: [], page: 0, record: 0 }
					return
				}

				this.listLoading = true
				const { data } = await this.$ut
					.api('homecarelong/customer/project/template/times/listpg', {
						communityId: this.comm.id,
						templateId: this.parentData.id,
						...this.page,
					})
					.finally(() => {
						this.listLoading = false
					})
				this.dataList = data
			},
			onSelectRows(rows) {
				this.selectRows = rows
			},
			handleAdd() {
				const existingServiceTimes = this.getExistingServiceTimes()
				this.selectRow = {
					existingServiceTimes,
					serviceTimes: this.getNextAvailableServiceTimes(existingServiceTimes),
				}
				this.dialogFormVisible = true
			},
			handleEdit(row) {
				this.selectRow = {
					...row,
					existingServiceTimes: this.getExistingServiceTimes(),
				}
				this.dialogFormVisible = true
			},
			handleDelete(row) {
				let ids = []
				if (row && row.id) {
					ids = [row.id]
				} else {
					if (this.selectRows.length > 0) {
						ids = this.selectRows.map((item) => item.id)
					} else {
						this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
						return
					}
				}
				this.$baseConfirm('你确定要删除选中的班次吗？', null, async () => {
					this.$ut
						.api('homecarelong/customer/project/template/times/delete', {
							communityId: this.comm.id,
							templateId: this.parentData.id,
							ids: ids,
						})
						.then(() => {
							this.$baseMessage('删除成功', 'success', 'ut-hey-message-success')
							this.fetchData()
						})
				})
			},
			handleSave() {
				this.dialogFormVisible = false
				this.fetchData()
			},
			getExistingServiceTimes() {
				return this.dataList?.info?.map((item) => item.serviceTimes).sort((a, b) => a - b) || []
			},
			getNextAvailableServiceTimes(existingServiceTimes) {
				if (!this.dataList.info || this.dataList.info.length === 0) {
					return 1
				}
				for (let i = 1; i <= 99; i++) {
					if (!existingServiceTimes.includes(i)) {
						return i
					}
				}
				return Math.max(...existingServiceTimes) + 1
			},
			handleClose() {
				this.$emit('close')
			},
		},
	}
</script>

<style lang="scss" scoped>
	.tag-container {
		display: flex;
		flex-wrap: wrap;
		gap: 4px;

		:deep(.el-tag) {
			margin: 0 !important;
			padding: 0 4px !important;
		}
	}
</style>
