<template>
    <div v-loading="loading" class="ut-body">
         <div class="info-box">
             <el-row>
                 <el-col :span="8">
                     <span>姓名：</span>
                     <span>{{ form.name }}</span>
                 </el-col>
                 <el-col :span="8">
                     <span>身份证：</span>
                     <span>{{ form.idcard }}</span>
                 </el-col>
                 <el-col :span="8">
                     <span>电话：</span>
                     <span>{{ form.phone }}</span>
                 </el-col>
             </el-row>
             <el-row>
                 <el-col :span="8">
                     <span>性别：</span>
                     <span v-if="form.sex==0">不详</span>
                     <span v-if="form.sex==1">男</span>
                     <span v-if="form.sex==2">女</span>
                 </el-col>
                 <el-col :span="8">
                     <span>出生日期：</span>
                     <span>{{ form.birthday }}</span>
                 </el-col>
                 <el-col :span="8">
                     <span>民族：</span>
                     <span>{{ form.nation }}</span>
                 </el-col>
             </el-row>
         </div>
         <div style="position: relative;">
             <form-list
                 :loading="listLoading"
                 :columns="columns"
                 :height="400"
                 :op-width="130"
                 :op-fixed="true"
                 :data-list="dataList"
                 :select-window="false"
                 :select-single="false"
                 :show-list="false"
                 :show-corner="false"
                 :show-search="false"
                 table-name="long-customer-project"
                 @select="handleSelect"
                 @fetchData="fetchData"
                 @fullscreen="onFullscreen"
                 @selectRows="onSelectRows"
             >
                 <template slot="button">
                     <el-button icon="el-icon-plus" type="primary" @click="handleAdd">增加项目</el-button>

                     <el-button  icon="el-icon-edit" :disabled="!selectRows.length || selectRows.length<2" @click="handleBatchTime($event)">批量设置</el-button>
                     <el-button  icon="el-icon-delete" type="danger" :disabled="!selectRows.length" @click="handleDelete($event)">删除</el-button>
                 </template>
                 <template #op="{ row }">
                    <el-button type="text" @click="handleUpdateTime(row)">编辑时间</el-button>
                     <el-button type="text" @click="handleDelete(row)">删除</el-button>
                 </template>
                 <template #cell="{ row, item }">
                     <div v-if="item.prop === 'color'" class="color-box">
                         <span class="color-preview" :style="{ backgroundColor: row.color }"></span>
                         {{ row[item.prop] }}
                     </div>
                     <div v-else-if="item.prop === 'sex'">
                         <span v-if="row.sex==1">男</span>
                         <span v-if="row.sex==2">女</span>
                     </div>
                     <div v-else-if="item.prop === 'groupName'">
                         <span v-if="!row.groupName" style="color:gray;">【未分组】</span>
                         <span v-else>{{row.groupName}}</span>
                     </div>
                     <span v-else>{{ row[item.prop] }}</span>
                 </template>
             </form-list>
         </div>

         <!-- <el-dialog	v-if="projectFormVisible" v-dialog-drag  title="客户服务项目选择" :visible.sync="projectFormVisible" append-to-body width="800px" :destroy-on-close="true" :close-on-click-modal="false" :close-on-press-escape="false"	>
			<project-list :init-data="selectRow" module="long" :height="400" :select-window="true" :select-single="false" @select="handleSelect" @close="projectFormVisible=false" />
		</el-dialog> -->

        <ut-modal v-model="projectFormVisible" width="700px" title="客户服务项目选择" top="90px">
            <project-list :init-data="selectRow" module="long" :height="400" :select-window="true" :select-single="false" @select="handleSelect" @close="projectFormVisible=false" />
        </ut-modal>

        <ut-modal v-model="timeFormVisible" width="500px" title="编辑时间" top="90px">
			<edit-time :init-data="selectRow"  @fetchData="fetchData" @close="timeFormVisible=false" />
        </ut-modal>


        <ut-modal v-model="batchSetTimeFormVisible" width="500px" title="批量设置" top="90px">
            <edit-time-batch :init-data="selectRows"  @fetchData="fetchData" @close="batchSetTimeFormVisible=false" />
        </ut-modal>


    </div>
 </template>

 <script>
 import { mapGetters } from 'vuex'
 import { lang } from '@/common/utils/i18n'
 import FormList from '@/views/components/form-list'
 import ProjectList from '../../../basic/project'
 import EditTime from './edit-time.vue'
 import EditTimeBatch from './edit-time-batch.vue'
 export default {
     components: {
         FormList,
         ProjectList,
         EditTime,
         EditTimeBatch,
     },
     props: {
         parentData: {
             type: Object,
             default: () => ({}),
         },
         module:{
             type:String,
             default:'',
         },
     },
     data() {
         return {
             loading: false,
             listLoading:false,
             form: {
                 id: '',
                 name: '',
                 code: '',
                 color: '',
                 showIndex: '',
                 valid:true,
             },
             columns: [
                //  {
                //      label: '序',
                //      align: 'center',
                //      prop: 'index',
                //      width:'50',
                //      fixed:'left',
                //      show: true,
                //  },
                 {
                     label: '项目名称',
                     align: 'left',
                     prop: 'projectName',
                     minWidth:'120',
                     show: true,
                 },
                 {
                     label: '医保编号',
                     align: 'center',
                     prop: 'govCode',
                     width:'80',
                     show: true,
                 },
                 {
                     label: '有效时间起',
                     align: 'center',
                     prop: 'validBegin',
                     width:'100',
                     show: true,
                 },
                 {
                     label: '有效时间止',
                     align: 'center',
                     prop: 'validEnd',
                     width:'100',
                     show: true,
                 },
                 {
                     label: '月次数',
                     align: 'center',
                     prop: 'monthTimes',
                     width:'70',
                     show: true,
                 },
             ],
             dataList: {
                 info: [],
                 page: 0,
                 record: 0,
             },
             page: {
                 key: '',
                 pageindex: 1,
                 pagesize: 20,
                 order: '',
             },
             selectRow: {},
             selectRows: [],

             projectFormVisible:false,
             timeFormVisible:false,
             batchSetTimeFormVisible:false,
         }
     },
     computed: {
         ...mapGetters({
             comm: 'comm/comm',
         }),
     },
     watch: {
     },
     created() {
     },
     beforeMount() {
        this.getInfo()
        this.fetchData()
     },
     destroyed() {
     },
     methods: {
         lang,
         async getInfo() {
             if (!this.parentData.id) return
             this.loading = true
             const { data } = await this.$ut.api('homecarecustomer/info/info', {
                 communityId:this.comm.id,
                 module:this.module,
                 id: this.parentData.id,
             }).finally(()=>{this.loading=false})
             this.form = data
         },
         async fetchData(pageReq) {
             if (pageReq) this.page = pageReq
             this.listLoading = true
             const {data} = await this.$ut.api('homecarelong/customer/project/listpg', {
                 communityId:this.comm.id,
                 customerId:this.parentData.id,
                 ...this.page,
             }).finally(()=>{this.listLoading=false})
             this.dataList=data
             // this.getEmployeeInfo()
         },
         async handleSelect(rows) {
			this.projectFormVisible = false

            this.loading=true
            await this.$ut.api('homecarelong/customer/project/add', {
                communityId: this.comm.id,
                customerId:this.parentData.id,
                projectIds:rows.map((item) => item.id)
            }).finally(()=>{this.loading = false})
            this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
            this.fetchData()

         },
         onFullscreen(v) {
             this.fullscreen = v
         },
         onSelectRows(rows) {
             this.selectRows = rows
         },
         handleAdd(){
            this.projectFormVisible=true
         },
         handleUpdateTime(row){
            this.selectRow=row
            this.timeFormVisible=true
         },
         handleDelete(row) {
            let ids = []
            if (row.id) {
                ids = [row.id]
            } else {
                if (this.selectRows.length > 0) {
                    ids = this.selectRows.map((item) => item.id)
                } else {
                    this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
                    return
                }
            }
            this.$baseConfirm('您确定要删除吗？', null, async () => {
                this.$ut.api('homecarelong/customer/project/delete', {
                    communityId: this.comm.id,
                    customerId:this.parentData.id,
                    ids: ids,
                }).then(() => {
                    this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
                    this.fetchData()
                })
            })
         },
         handleBatchTime(row){
            this.selectRow=row
            this.batchSetTimeFormVisible=true
         }
     },
 }
 </script>
 <style lang='scss' scoped>
 .info-box{
     line-height: 30px;
     padding: 0 10px 0 10px;
 }

 </style>
