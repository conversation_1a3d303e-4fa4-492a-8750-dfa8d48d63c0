<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="130px" :model="form" :rules="rules" class="ut-form">
			<el-form-item label="有效时间（起）：" prop="validBegin">
				<el-date-picker	v-model="form.validBegin" format="yyyy年MM月dd日" value-format="yyyy-MM-dd" type="date" placeholder="请输入有效时间"/>
			</el-form-item>
			<el-form-item label="有效时间（止）：" prop="validEnd">
				<el-date-picker	v-model="form.validEnd" format="yyyy年MM月dd日" value-format="yyyy-MM-dd" type="date" placeholder="请输入有效时间"/>
			</el-form-item>
			<el-form-item label="月服务次数：" prop="monthTimes">
				<el-input v-model.trim="form.monthTimes" placeholder="" />
			</el-form-item>
		</el-form>
		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
export default {

	mixins:[],
	props: {
		initData: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			loading: false,
			form: {
				monthTimes:'',
				validBegin: '',
				validEnd: '',
			},
			rules: {
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

	},
	mounted() {
	},
	methods: {
		lang,
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true

				let ids=this.initData.map(u=>u.id)
				await this.$ut.api('homecarelong/customer/project/setMonthTimes', {
				  	communityId: this.comm.id,
                    ids:ids,
					...this.form,
				})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')
			})
		},
	},
}
</script>
<style lang="scss" scoped>
</style>
