<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="120"
			:op-fixed="true"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			table-name="long-customer-type"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button">
				<!-- <el-button icon="el-icon-plus" type="primary" :disabled="!treeNode.id" @click="handleAdd">增加客户</el-button> -->
				<!-- <el-button icon="el-icon-position" type="primary" :disabled="!selectRows.length" @click="handleMove($event)">移动到</el-button> -->
				<!-- <el-button v-if="treeNode.id!=-1 && treeNode.id" icon="el-icon-delete" type="danger" :disabled="!selectRows.length" @click="handleDelete($event)">从分组删除</el-button> -->
				
			</template>
			<template #op="{ row }">
				<el-button type="text" @click="handleProject(row)">指定服务项目</el-button>
			</template>
			<template #cell="{ row, item }">
				<div v-if="item.prop === 'color'" class="color-box">
					<span class="color-preview" :style="{ backgroundColor: row.color }"></span>
					{{ row[item.prop] }}
				</div>
				<div v-else-if="item.prop === 'sex'">
                    <span v-if="row.sex==1">男</span>
                    <span v-if="row.sex==2">女</span>
				</div>
                <div v-else-if="item.prop === 'groupName'">
                    <span v-if="!row.groupName" style="color:gray;">【未分组】</span>
                    <span v-else>{{row.groupName}}</span>
				</div>
				<span v-else>{{ row[item.prop] }}</span>
			</template>
		</form-list>

		<!-- 基本信息 -->
		<!-- <el-dialog	v-if="projectFormVisible" v-dialog-drag  title="客户服务项目指定"  :visible.sync="projectFormVisible" append-to-body width="800px" :destroy-on-close="true" :close-on-click-modal="false" :close-on-press-escape="false"	>
			<project-list :parent-data="selectRow" module="long"  @fetchData="fetchData" @close="projectFormVisible=false" />
		</el-dialog> -->

		<ut-modal v-model="projectFormVisible" width="800px" title="客户服务项目指定">
			<project-list :parent-data="selectRow" module="long"  @fetchData="fetchData" @close="projectFormVisible=false" />
		</ut-modal>

	</div>
</template>

<script>
import FormList from '@/views/components/form-list'
import { mapGetters } from 'vuex'
import Vue from 'vue'
import ProjectList from './list/index.vue'

export default {
	name: 'LongCustomerProject',
	components: {
		FormList,
		ProjectList,
	},
	props: {
		height: {
			type: Number,
			default: () => Vue.prototype.$baseTableHeight(1),
		},
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
        treeNode:{
            type:Object,
            default:()=>{},
        }
	},
	data() {
		return {
			listLoading: false,
			fullscreen: false,
			columns: [
				// {
				// 	label: '序',
				// 	align: 'center',
				// 	prop: 'index',
				// 	width:'50',
				// 	fixed:'left',
				// 	show: true,
				// },
				{
					label: '姓名',
					align: 'center',
					prop: 'name',
					width:'120',
					show: true,
				},
				{
					label: '性别',
					align: 'center',
					prop: 'sex',
					width:'60',
					show: true,
				},
				{
					label: '电话',
					align: 'center',
					prop: 'phone',
					width:'120',
					show: true,
				},
				{
					label: '证件号码',
					align: 'center',
					prop: 'idcard',
					minWidth:'220',
					show: true,
				},
				{
					label: '分组',
					align: 'center',
					prop: 'groupName',
					width:'120',
					show: true,
				},
				{
					label: '护理员',
					align: 'center',
					prop: 'attendantName',
					width:'100',
					show: true,
				},
				
				{
					label: '民族',
					align: 'center',
					prop: 'nation',
					width:'120',
					show: true,
				},
				{
					label: '出生日期',
					align: 'center',
					prop: 'birthday',
					width:'120',
					show: true,
				},
				{
					label: '居住状态',
					align: 'center',
					prop: 'liveState',
					width:'180',
					show: true,
				},
				{
					label: '失能等级',
					align: 'center',
					prop: 'levelName',
					width:'180',
					show: true,
				},				

			],
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 20,
				order: '',
			},
			selectRow: {},
			selectRows: [],
			projectFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
    watch:{
        treeNode:{
            deep:true,
            handler(){
                this.fetchData()
            }
        }
    },
	created() {
		// this.fetchData()
	},
	methods: {
		async fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true
			const {data} = await this.$ut.api('homecarecustomer/group/customer/listpg', {
				communityId:this.comm.id,
				module:'long',
                groupId:this.treeNode.id,
                ...this.page,
            }).finally(()=>{this.listLoading=false})
            this.dataList=data
			// this.getEmployeeInfo()
		},
		handleSelect(rows) {
			this.$emit('select', rows)
		},
		onFullscreen(v) {
			this.fullscreen = v
		},
		onSelectRows(rows) {
			this.selectRows = rows
		},
		handleProject(row) {
			this.selectRow = row
			this.projectFormVisible = true
		},	
		handleBatch(row){
			this.selectRow = row
		}	
		

	},
}
</script>

<style lang="scss" scoped>
.color-box {
	display: flex;
	align-items: center;
	justify-content: center;

	.color-preview {
		width: 20px;
		height: 20px;
		margin-right: 10px;
        border-radius: 2px;
	}
}

</style>
