<template>
	<div>
        <el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-form-item label="护理人员：" prop="attendantId">
                <el-input v-model.trim="form.attendantName" placeholder="请选择护理人员" @focus="attendantFormVisible=true">
                    <template slot="append">
                        <i class="el-icon-search" @click="attendantFormVisible=true"></i>
                    </template>
                </el-input>
			</el-form-item>
        </el-form>

		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>

        <el-dialog v-if="attendantFormVisible" v-dialog-drag class="select-window" title="选择护理人员" :visible.sync="attendantFormVisible" append-to-body width="800px" :destroy-on-close="true" :close-on-click-modal="false" :close-on-press-escape="false">
            <attendant-list :select-window="true" :select-single="true" :height="500" @select="handleSelectAttendant" @close="attendantFormVisible=false"/>
        </el-dialog>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
import AttendantList from '../../care/attendant'

export default {
	components:{
        AttendantList,
	},
	props: {
		curNode: {
			type: Object,
			default: () => ({}),
		},
		initData: {
			type: Object,
			default: () => ({}),
		},
	},
	
	data() {
		return {
            selNode:{},
            form:{
                attendantId:'',
            },
            rules: {
				attendantName: [{ required: true, trigger: 'change', message: '请选择护理员' }],
			},
            attendantFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

	},
	mounted() {
	},
	methods: {
		lang,
        nodeClick(node){
            this.selNode=node
        },
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
		
				await this.$ut.api('homecarecustomer/group/customer/setAttendant', {
					communityId:this.comm.id,
                    module:'long',
                    customerIds:[this.initData.id],
					attendantId:this.form.attendantId,
				}).finally(()=>{this.loading = false})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
        handleSelectAttendant(rows){
			this.attendantFormVisible = false
			if (rows && rows[0]) {
				this.form.attendantId = rows[0].attendantId
				this.form.attendantName = rows[0].name
			}
		},
	},
}
</script>
<style lang="scss" scoped>
.select-window{

	:deep(.ut-list){
		.el-card__body{
			height: auto !important;
		}
	}

	:deep(.ut-tree){
		.el-card{
			height: 100%;
		}
		.el-card__body{
			height: auto !important;
		}
	}

}
</style>
