<template>
	<form-tree-list :tree-width="220" :class="{ 'ut-fullscreen': fullscreen }">
		<template #tree>
			<tree @node-click="nodeClick"/>
		</template>
		<template #list>
			<list :tree-node="treeNode" :select-single="selectSingle" :select-window="selectWindow" @select="handleSelect"/>
		</template>
	</form-tree-list>
</template>

<script>
import FormTreeList from '@/views/components/form-tree-list'
import List from './list.vue'
import Tree from './tree.vue'
export default {
	name:'CustomerGroup',
	components: {
		FormTreeList,
		List,
		Tree,
	},
	props: {
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			fullscreen: false,
			treeNode:{},
		}
	},
	computed: {},
	created() {},
	methods: {
		nodeClick(node) {
			this.treeNode=node
		},

		handleSelect(rows){
			this.$emit('select',rows)
		}

	},
}
</script>