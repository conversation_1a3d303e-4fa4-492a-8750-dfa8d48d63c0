<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="220"
			:op-fixed="true"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			table-name="long-customer-type"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button">
				<!-- <el-button icon="el-icon-plus" type="primary" :disabled="!treeNode.id" @click="handleAdd">增加客户</el-button> -->
				<el-button icon="el-icon-position" type="primary" :disabled="!selectRows.length" @click="handleMove($event)">移动到</el-button>
				<el-button v-if="treeNode.id!=-1 && treeNode.id" icon="el-icon-delete" type="danger" :disabled="!selectRows.length" @click="handleDelete($event)">从分组删除</el-button>
				
			</template>
			<template #op="{ row }">
				<el-button v-if="!row.attendantId" type="text" @click="handleAttendant(row)">指定护理员</el-button>
				<el-button v-else type="text" @click="handleAttendant(row)">变更护理员</el-button>
				<el-button v-if="!row.isSecond && row.groupId" type="text" @click="handleSecond(row)">创建复评</el-button>
				<el-button type="text" @click="handleDetail(row)">详细</el-button>
			</template>
			<template #cell="{ row, item }">
				<div v-if="item.prop === 'color'" class="color-box">
					<span class="color-preview" :style="{ backgroundColor: row.color }"></span>
					{{ row[item.prop] }}
				</div>
				<div v-else-if="item.prop === 'sex'">
                    <span v-if="row.sex==1">男</span>
                    <span v-if="row.sex==2">女</span>
				</div>
				<div v-else-if="item.prop === 'isSecond'">
                    <span v-if="row.isSecond">是</span>
				</div>
                <div v-else-if="item.prop === 'groupName'">
                    <span v-if="!row.groupName" style="color:gray;">【未分组】</span>
                    <span v-else>{{row.groupName}}</span>
				</div>
				<span v-else>{{ row[item.prop] }}</span>
			</template>
		</form-list>

		<!-- 基本信息 -->
		<ut-modal v-model="attendantFormVisible" title="指定护理员" width="400px">
			<set-attendant :init-data="selectRow" @fetchData="fetchData" @close="attendantFormVisible=false" />
		</ut-modal>

		<ut-modal v-model="moveFormVisible" title="选择分组" width="400px">
			<move :height="400" :cur-node="treeNode" @select="handleMoveSuccess" @close="moveFormVisible=false"/>
		</ut-modal>

	</div>
</template>

<script>
// import Edit from './edit'
// import EmployeeList from '../../../personnel/employee'
import FormList from '@/views/components/form-list'
import { mapGetters } from 'vuex'
import Vue from 'vue'
import Move from './move'
import SetAttendant from './set-attendant.vue'

export default {
	name: 'LongCustomerSource',
	components: {
		Move,
		FormList,
		SetAttendant,
	},
	props: {
		height: {
			type: Number,
			default: () => Vue.prototype.$baseTableHeight(1),
		},
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
        treeNode:{
            type:Object,
            default:()=>{},
        }
	},
	data() {
		return {
			listLoading: false,
			fullscreen: false,
			columns: [
				// {
				// 	label: '序',
				// 	align: 'center',
				// 	prop: 'index',
				// 	width:'50',
				// 	fixed:'left',
				// 	show: true,
				// },
				{
					label: '客户姓名',
					align: 'center',
					prop: 'name',
					width:'120',
					show: true,
				},
				{
					label: '性别',
					align: 'center',
					prop: 'sex',
					width:'60',
					show: true,
				},
				{
					label: '电话',
					align: 'center',
					prop: 'phone',
					width:'120',
					show: true,
				},
				{
					label: '证件号码',
					align: 'center',
					prop: 'idcard',
					minWidth:'220',
					show: true,
				},
				{
					label: '分组',
					align: 'center',
					prop: 'groupName',
					width:'120',
					show: true,
				},
				{
					label: '护理员',
					align: 'center',
					prop: 'attendantName',
					width:'100',
					show: true,
				},
				
				{
					label: '民族',
					align: 'center',
					prop: 'nation',
					width:'120',
					show: true,
				},
				{
					label: '出生日期',
					align: 'center',
					prop: 'birthday',
					width:'120',
					show: true,
				},
				{
					label: '居住状态',
					align: 'center',
					prop: 'liveState',
					width:'180',
					show: true,
				},
				{
					label: '失能等级',
					align: 'center',
					prop: 'levelName',
					width:'180',
					show: true,
				},	
				{
					label: '存在复评',
					align: 'center',
					prop: 'isSecond',
					width:'80',
					show: true,
					
				}			

			],
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 20,
				order: '',
			},
			selectRow: {},
			selectRows: [],
			moveFormVisible:false,
			detailFormVisible:false,
			attendantFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
    watch:{
        treeNode:{
            deep:true,
            handler(){
                this.fetchData()
            }
        }
    },
	created() {
		// this.fetchData()
	},
	methods: {
		async fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true
			const {data} = await this.$ut.api('homecarecustomer/group/customer/listpg', {
				communityId:this.comm.id,
				module:'long',
                groupId:this.treeNode.id,
                ...this.page,
            }).finally(()=>{this.listLoading=false})
            this.dataList=data
			this.getSecondState()
		},
		async getSecondState() {
			const ids=this.dataList.info.map(o=>o.id)
			if(!ids.length) return
			
			this.listLoading = true
			const {data} = await this.$ut.api('homecarelong/customer/second/state', {
				communityId:this.comm.id,
				customerIds:ids,
            }).finally(()=>{this.listLoading=false})
			.finally(()=>{this.listLoading=false})
			if(data){
				this.dataList.info.forEach(item=>{
					let obj=data.find(u=>u.customerId==item.id)
					if(obj){
						this.$set(item,'isSecond',obj.isSecond)
					}
				})
			}
		},

		handleSelect(rows) {
			this.$emit('select', rows)
		},
		onFullscreen(v) {
			this.fullscreen = v
		},
		onSelectRows(rows) {
			this.selectRows = rows
		},
		// handleAdd() {
		// 	this.selectRow = {}
		// 	this.employeeFormVisible=true
		// },
		handleAttendant(row) {
			this.selectRow = row
			this.attendantFormVisible = true
		},
		handleDetail(row){
			this.selectRow=row
			this.detailFormVisible=true
		},
		handleDelete(row) {
			let ids = []
			if (row.id) {
				ids = [row.id]
			} else {
				if (this.selectRows.length > 0) {
					ids = this.selectRows.map((item) => item.id)
				} else {
					this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
					return
				}
			}
			this.$baseConfirm('你确定要删除吗', null, async () => {
				this.$ut.api('homecarecustomer/group/customer/delete', {
                    communityId: this.comm.id,
					module:'long',
					customerIds: ids,
                }).then(() => {
                    this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
                    this.fetchData()
                })
			})
		},
		handleMove(row) {
			this.selectRow = row
			this.moveFormVisible=true
		},
		handleMoveSuccess(node){
			let ids = []
			if (this.selectRows.length > 0) {
				ids = this.selectRows.map((item) => item.id)
			} else {
				this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
				return
			}
			this.$ut.api('homecarecustomer/group/customer/move', {
				communityId: this.comm.id,
				module:'long',
				customerIds: ids,
				groupId:node.id,
			}).then(() => {
				this.moveFormVisible=false
				this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
				this.fetchData()
			})
			// })
		},
		handleSecond(row){
			this.$baseConfirm('你确定要创建复评记录吗', null, async () => {
				this.$ut.api('homecarelong/customer/second/create', {
                    communityId: this.comm.id,
					customerId: row.id,
                }).then(() => {
                    this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
                    this.fetchData()
                })
			})
		}

	},
}
</script>

<style lang="scss" scoped>
.color-box {
	display: flex;
	align-items: center;
	justify-content: center;

	.color-preview {
		width: 20px;
		height: 20px;
		margin-right: 10px;
        border-radius: 2px;
	}
}

</style>
