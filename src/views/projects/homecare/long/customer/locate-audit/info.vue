<template>
    <div v-loading="loading" class="ut-body">

        <div class="customer-info">
             <el-descriptions :column="4" border>
                 <el-descriptions-item label="客户姓名">{{customer.name}}</el-descriptions-item>
                 <el-descriptions-item label="性别">
                     <span v-if="customer.sex==1">男</span>
                     <span v-else-if="customer.sex==2">女</span>
                 </el-descriptions-item>
                 <el-descriptions-item span="2" label="手机号">{{customer.phone}}</el-descriptions-item>
                 <el-descriptions-item span="2" label="身份证号">{{customer.idcard}}</el-descriptions-item>
                 <el-descriptions-item span="2" label="地址">{{customer.address}}</el-descriptions-item>
                 <el-descriptions-item span="2" label="签到时间">{{ form.checkInTime }}</el-descriptions-item>
                 <el-descriptions-item span="2" label="签退时间">{{ form.checkOutTime }}</el-descriptions-item>
                 <el-descriptions-item span="2" label="项目数">{{form.projectCount}}</el-descriptions-item>
                 <el-descriptions-item span="2" label="服务时长">{{form.totalDuration}}</el-descriptions-item>
             </el-descriptions>
         </div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'

export default {
 components: {
 },
 props: {
     parentData:{
         type:Object,
         default:()=>{},
     }
 },
 data() {
     return {
         loading:false,
         form:{},
         tabName:'main',
         customer:{},
         firstNurseFormVisible:false,
     }
 },
 computed: {
     ...mapGetters({
         comm: 'comm/comm',
     }),
 },
 watch: {
 },
 created() {
 },
 async beforeMount() {
     this.getInfo()
 },
 destroyed() {
 },
 methods: {        
     lang,
     async getInfo() {
         if (!this.parentData.id) return
         this.loading = true
         const { data } = await this.$ut.api('homecarelong/care/work/tjInfo', {
             communityId: this.comm.id,
             id: this.parentData.id,
         }).finally(()=>{this.loading=false})
         this.form = data
         this.getCustomerInfo(data)
     },
     async getCustomerInfo(workInfo) {
        if(!workInfo) return
         if (!workInfo.customerId) return
         this.loading = true
         const { data } = await this.$ut.api('homecarecustomer/info/info', {
             communityId: this.comm.id,
             module:'long',
             id: workInfo.customerId,
         }).finally(()=>{this.loading=false})
         this.customer = data
     },
},
}
</script>
<style lang='scss' scoped>
.customer-info{
 padding-bottom: 8px;
 line-height: 1.5;
}
.project-wrapper{
 display: flex;
 flex-direction: row;
 padding-bottom: 12px;
 .title{
     width:130px;
     text-align: right;
     padding-right: 4px;
 }

}
.project-box{
 display: flex;
 flex-direction: column;
 flex-wrap: wrap;
 max-height: 330px;
 min-height: 200px;
 overflow: auto;
 flex: 1;
 border: 1px solid #f2f2f2;
 padding: 8px;
 font-size: 16px;
}
.project-item{

    min-width: 200px;
    padding: 0 15px;
    line-height: 2;
}

.select-window{

 :deep(.ut-list){
     .el-card__body{
         height: auto !important;
     }
 }

 :deep(.ut-tree){
     .el-card{
         height: 100%;
     }
     .el-card__body{
         height: auto !important;
     }
 }

 :deep(.ut-tree-list){
     padding: 0;
 }

}

.monthTimes{
    color: $base-color-blue;
}
</style>