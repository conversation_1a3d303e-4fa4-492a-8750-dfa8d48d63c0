<template>
	<div v-loading="loading">
		<div class="audit-map-wrapper ut-form">
			<div class="map-container">
				<div id="auditMapContainer" class="map-box" style="height: 450px"></div>
			</div>
			<div class="audit-panel">
				<div class="audit-form">
					<el-form ref="auditForm" :model="auditForm" label-width="100px">
						<el-form-item label="变更原因：">
							<el-input v-model="auditForm.auditRemark" type="textarea" :rows="3" disabled />
						</el-form-item>
					</el-form>
				</div>
			</div>
		</div>
		<div class="ut-edit-footer">
			<el-button type="primary" @click="confirmAudit">确认审核</el-button>
			<el-button @click="$emit('close')">取消</el-button>
		</div>
	</div>
</template>

<script>
	import { MapMixins } from '@/views/projects/homecare/components/map'

	export default {
		name: 'LocationAuditMap',
		mixins: [MapMixins],
		props: {
			auditData: {
				type: Object,
				default: () => ({}),
			},
		},
		data() {
			return {
				loading: false,
				auditForm: {
					auditState: 1,
					auditRemark: '',
				},
				infoWindow: null,
			}
		},
		created() {
			this.prepareMapData()
		},
		methods: {
			prepareMapData() {
				this.mapMarkers = []

				const oldLat = this.auditData.oldLatitude
				const oldLng = this.auditData.oldLongitude
				if (oldLng && oldLat) {
					const name = this.auditData.oldLocationAddress ? `原位置：${this.auditData.oldLocationAddress}` : '原位置'
					this.mapMarkers.push({
						type: 'old',
						name,
						lat: parseFloat(oldLat),
						lng: parseFloat(oldLng),
						address: this.auditData.oldLocationAddress,
					})
				}

				const newLat = this.auditData.latitude
				const newLng = this.auditData.longitude

				if (newLng && newLat) {
					const name = this.auditData.locationAddress ? `新位置：${this.auditData.locationAddress}` : '新位置'
					this.mapMarkers.push({
						type: 'new',
						name,
						lat: parseFloat(newLat),
						lng: parseFloat(newLng),
						address: this.auditData.locationAddress,
					})
				}

				// 如果没有经纬度数据，显示提示信息
				if (this.mapMarkers.length === 0) {
					this.$message.warning('暂无位置坐标信息，无法显示地图')
					return
				}

				this.initAuditMap()
			},
			async initAuditMap() {
				try {
					this.loading = true

					// 设置地图中心
					let centerLatLng = null
					if (this.mapMarkers.length > 0) {
						centerLatLng = { lat: this.mapMarkers[0].lat, lng: this.mapMarkers[0].lng }
					}

					// 初始化地图选项
					const mapOptions = {}
					if (centerLatLng) {
						mapOptions.center = centerLatLng
					}

					// 初始化地图
					await this.initMap('auditMapContainer', mapOptions, 'auditMap')

					// 添加标记
					this.addAuditMarkers()

					// 创建信息窗
					this.setupInfoWindow()
				} catch (error) {
					console.error('地图初始化失败:', error)
				} finally {
					this.loading = false
				}
			},

			addAuditMarkers() {
				if (!this.mapMarkers.length) return

				// 定义标记样式
				const markerStyles = {
					old: {
						color: '#000',
						src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/marker_blue.png',
						width: 32,
						height: 40,
						anchor: { x: 16, y: 48 },
						// 文字标签配置
						offset: { x: 0, y: 0 },
						size: 12,
						direction: 'bottom',
						strokeColor: '#fff',
						strokeWidth: 2,
					},
					new: {
						color: '#000',
						src: 'https://mapapi.qq.com/web/lbs/visualizationApi/demo/img/big.png',
						width: 32,
						height: 42,
						anchor: { x: 16, y: 48 },
						// 文字标签配置
						offset: { x: 0, y: 0 },
						size: 12,
						direction: 'bottom',
						strokeColor: '#fff',
						strokeWidth: 2,
					},
				}

				// 添加标记
				const multiMarker = this.addMarkers(this.mapMarkers, markerStyles)

				// 设置点击事件
				if (multiMarker) {
					multiMarker.on('click', (evt) => {
						this.handleMarkerClick(evt)
					})
				}
			},

			setupInfoWindow() {
				// 创建信息窗
				this.infoWindow = this.createInfoWindow()
				if (this.infoWindow) {
					this.infoWindow.close()
				}
			},

			handleMarkerClick(evt) {
				const props = evt.geometry.properties
				if (!props || !props.name || !this.infoWindow) return

				this.infoWindow.open()
				this.infoWindow.setPosition(evt.geometry.position)

				const content = `
					<div style="padding: 10px;">
						<div style="font-weight: bold; margin-bottom: 5px;">${props.name}</div>
						<div style="color: #666;">${props.address || ''}</div>
					</div>
				`
				this.infoWindow.setContent(content)
			},
			confirmAudit() {
				this.$emit('audit-confirm', {
					auditState: 1,
					auditRemark: this.auditData.remark,
				})
			},
		},
	}
</script>

<style lang="scss" scoped>
	.audit-map-wrapper {
		display: flex;
		flex-direction: column;

		.map-container {
			flex: 1;
			margin-bottom: 20px;

			.map-box {
				width: 100%;
				border: 1px solid #ddd;
				border-radius: 4px;
			}
		}

		.audit-panel {
			.audit-form {
				margin-bottom: 20px;
			}
		}
	}
</style>
