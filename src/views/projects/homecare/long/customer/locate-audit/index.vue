<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="80"
			:op-fixed="true"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			table-name="long-customer-type"
			:show-checkbox="false"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button"></template>
			<template #op="{ row }">
				<!-- <el-button type="text" @click="handleInfo(row)">详细</el-button> -->
				<el-button v-if="row.auditState == 0" type="text" @click="handleAuditWithMap(row)">审核</el-button>
				<!-- <el-button v-else type="text" @click="handleAuditCancel(row)">撤销审核</el-button> -->
			</template>
			<template #cell="{ row, item }">
				<div v-if="item.prop === 'color'" class="color-box">
					<span class="color-preview" :style="{ backgroundColor: row.color }"></span>
					{{ row[item.prop] }}
				</div>
				<div v-else-if="item.prop === 'sex'">
					<span v-if="row.sex == 1">男</span>
					<span v-if="row.sex == 2">女</span>
				</div>
				<div v-else-if="item.prop === 'auditState'">
					<span v-if="row.auditState">已审核</span>
					<span v-else>未审核</span>
				</div>
				<span v-else>{{ row[item.prop] }}</span>
			</template>
		</form-list>

		<!-- 详细 -->
		<ut-modal v-model="infoFormVisible" title="详细" width="800px">
			<info :parent-data="selectRow" @close="infoFormVisible = false" />
		</ut-modal>

		<!-- 位置审核地图 -->
		<ut-modal v-model="auditMapVisible" title="位置变更审核" width="800px">
			<location-audit-map :audit-data="selectRow" @audit-confirm="handleAuditConfirm" @close="auditMapVisible = false" />
		</ut-modal>
	</div>
</template>

<script>
	import { mapGetters } from 'vuex'
	import Vue from 'vue'
	import FormList from '@/views/components/form-list'
	import Info from './info.vue'
	import LocationAuditMap from './location-audit-map.vue'
	export default {
		name: 'Works',
		components: {
			FormList,
			Info,
			LocationAuditMap,
		},
		props: {
			height: {
				type: Number,
				default: () => Vue.prototype.$baseTableHeight(1),
			},
			selectWindow: {
				type: Boolean,
				default: false,
			},
			selectSingle: {
				type: Boolean,
				default: false,
			},
			showList: {
				type: Boolean,
				default: false,
			},
		},
		data() {
			return {
				listLoading: false,
				fullscreen: false,
				columns: [
					{
						label: '序',
						align: 'center',
						prop: 'index',
						width: '50',
						fixed: 'left',
						show: true,
					},
					// {
					// 	label: '分组名称',
					// 	align: 'left',
					// 	prop: 'groupName',
					// 	width:'140',
					// 	fixed:'left',
					// 	show: true,
					// },
					// {
					// 	label: '护理员',
					// 	align: 'left',
					// 	prop: 'attendantName',
					// 	width:'120',
					// 	fixed:'left',
					// 	show: true,
					// },
					{
						label: '客户姓名',
						align: 'left',
						prop: 'name',
						width: '120',
						fixed: 'left',
						show: true,
					},
					{
						label: '性别',
						align: 'center',
						prop: 'sex',
						width: '60',
						show: true,
					},
					{
						label: '电话',
						align: 'center',
						prop: 'phone',
						width: '120',
						show: true,
					},
					// {
					// 	label: '证件号码',
					// 	align: 'center',
					// 	prop: 'idcard',
					// 	minWidth:'180',
					// 	show: true,
					// },
					{
						label: '地址',
						align: 'left',
						prop: 'address',
						width: '220',
						show: true,
					},
					{
						label: '变更时间',
						align: 'center',
						prop: 'createTime',
						width: '160',
						show: true,
					},
					{
						label: '变更人',
						align: 'left',
						prop: 'realName',
						width: '120',
						show: true,
					},
					{
						label: '原位置',
						align: 'left',
						prop: 'oldLocationAddress',
						width: '160',
						show: true,
					},
					{
						label: '新位置',
						align: 'left',
						prop: 'locationAddress',
						width: '160',
						show: true,
					},
					{
						label: '审核状态',
						align: 'center',
						prop: 'auditState',
						width: '90',
						show: true,
					},
					{
						label: '审核时间',
						align: 'center',
						prop: 'auditTime',
						width: '160',
						show: true,
					},
					{
						label: '审核人',
						align: 'left',
						prop: 'string',
						width: '100',
						show: true,
					},
					{
						label: '审核说明',
						align: 'left',
						prop: 'auditRemark',
						minWidth: '120',
						show: true,
					},
				],
				dataList: {
					info: [],
					page: 0,
					record: 0,
				},
				page: {
					key: '',
					pageindex: 1,
					pagesize: 20,
				},
				search: {},
				selectRow: {},
				selectRows: [],
				infoFormVisible: false,
				auditMapVisible: false,
			}
		},
		computed: {
			...mapGetters({
				comm: 'comm/comm',
			}),
		},
		created() {
			this.fetchData()
		},
		methods: {
			async fetchData(pageReq) {
				if (pageReq) this.page = pageReq
				this.listLoading = true
				const { data } = await this.$ut
					.api('homecarelong/customer/location/listpg', {
						communityId: this.comm.id,
						module: 'long',
						...this.page,
					})
					.finally(() => {
						this.listLoading = false
					})
				this.dataList = data
				//this.getCustomerInfo(data)
			},
			async getCustomerInfo({ info }) {
				let ids = info.filter((u) => u.customerId).map((u) => u.customerId)
				if (!ids || !ids.length) return
				let params = {
					ids: ids,
					communityId: this.comm.id,
					module: 'long',
				}
				try {
					const { data } = await this.$ut.api('homecarecustomer/info/listByIds', params)
					this.dataList.info.forEach((item) => {
						let obj = data.find((u) => u.id == item.customerId)
						if (obj) this.$set(item, '_customer', obj)
					})
				} finally {
					this.listLoading = false
				}
			},
			handleSelect(rows) {
				this.$emit('select', rows)
			},
			onFullscreen(v) {
				this.fullscreen = v
			},
			onSelectRows(rows) {
				this.selectRows = rows
			},
			handleInfo(row) {
				this.selectRow = row
				this.infoFormVisible = true
			},
			handleAuditWithMap(row) {
				this.selectRow = row
				this.auditMapVisible = true
			},
			handleAuditConfirm(auditData) {
				this.$ut
					.api('homecarelong/customer/location/audit', {
						communityId: this.comm.id,
						module: 'long',
						auditState: auditData.auditState,
						auditRemark: auditData.auditRemark,
						ids: [this.selectRow.id],
					})
					.then(() => {
						this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
						this.auditMapVisible = false
						this.fetchData()
					})
			},
			handleAuditCancel(row) {
				this.$baseConfirm('确认撤销审核吗？', '确认操作', async () => {
					this.$ut
						.api('homecarelong/customer/location/auditCancel', {
							communityId: this.comm.id,
							ids: [row.id],
						})
						.then(() => {
							this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
							this.fetchData()
						})
				})
			},
		},
	}
</script>

<style lang="scss" scoped>
	.color-box {
		display: flex;
		align-items: center;
		justify-content: center;

		.color-preview {
			width: 20px;
			height: 20px;
			margin-right: 10px;
			border-radius: 2px;
		}
	}
</style>
