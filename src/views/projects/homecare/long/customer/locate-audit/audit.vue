<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="130px" :model="form" :rules="rules" class="ut-form">
			<el-form-item label="客户：" prop="customerName">
				<el-input v-model.trim="form.customerName"  placeholder="请选择客户" @focus="customerVisible = true" >
					<template slot="append">
						<i class="el-icon-search" @click="customerVisible = true"></i>
					</template>
				</el-input>
			</el-form-item>
			<el-form-item label="日期：" prop="workDate">
				<el-date-picker	v-model="form.workDate" format="yyyy年MM月dd日" value-format="yyyy-MM-dd" type="date" placeholder="选择排班日期"/>
			</el-form-item>
		</el-form>
		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
export default {
	name:'CustomerSchedulingEdit',
	components:{
			
	},
	props: {
		attendantId:{
			type:String,
			default:'',
		},
		initData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			loading: false,
			form: {
				id: '',
				customerId: '',
				workDate: '',
				customerName:'',
			},
			rules: {
				customerName: [{ required: true, trigger: 'blur', message: '请选择客户' }],
				workDate: [{ required: true, trigger: 'blur', message: '请指定排班日期' }],
			},
			customerVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarelong/care/head/scheduling/customer/dateInfo', {
				communityId: this.comm.id,
				module:'long',
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
			this.getCustomerInfo(data)
		},
		async getCustomerInfo(info) {
			if(!info) return
			let params = {
				id: info.customerId,
				communityId: this.comm.id,
				module:'long'
			}
			try{
				const {data} = await this.$ut.api('homecarecustomer/info/info', params)
				this.form.customerName = data.name
			}
			finally{
				this.listLoading = false
			}
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
				await this.$ut.api('homecarelong/care/head/scheduling/customer/dateSave', {
				  	communityId: this.comm.id,
					module:'long',
					...this.form,
				}).finally(()=>{this.loading=false})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
		handleSelectCustomer(rows) {
			this.customerVisible = false
			if (rows && rows[0]) {
				this.form.customerId = rows[0].id
				this.form.customerName = rows[0].name
			}
		},
	},
}
</script>
<style lang="scss" scoped>
.color-box {
	display: inline-block;
	height: 32px;
	width: 150px;
	border: 1px solid #dcdfe6;
	padding-left: 20px;

	.color-box-text {
		color:#fff;
		mix-blend-mode: difference;
	}
}
.placeholder {
	height: 70px;
}
</style>
