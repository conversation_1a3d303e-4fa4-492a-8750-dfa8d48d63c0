<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="100"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			:page-size="page.pagesize"
			table-name="long-basic-project"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button">
				<!-- <el-button icon="el-icon-plus" type="primary" @click="handleAdd">添加</el-button>
				<el-button icon="el-icon-delete" type="danger" :disabled="!selectRows.length" @click="handleDelete($event)">删除</el-button> -->
			</template>
			<!--<template #op="{ row }">
				 <el-button type="text" @click="handleEdit(row)">编辑</el-button>
				<el-button type="text" @click="handleDelete(row)">删除</el-button> 
			</template>-->
			<template #cell="{ row, item }">
				<div v-if="item.prop === 'color'" class="color-box">
					<span class="color-preview" :style="{ backgroundColor: row.color }"></span>
					{{ row[item.prop] }}
				</div>
                <div v-else-if="item.prop === 'valid'">
                    <span v-if="row.valid"><i class="el-icon-check" /></span>
                    <span v-else>未启用</span>
				</div>
				<span v-else>{{ row[item.prop] }}</span>
			</template>
		</form-list>
		
	</div>
</template>

<script>
//import Edit from './edit'
import FormList from '@/views/components/form-list'
import { mapGetters } from 'vuex'
import Vue from 'vue'
export default {
	name: 'AttendantCustomerList',
	components: {
		//Edit,
		FormList,
	},
	props: {
		height: {
			type: [Number, String],
			default: () => Vue.prototype.$baseTableHeight(1),
		},
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
		attendantId:{
			type:String,
			default:'',
		}
	},
	data() {
		return {
			listLoading: false,
			fullscreen: false,
			columns: [
				{
					label: '分组',
					align: 'center',
					prop: 'groupName',
					width:'120',
					show: true,
				},
				// {
				// 	label: '护理员',
				// 	align: 'center',
				// 	prop: 'attendantName',
				// 	width:'100',
				// 	show: true,
				// },
				{
					label: '客户姓名',
					align: 'center',
					prop: 'name',
					width:'120',
					show: true,
				},
				{
					label: '性别',
					align: 'center',
					prop: 'sex',
					width:'60',
					show: true,
				},
				{
					label: '电话',
					align: 'center',
					prop: 'phone',
					width:'120',
					show: true,
				},
				{
					label: '证件号码',
					align: 'center',
					prop: 'idcard',
					minWidth:'220',
					show: true,
				},	
				{
					label: '地址',
					align: 'left',
					prop: 'location',
					minWidth:'180',
					width:'auto',
					show: true,
				},
				{
					label: '失能等级',
					align: 'center',
					prop: 'levelName',
					width:'100',
					show: true,
				},	
				// {
				// 	label: '民族',
				// 	align: 'center',
				// 	prop: 'nation',
				// 	width:'120',
				// 	show: true,
				// },
				// {
				// 	label: '出生日期',
				// 	align: 'center',
				// 	prop: 'birthday',
				// 	width:'120',
				// 	show: true,
				// },
				// {
				// 	label: '居住状态',
				// 	align: 'center',
				// 	prop: 'liveState',
				// 	width:'180',
				// 	show: true,
				// },
				// {
				// 	label: '存在复评',
				// 	align: 'center',
				// 	prop: 'isSecond',
				// 	width:'80',
				// 	show: true,
					
				// }		
			],
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 100,
			},
			selectRow: {},
			selectRows: [],
			dialogFormVisible: false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	created() {
		this.fetchData()
	},
	methods: {
		async fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true
			const {data} = await this.$ut.api('homecarelong/transaction/attendant/customer/listpg', {
				communityId: this.comm.id,
				module:'long',
                attendantId:this.attendantId,
                ...this.page,
            }).finally(()=>{this.listLoading=false})
            this.dataList=data
		},
		handleSelect(rows) {
			this.$emit('select', rows)
		},
		onFullscreen(v) {
			this.fullscreen = v
		},
		onSelectRows(rows) {
			this.selectRows = rows
		},
		// handleAdd() {
		// 	this.selectRow = {}
		// 	this.dialogFormVisible = true
		// },
		// handleEdit(row) {
		// 	this.selectRow = row
		// 	this.dialogFormVisible = true
		// },
	},
}
</script>

<style lang="scss" scoped>
.color-box {
	display: flex;
	align-items: center;
	justify-content: center;

	.color-preview {
		width: 20px;
		height: 20px;
		margin-right: 10px;
        border-radius: 2px;
	}
}

</style>
