<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="120px" :model="form" :rules="{...rules,...rules_no,...rules_yes}" class="ut-form">
			<el-row>
                
                <el-col>	
                    <el-form-item label="医保审核：" prop="isPass">
                        <el-radio-group v-model="form.isPass">
                            <el-radio-button :label="true">通过</el-radio-button>
                            <el-radio-button :label="false">不通过</el-radio-button>
                        </el-radio-group>
					</el-form-item>	
                </el-col>
				<el-col v-show="form.isPass==false" :span="24">
					<el-form-item label="不通过原因：" prop="noPassReason" >
						<el-input v-model.trim="form.noPassReason" type="textarea" :rows="2"/>
					</el-form-item>
				</el-col>
				<el-col v-show="form.isPass==false" :span="24">
					<el-form-item label="下次提醒时间：" prop="nextGovAuditTime" >
						<el-date-picker	v-model="form.nextGovAuditTime"	format="yyyy年MM月dd日"	value-format="yyyy-MM-dd"  type="date" placeholder="止日期"/>
					</el-form-item>
				</el-col>
                
				<el-col  v-show="form.isPass" :span="24">							
                    <el-form-item label="社保通过时间：" prop="govAuditTime">
                        <el-date-picker v-model="form.govAuditTime" type="date" value-format="yyyy-MM-dd" placeholder="请输入时间" />
                    </el-form-item>
                </el-col>
				<el-col v-show="form.isPass" :span="24">
					<el-form-item label="备注：" prop="govAuditRemark">
						<el-input v-model.trim="form.govAuditRemark" type="textarea" :rows="2"/>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>


		<div class="ut-edit-footer">
			<el-button type="primary" :disabled="form.isPass!=true && form.isPass!=false" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>

	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
export default {
	components:{
	},
	mixins: [],
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
	},
	
	data() {
		return {
			loading: false,
			form: {
				isPass:null,
                noPassReason:'',
				govAuditTime: '',
                govAuditRemark:'',
				nextGovAuditTime:'',
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
		rules(){
            return {isPass: [{ required: true, trigger: ['blur','change'], message: '请选择审核状态' }]}
        },
        rules_no(){
            if(this.form.isPass) return {}
            return {
                noPassReason: [{ required: true, trigger: ['blur'], message: '请输入原因' }],
                nextGovAuditTime:[{ required: true, trigger: ['blur'], message: '请输入下次提醒时间' }]
            }
        },
		rules_yes(){
            if(!this.form.isPass) return {}
            return {
                govAuditTime: [{ required: true, trigger: ['blur'], message: '请输入社保通过时间' }]
            }
        }
	},
	beforeMount(){

	},
	mounted() {
	},
	methods: {
		lang,
		save() {
            this.$refs['form'].validate(async (valid) => {
                if (!valid) return
                this.loading = true
                this.$ut.api('homecarelong/transaction/subscribe/gov/audit', {
                    communityId: this.comm.id,
                    ...this.form,
                    id:this.initData.id,
                }).then(()=>{
                    this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
                    this.$emit('fetchData')
                    this.$emit('close')
                }).finally(()=>{this.loading = false})
            })
        },
	},
}
</script>
<style lang="scss" scoped>
</style>
