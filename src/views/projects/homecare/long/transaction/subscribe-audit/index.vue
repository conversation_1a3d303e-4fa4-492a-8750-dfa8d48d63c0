<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="120"
			:op-fixed="true"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			table-name="long-customer-type"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button">

			</template>
			<template #op="{ row }">
				<el-button type="text" @click="handleInfo(row)">查看</el-button>		
				<el-button type="text" @click="handleGovAudit(row)">审核</el-button>
			</template>
			<template #cell="{ row, item }">
				<div v-if="item.prop === 'color'" class="color-box">
					<span class="color-preview" :style="{ backgroundColor: row.color }"></span>
					{{ row[item.prop] }}
				</div>
                <div v-else-if="item.prop === 'sex'">
                    <span v-if="row.sex==1">男</span>
                    <span v-if="row.sex==2">女</span>
				</div>
				<span v-else>{{ row[item.prop] }}</span>
			</template>
		</form-list>


		<!-- 审核 -->
		<ut-modal v-model="govAuditFormVisible" title="审核" width="500px">
			<gov-audit :init-data="selectRow" @close="govAuditFormVisible=false" @fetchData="fetchData" />
		</ut-modal>

		<!-- 详细 -->
		<ut-modal v-model="infoFormVisible" title="详细" width="800px">
			<info :init-data="selectRow" @close="infoFormVisible=false" />
		</ut-modal>

	</div>
</template>

<script>
// import EditPosition from './edit-position'
import FormList from '@/views/components/form-list'
import { mapGetters } from 'vuex'
import Vue from 'vue'
import GovAudit from './gov/audit.vue'
import Info from '../info'

export default {
	name: 'LongCustomerSource',
	components: {
		FormList,
		GovAudit,
		Info,
	},
	props: {
		height: {
			type: Number,
			default: () => Vue.prototype.$baseTableHeight(1),
		},
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			listLoading: false,
			fullscreen: false,
			columns: [
				{
					label: '序',
					align: 'center',
					prop: 'index',
					width:'50',
					fixed:'left',
					show: true,
				},
				{
					label: '姓名',
					align: 'left',
					prop: 'name',
					width:'120',
					fixed:'left',
					show: true,
				},
				{
					label: '性别',
					align: 'center',
					prop: 'sex',
					width:'60',
					show: true,
				},
				{
					label: '电话',
					align: 'center',
					prop: 'phone',
					width:'120',
					show: true,
				},
				{
					label: '证件号码',
					align: 'center',
					prop: 'idcard',
					minWidth:'180',
					show: true,
				},
				
				{
					label: '民族',
					align: 'center',
					prop: 'nation',
					width:'120',
					show: true,
				},
				{
					label: '出生日期',
					align: 'center',
					prop: 'birthday',
					width:'120',
					show: true,
				},
				{
					label: '地址',
					align: 'center',
					prop: 'address',
					width:'320',
					show: true,
				},		
				{
					label: '备注',
					align: 'center',
					prop: 'remark',
					width:'280',
					show: true,
				},				

			],
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 20,
			},
			selectRow: {},
			selectRows: [],
			govAuditFormVisible:false,
			infoFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	created() {
		this.fetchData()
	},
	methods: {
		async fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true
			const {data} = await this.$ut.api('homecarelong/transaction/subscribe/listpgSubmitNoAudit', {
				communityId:this.comm.id,
                ...this.page,
            }).finally(()=>{this.listLoading=false})
            this.dataList=data
		},
		handleSelect(rows) {
			this.$emit('select', rows)
		},
		onFullscreen(v) {
			this.fullscreen = v
		},
		onSelectRows(rows) {
			this.selectRows = rows
		},
		handleInfo(row) {
			this.selectRow = row
			this.infoFormVisible = true
		},
		handleGovAudit(row){
			this.selectRow = row
			this.govAuditFormVisible=true
		},

		handleDelete(row) {
			let ids = []
			if (row.id) {
				ids = [row.id]
			} else {
				if (this.selectRows.length > 0) {
					ids = this.selectRows.map((item) => item.id)
				} else {
					this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
					return
				}
			}
			this.$baseConfirm('你确定要删除吗', null, async () => {
				this.$ut.api('homecarelong/transaction/subscribe/delete', {
                    communityId: this.comm.id,
                    ids: ids,
                }).then(() => {
                    this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
                    this.fetchData()
                })
			})
		},

	},
}
</script>

<style lang="scss" scoped>
.color-box {
	display: flex;
	align-items: center;
	justify-content: center;

	.color-preview {
		width: 20px;
		height: 20px;
		margin-right: 10px;
        border-radius: 2px;
	}
}

</style>
