<template>
	<div class="flex ut-fullheight">
		
		<div style="width:200px">
			<el-card style="height:100%"  shadow="hover">
				<tree :height="height" @node-click="nodeClick"/>
			</el-card>
		</div>
		<div style="width:160px; padding:0 4px">
			<el-card style="height:100%;"  shadow="hover">
				<attendant-list :tree-node="treeNode" @select="handleAttendantSelect"/>
			</el-card>
		</div>
		<div style="width:calc(100% - 360px)">
			<customer-list :parent-data="selAttendant" />
		</div>
	</div>

</template>

<script>

import Vue from 'vue'
import AttendantList from './attendant-list.vue'
import CustomerList from './customer-list.vue'
import Tree from '../../basic/group/tree.vue'
export default {
	name:'LongTransactionCustomer',
	components: {
		AttendantList,
		CustomerList,
		Tree,
	},
	props: {
		selectWindow: {
			type: <PERSON>olean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		height:{
			type:[Number,String],
			default: () => Vue.prototype.$baseTableHeight(1),
		}
	},
	data() {
		return {
			fullscreen: false,
			treeNode:{},
			selAttendant:{},
		}
	},
	computed: {},
	created() {},
	methods: {
		nodeClick(node) {
			this.treeNode=node
		},

		handleSelect(rows){
			this.$emit('select',rows)
		},
		handleAttendantSelect(rows){
			if(rows && rows.length){
				this.selAttendant=rows[0]
			}
		}

	},
}
</script>
<style>
.ut-layout-column,.ut-app-main,.ut-main{
	height: 100%;
}

section{
	background: none !important;
	height: calc(100% - 40px);
	padding-bottom: 4px;
}
</style>
<style lang="scss" scoped>
:deep(.el-card__body){
	height: 100%;
}
</style>