<template>
	<div class="table-container attendant-table ut-body flex flex-direction ut-fullheight">
		<div class="flex">
			<el-button class="btn-search" type="primary" size="mini" @click="searchVisible = true">搜索</el-button>
			<el-button class="btn-search" size="mini" @click="onClear">清空</el-button>

		</div>
		<div v-loading="listLoading" class="flex-sub table-box">
			<el-table ref="myTable" :data="dataList.info" border height="calc(100% - 15px)" highlight-current-row @row-click="setSelectRows">
				<el-table-column prop="name" align="center" label="护理员" width="auto" />
			</el-table>
		</div>
		<div class="btn-box">
			<el-button size="mini" :disabled="page.pageindex==1" @click="prev">上一页</el-button>
			<el-button size="mini" :disabled="page.pageindex==dataList.page"  @click="next">下一页</el-button>
		</div>

		<el-dialog :visible.sync="searchVisible" title="护理员搜索" width="500px" append-to-body  :close-on-click-modal="false" :close-on-press-escape="false">
			<search  @fetchData="onSearch" @close="searchVisible=false" />
		</el-dialog>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import search from './search.vue'
export default {
	name: 'LongCustomerSource',
	components: {
		search
	},
	props: {
        treeNode:{
            type:Object,
            default:()=>{},
        }
	},
	data() {
		return {
			listLoading: false,			
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 20,
			},
			selectRow: {},
			selectRows: [],
			searchVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
    watch:{
		dataList: {
			deep: true,
			immediate: true,
			handler(newVal) {
				this.$nextTick(() => {
					let myTable = this.$refs.myTable
					if (myTable) myTable.doLayout()
					if(newVal.info){
							newVal.info.forEach((row) => {
							if(row._select) this.$refs.myTable.toggleRowSelection(row, true)
						})
					}
					
				})
			},
		},
        treeNode:{
            deep:true,
            handler(){
                this.fetchData()
            }
        }
    },
	created() {
		// this.fetchData()
	},
	methods: {
		async fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true
			const {data} = await this.$ut.api('homecarecustomer/group/attendant/listpg', {
				communityId:this.comm.id,
				module:'long',
                groupId:this.treeNode.id,
                ...this.page,
            }).finally(()=>{this.listLoading=false})
            this.dataList=data
		},
		setSelectRows(row){
			this.selectRow = row
			this.selectRows=[row]
			this.$emit('select',this.selectRows)
		},
		prev(){
			this.page.pageindex--
			this.fetchData()
		},
		next(){
			this.page.pageindex++
			this.fetchData()
		},
		onSearch(val){
			if(!val) return
			this.page.key = val
			this.fetchData()
		},
		onClear(){
			this.page.key = '';
			this.page.pageindex = 1;
			this.fetchData()
		}
	},
}
</script>

<style lang="scss" scoped>
:deep(.el-table th .cell){
	color: $base-color-blue !important;
}

:deep(.el-table .current-row) {
  box-shadow: 0 0 4px $base-color-blue;
}

.color-box {
	display: flex;
	align-items: center;
	justify-content: center;

	.color-preview {
		width: 20px;
		height: 20px;
		margin-right: 10px;
        border-radius: 2px;
	}
}

.attendant-table{
	:deep(.cell){
		cursor: pointer;
	}
}

.btn-box{
	padding: 8px 0 0 0;
	text-align: center;
	:deep(.el-button){
		padding: 5px 10px;
	}
}

.btn-search{
	margin: 0 5px 10px 5px;
}
</style>
