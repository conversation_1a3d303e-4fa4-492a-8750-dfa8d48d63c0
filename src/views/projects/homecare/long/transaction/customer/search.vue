<template>
	<div >
		<el-form ref="form" label-width="110px" :model="form" class="ut-form" @submit.native.prevent>
			<el-form-item label="名称：" prop="name">
				<el-input v-model.trim="form.name"  placeholder="请输入名称" />
			</el-form-item>
			
		</el-form>
		<div v-if="showFooter" class="ut-edit-footer">
			<el-button type="primary" @click="confirm">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>
<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
export default {
	components: {
	},
	props: {
		showFooter: {
			type: Boolean,
			default: () => true,
		},
	},
	data() {
		return {
			loading:false,
			form: {
				name:''
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	created(){
		
	},
	beforeMount(){
	},
	mounted(){
	},
	methods: {
		lang,
		confirm(){
            this.$refs['form'].validate(async (valid) => {
                if(!valid) return

                this.$emit('fetchData',this.form.name)
				this.$emit('close')
            })
        }
	},
}
</script>
<style lang="scss" scoped>

</style>