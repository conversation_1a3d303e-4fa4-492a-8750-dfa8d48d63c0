<template>
	<div v-loading="listLoading" class="table-container ut-body flex flex-direction ut-fullheight">
        <div class="search-box">
            <div class="search-box-key">
                <el-input v-model="page.key" placeholder="输入关键字搜索" @input="handleInput" @keydown.native.enter="fetchData">
                    <template slot="append">
                        <div class="search-button" @click="fetchData">
                        <i class="el-icon-search" ></i>搜索
                        </div>
                    </template>
                </el-input>
            </div>
            <el-button icon="el-icon-location-outline" :disabled="!parentData || !parentData.id" @click="openMap">地图上显示</el-button>
        </div>
        <div class="flex-sub table-box">
            <el-table ref="myTable" :data="dataList.info" border size="small" height="100%">
                <el-table-column prop="name" align="center" label="客户" :width="120" fixed="left" />
                <el-table-column prop="sex" align="center" label="性别" :width="60">
                    <template #default="{ row }">
                        <span v-if="row.sex==1">男</span>
                        <span v-if="row.sex==2">女</span>
                    </template>
                </el-table-column>
                <el-table-column prop="phone" align="center" label="电话" :width="120" />
                <el-table-column prop="idcard" align="center" label="证件号码" :width="180" />
                <el-table-column prop="nation" align="center" label="民族" :width="120" />
                <el-table-column prop="birthday" align="center" label="出生日期" :width="120" />
                <el-table-column prop="address" align="center" label="地址" :width="220" />
                <el-table-column prop="remark" align="center" label="备注" width="auto" :min-width="220" />
                <el-table-column prop="groupName" align="center" label="分组名称" :width="160" />
                <el-table-column prop="attendantName" align="center" label="护理员" :width="120" />
                <template #empty>
					<el-image class="ut-data-empty" :src="require('@/assets/empty_images/data_empty.png')" />
				</template>
            </el-table>
        </div>
        <el-pagination
			background
			:current-page="page.pageindex"
			layout="total, sizes, prev, pager, next, jumper"
			:page-size="page.pagesize"
			:total="dataList.record"
			@current-change="handleCurrentChange"
			@size-change="handleSizeChange"
		/>

	</div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
	name: 'LongTransactionCustomerList',
	components: {
	},
	props: {
		parentData:{
            type:Object,
            default:()=>{},
        }
	},
	data() {
		return {
			listLoading: false,
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 20,
			},
			selectRow: {},
			selectRows: [],
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
    watch: {

        parentData:{
            deep:true,
            handler(){
                this.fetchData()
            }
        }
    },
	created() {
		this.fetchData()
	},
	methods: {
		async fetchData(pageReq) {
            if(!this.parentData || !this.parentData.id) return
			if (pageReq) this.page = pageReq
			this.listLoading = true
			const {data} = await this.$ut.api('homecarelong/transaction/attendant/customer/listpg', {
				communityId:this.comm.id,
                module:'long',
                attendantId:this.parentData.attendantId,
                ...this.page,
            }).finally(()=>{this.listLoading=false})
            this.dataList=data
            this.initTable()
		},
        initTable(){
            let myTable = this.$refs.myTable
            if (myTable) myTable.doLayout()
            if(this.dataList.info){
                    this.dataList.info.forEach((row) => {
                    if(row._select) this.$refs.myTable.toggleRowSelection(row, true)
                })
            }
        },
        handleInput(v){
            if(v==='') {this.fetchData()}
        },
        handleCurrentChange(val) {
			this.page.pageindex = val
			this.fetchData()
		},
        handleSizeChange(val) {
			this.page.pagesize = val
			this.fetchData()
		},
        openMap(){
            if(!this.parentData || !this.parentData.id) return
            this.$router.push({path:'/long/basic/attendant/customer',query:{attendantId:this.parentData.attendantId}})
        },
		
	},
}
</script>

<style lang="scss" scoped>


 .search-box{
    display: flex;
    justify-content: space-between;
    .search-box-key{
        width:300px;
    }

    .search-box-button{
        padding:  0 20px;
    }
    padding-bottom: 8px;
}

.table-box{
    :deep(.el-table){
        height: 100%;
    }
}

.color-box {
	display: flex;
	align-items: center;
	justify-content: center;

	.color-preview {
		width: 20px;
		height: 20px;
		margin-right: 10px;
        border-radius: 2px;
	}
}

</style>
