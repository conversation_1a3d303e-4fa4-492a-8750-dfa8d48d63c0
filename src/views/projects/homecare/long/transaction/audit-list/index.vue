<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="140"
			:op-fixed="true"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			table-name="long-customer-type"
			:show-checkbox="false"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button">

			</template>
			<template #op="{ row }">
				<el-button type="text" @click="handleInfo(row)">查看</el-button>		
				<el-button type="text" @click="handleAuditCancel(row)">撤销审核</el-button>		
			</template>
			<template #cell="{ row, item }">
				<div v-if="item.prop === 'color'" class="color-box">
					<span class="color-preview" :style="{ backgroundColor: row.color }"></span>
					{{ row[item.prop] }}
				</div>
                <div v-else-if="item.prop === 'sex'">
                    <span v-if="row.sex==1">男</span>
                    <span v-if="row.sex==2">女</span>
				</div>
				<div v-else-if="item.prop === 'isSubmit'">
                    <span v-if="row.isSubmit">已提交</span>
				</div>
				<div v-else-if="item.prop === 'isAudit'">
                    <span v-if="row.isAudit">已审</span>
				</div>
				<div v-else-if="item.prop === 'workTimes'">
                    <span v-if="row.workTimes">{{row.workTimes}}</span>
				</div>
				<div v-else-if="item.prop === 'isPass'">
                    <span v-if="row.isPass==true">通过</span>
                    <span v-if="row.isPass==false">通过</span>
				</div>
				
				<span v-else>{{ row[item.prop] }}</span>
			</template>
		</form-list>

		<!-- 详细 -->
		<ut-modal v-model="infoFormVisible" title="详细" width="800px">
			<info :init-data="selectRow" @close="infoFormVisible=false" />
		</ut-modal>




	</div>
</template>

<script>
import Vue from 'vue'
import { mapGetters } from 'vuex'
import FormList from '@/views/components/form-list'
import Info from '../info'
export default {
	name: 'LongTransactionAuditList',
	components: {
		FormList,
		Info,
	},
	props: {
		height: {
			type: Number,
			default: () => Vue.prototype.$baseTableHeight(1),
		},
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			listLoading: false,
			fullscreen: false,
			columns: [
				{
					label: '序',
					align: 'center',
					prop: 'index',
					width:'50',
					fixed:'left',
					show: true,
				},
				{
					label: '姓名',
					align: 'left',
					prop: 'name',
					width:'120',
					fixed:'left',
					show: true,
				},
				{
					label: '性别',
					align: 'center',
					prop: 'sex',
					width:'60',
					show: true,
				},
				{
					label: '电话',
					align: 'center',
					prop: 'phone',
					width:'120',
					show: true,
				},
				{
					label: '证件号码',
					align: 'center',
					prop: 'idcard',
					minWidth:'180',
					show: true,
				},{
					label: '分组名称',
					align: 'center',
					prop: 'groupName',
					width:'160',
					show: true,
				},{
					label: '护理员',
					align: 'center',
					prop: 'attendantName',
					width:'120',
					show: true,
				},{
					label: '业务员',
					align: 'center',
					prop: 'salesmanName',
					width:'110',
					show: true,
				},
				{
					label: '民族',
					align: 'center',
					prop: 'nation',
					width:'120',
					show: true,
				},
				{
					label: '出生日期',
					align: 'center',
					prop: 'birthday',
					width:'120',
					show: true,
				},
				{
					label: '地址',
					align: 'center',
					prop: 'address',
					width:'320',
					show: true,
				},		
				{
					label: '备注',
					align: 'center',
					prop: 'remark',
					width:'280',
					show: true,
				},	
				{
					label: '服务次数',
					align: 'center',
					prop: 'workTimes',
					width:'90',
					show: true,
				},	
				{
					label: '提交',
					align: 'center',
					prop: 'isSubmit',
					width:'90',
					show: true,
				},		
				{
					label: '提交时间',
					align: 'center',
					prop: 'submitTime',
					width:'160',
					show: true,
				},		
				{
					label: '审核',
					align: 'center',
					prop: 'isAudit',
					width:'90',
					show: true,
				},	
				{
					label: '审核时间',
					align: 'center',
					prop: 'govAuditTime',
					width:'160',
					show: true,
				},						
				{
					label: '审核情况',
					align: 'center',
					prop: 'isPass',
					width:'90',
					show: true,
				},										
				{
					label: '通过备注',
					align: 'left',
					prop: 'govAuditRemark',
					width:'220',
					show: true,
				},										
				{
					label: '不通过原因',
					align: 'left',
					prop: 'noPassReason',
					width:'220',
					show: true,
				},										
				{
					label: '下次提醒时间',
					align: 'center',
					prop: 'nextGovAuditTime',
					width:'180',
					show: true,
				},				

			],
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 20,
			},
			selectRow: {},
			selectRows: [],
			assessFormVisible: false,
			datasetFormVisible:false,
			infoFormVisible:false,
			auditFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	created() {
		this.fetchData()
	},
	methods: {
		async fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true
			const {data} = await this.$ut.api('homecarelong/transaction/subscribe/listpgAudit', {
				communityId:this.comm.id,
                ...this.page,
            }).finally(()=>{this.listLoading=false})
            this.dataList=data
		},
		handleSelect(rows) {
			this.$emit('select', rows)
		},
		onFullscreen(v) {
			this.fullscreen = v
		},
		onSelectRows(rows) {
			this.selectRows = rows
		},
		handleAssess(row) {
			this.selectRow = row
			this.assessFormVisible=true
		},
		handleDataSet(row){
			this.selectRow = row
			this.datasetFormVisible=true
		},
		handleAudit(row){
			this.selectRow = row
			this.auditFormVisible=true
		},
		handleAuditCancel(row){
			this.$baseConfirm('确认撤销 '+row.name+'  的审核操作吗？', '确认操作', async () => {
				this.$ut.api('homecarelong/transaction/subscribe/gov/auditCancel',{
					communityId:this.comm.id,
                    id:row.id
				}).then(()=>{
					this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
					this.fetchData()
				})
			})
		},
		handleInfo(row){
			this.selectRow = row
			this.infoFormVisible=true
		},
		
	},
}
</script>

<style lang="scss" scoped>
.color-box {
	display: flex;
	align-items: center;
	justify-content: center;

	.color-preview {
		width: 20px;
		height: 20px;
		margin-right: 10px;
        border-radius: 2px;
	}
}

</style>
