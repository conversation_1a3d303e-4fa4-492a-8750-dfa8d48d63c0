<template>
    <div class=''>
        <el-form ref="form" label-width="120px" :model="form" :rules="{...rules,...rules_no}" class="ut-form">
			
			<el-row :gutter="10">
                <el-col>	
                    <el-form-item label="医保审核：" prop="govAudit">
                        <el-radio-group v-model="form.govAudit">
                            <el-radio-button :label="true">通过</el-radio-button>
                            <el-radio-button :label="false">不通过</el-radio-button>
                        </el-radio-group>
					</el-form-item>	
                </el-col>
				<el-col v-show="form.govAudit==false" :span="24">
					<el-form-item label="不通过原因：" prop="reason" >
						<el-input v-model.trim="form.reason" type="textarea" :rows="2"/>
					</el-form-item>
				</el-col>
				<el-col v-show="form.govAudit==false" :span="12">
					<el-form-item label="下次提醒时间：" prop="nextDate" >
						<el-date-picker	v-model="form.nextDate"	format="yyyy年MM月dd日"	value-format="yyyy-MM-dd"  type="date" placeholder="止日期"/>
					</el-form-item>
				</el-col>
                
				<el-col v-show="form.govAudit" :span="24">
					<el-form-item label="备注：" prop="remark">
						<el-input v-model.trim="form.remark" type="textarea" :rows="2"/>
					</el-form-item>
				</el-col>
			</el-row>
			
		</el-form>
		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">确 定</el-button>
			<el-button @click="$emit('close')">取 消</el-button>
		</div>
    </div>
</template>

<script>
export default {
    components: {},
    props: {
        
    },
    data() {
        return {
            form:{
                govAudit:null,
                reason:''
            },

            rulesNoPass:{
                govAudit: [{ required: true, trigger: ['blur','change'], message: '请选择审核状态' }],
            }
        }
    },
    computed: {
        rules(){
            return {govAudit: [{ required: true, trigger: ['blur','change'], message: '请选择审核状态' }]}
        },
        rules_no(){
            if(this.form.govAudit) return {}
            return {
                reason: [{ required: true, trigger: ['blur'], message: '请输入原因' }],
                nextDate:[{ required: true, trigger: ['blur'], message: '请输入下次提醒时间' }]
            }
        }
    },
    watch: {
    },
    created() {
    },
    beforeMount() {
    },
    destroyed() {
    },
    methods: {
        save(){
            this.$refs['form'].validate(async (valid) => {
				if (!valid) return
            })
        }
    },
}
</script>
<style lang='scss' scoped>

</style>