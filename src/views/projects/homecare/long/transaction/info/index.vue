<template>
    <div v-loading="loading">
     <div class="ut-body">
         <el-tabs v-model="tabName" type="border-card" style="height: 460px;">
            <el-tab-pane name="main">
                <span slot="label">内容</span>
                <div>
                    <el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
                        <el-row>
                            <el-col :span="12">							
                                <el-form-item label="姓名：" prop="name">
                                    <span>{{form.name}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="性别：" prop="sex">
                                    <span v-if="form.sex==0">不详</span>
                                    <span v-if="form.sex==1">男</span>
                                    <span v-if="form.sex==2">女</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">		
                                <el-form-item label="电话：" prop="phone">
                                      <span>{{form.phone}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="证件号：" prop="idcard">
                                     <span>{{form.idcard}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="出生日期：" prop="birthday">
                                    <span>{{form.birthday}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">		
                                <el-form-item label="民族：" prop="nation">
                                    <span>{{form.nation}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">		
                                <el-form-item label="学历：" prop="education">
                                    <span>{{form.education}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">		
                                <el-form-item label="婚姻：" prop="marriage">
                                    <span>{{form.marriage}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">		
                                <el-form-item label="居住状态：" prop="liveState">
                                    <span>{{form.liveState}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">		
                                <el-form-item label="失能等级：" prop="disabilityLevel">
                                    <span>{{form.disabilityLevel}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">		
                                <el-form-item label="政治面貌：" prop="politics">
                                    <span>{{form.politics}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">		
                                <el-form-item label="方言：" prop="dialect">
                                    <span>{{form.dialect}}</span>
                                </el-form-item>
                            </el-col>
                            
                            <el-col :span="12">		
                                <el-form-item label="宗教信仰：" prop="religion">
                                    <span>{{form.religion}}</span>
                                </el-form-item>
                            </el-col>                          
                            <el-col :span="12">
                                <el-form-item label="经纬度：" prop="longitude">
                                    <div>{{form.longitude}}<span v-if="form.longitude">,</span>{{form.latitude}}</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="20">		
                                <el-form-item label="地址：" prop="address">
                                    <span>{{form.address}}</span>
                                </el-form-item>
                            </el-col>						
  
        						
                            <el-col :span="24">		
                                <el-form-item label="备注：" prop="remark">
                                    <span>{{form.textarea}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="业务员:" prop="salesmanId">
                                    <span>{{form.salesmanName}}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
            </el-tab-pane>
            <el-tab-pane name="dataset">
                <span slot="label">资料</span>
                <data-set :init-data="initData" :readonly="true" :height="400" />
            </el-tab-pane>
         </el-tabs>

     </div>
     

 </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
import DataSet from './data-set'
export default {
 components: {
    DataSet,
 },
 props: {
     initData:{
         type:Object,
         default:()=>{},
     }
 },
 data() {
     return {
         loading:false,
         form:{},
         tabName:'main',
         rules: {
             validBegin: [{ required: true, trigger: 'blur', message: '请输入有效时起（起）' }],
             validEnd: [{ required: true, trigger: 'blur', message: '请输入有效时起（止）' }],
             firstNurseId: [{ required: true, trigger: 'blur', message: '请选择评定人' }],
             firstTime: [{ required: true, trigger: 'blur', message: '请输入评定时间' }],
             
         },
         person:{},
         basicProject:[],
         firstNurseFormVisible:false,
     }
 },
 computed: {
     ...mapGetters({
         comm: 'comm/comm',
     }),
 },
 watch: {
 },
 created() {
 },
 async beforeMount() {
     await this.getBasicProject()
     this.getInfo()
 },
 destroyed() {
 },
 methods: {        
     lang,
     async getInfo() {
         if (!this.initData.id) return
         this.loading = true
         const { data } = await this.$ut.api('homecarelong/transaction/subscribe/info', {
             communityId: this.comm.id,
             id: this.initData.id,
         }).finally(()=>{this.loading=false})
       
         this.form=data || {}	
     },
     async getDataSet() {
        if (!this.initData.id) return
        this.loading=true
        const { data } = await this.$ut.api('ut/api/info', {
            communityId: this.comm.id,
            id: this.initData.id,
        }).finally(()=>{this.loading = false})
        this.form = data
     },
     async getBasicProject() {
         this.loading = true
         const { data } = await this.$ut.api('homecarelong/basic/project/list', {
             communityId: this.comm.id,
         }).finally(()=>{this.loading=false})
         this.basicProject=data || []
     
     },
     
 },
}
</script>
<style lang='scss' scoped>
.customer-info{
 padding-bottom: 8px;
 line-height: 1.5;
}
.project-wrapper{
 display: flex;
 flex-direction: row;
 padding-bottom: 12px;
 .title{
     width:130px;
     text-align: right;
     padding-right: 4px;
 }

}
.project-box{
 display: flex;
 flex-direction: row;
 flex-wrap: wrap;
 max-height: 330px;
 min-height: 200px;
 overflow: auto;
 flex: 1;
 border: 1px solid #f2f2f2;
 padding: 8px;
 font-size: 16px;
}
.project-item{
 min-width: 200px;
 line-height: 2;
}

.select-window{

 :deep(.ut-list){
     .el-card__body{
         height: auto !important;
     }
 }

 :deep(.ut-tree){
     .el-card{
         height: 100%;
     }
     .el-card__body{
         height: auto !important;
     }
 }

 :deep(.ut-tree-list){
     padding: 0;
 }

}

:deep(.el-tabs__content){
    height: 400px;
    overflow: scroll;
}

:deep(.el-form-item){
    margin-bottom: 0 !important;
}

:deep(.el-form-item__label){
    color: gray;
}
</style>