<template>
	<div v-loading="listLoading" class="box-wrapper ut-body" :style="{height:height+'px'}">
		<div class="box">
			<template v-for="(item,index) in dataList">
				<div :key="index" class="item">
					<div class="title"><span>{{item.title}}</span></div>
					<div class="content">
						<template v-for="(subItem,subIndex) in item.details">
							<div :key="subIndex" class="sub-item">
								<div class="sub-title">
									<span>{{ subItem.title }}</span>
									<span v-if="subItem.require" class="required">必填</span>
								</div>
								<div class="sub-content">
									<ut-media :url-list="subItem.files" :width="160" :height="120" delete-color="#efefef" :class="{readonly}" :max-count=1 @delete="(v,i)=>{return subFileDelete(subItem,v,i) }">
										<template v-if="subItem.count === 0 || subItem.files.length<subItem.count" slot="add">
											<el-upload
												ref="upload" :action="uploadAction" :disabled="readonly" :show-file-list="false"
												list-type="picture-card"
												:before-upload="beforeUpload.bind(null,subItem)"
												:file-list="subItem.files" :on-success="onSuccessBg.bind(null, subItem)">
												<i slot="default" class="el-icon-plus"></i>
											</el-upload>
										</template>
									</ut-media>
								</div>
							</div>
						</template>

					</div>
				</div>

			</template>
		</div>


	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import Vue from 'vue'
import UploadMixins from '../../../components/upload.mixins.js'

export default {
	components: {
	},
	mixins: [UploadMixins],
	props: {
		height: {
			type: Number,
			default: () => Vue.prototype.$baseTableHeight(1),
		},
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
		initData:{
			type: Object,
			default: () => ({}),
		},
		readonly: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			listLoading: false,
			dataList:[],

			fileListBg:[],
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),

	},
	created() {
		this.fetchData()
	},
	methods: {
		async fetchData() {
			this.listLoading = true
			const {data} = await this.$ut.api('homecarelong/transaction/subscribe/dataset/list', {
				communityId:this.comm.id,
                personId:this.initData.id,
            }).finally(()=>{this.listLoading=false})
            this.dataList=data
		},

		onSuccessBg(obj,response){
			if (response[0] != undefined) {
				const { source, name,ext } = response[0]
				if (source != undefined && name != undefined) {
					obj.files.push({
						id:'',
						name:source,
						url:`${this.uploadInfo.preview}?file=${name}${ext}`
					})
				}
			}
		},
		subFileDelete(obj,v,index){
			obj.files.splice(index,1)
		},
		beforeUpload(obj,file){
			const isLt5M = file.size / 1024 / 1024 < 5;
			if (!isLt5M) {
				this.$message.error('上传内容不能超过 5MB!');
				return false
			}
			if(obj.ext){
				let ext = file.name.split('.').pop();
				let arr=obj.ext.split('|')
				let existExt = arr.find(u=>u==ext)


				if (!existExt) {
					this.$message.error('需要上传指定格式:'+obj.ext.replaceAll('|',' , '))
					return false
				}
			}
			return true
		}

	},
}
</script>

<style lang="scss" scoped>
.item{
	border-top: 1px solid #a0a0a0;
	border-right: 1px solid #a0a0a0;
	border-left: 1px solid #a0a0a0;
	display: flex;
	&:last-child{
		border-bottom: 1px solid #a0a0a0;
	}
	.title{
		display: flex;
		align-items: center;
    	justify-content: center;
		width:100px;
		border-right:1px solid  #a0a0a0;
		padding: 10px;
		font-weight: bold;
	}
	.content{
		padding: 10px;
		display: flex;
		.sub-item{
			margin: 5px;
		}
	}
}

.sub-item{
	display: flex;
	flex-direction: column;
    align-items: center;
	border: 1px dashed #eee;
	border-radius: 4px;
	&:hover{
		border: 1px dashed #aaa;
		.sub-title{
			border-bottom: 1px dashed #aaa;
		}
	}
	.sub-title{
		font-weight: bold;
		border-bottom: 1px dashed #eee;
		width: 100%;
		text-align: center;
		padding: 4px;
		.required{
			color: #a0a0a0;
			font-weight: normal;
			font-size: 12px;
			padding-left: 10px;
		}
	}
}

.box-wrapper{
	display: flex;
    flex-direction: column;
    justify-content: space-between;

	.box{
		overflow-y: auto;
		padding-bottom: 8px;
	}
}

.footer{
	border-top: 1px dashed #ccc;
	padding-top: 16px;
	text-align: center;
}

:deep(.el-checkbox__label){
	font-size: 16px;
    padding: 10px 10px 10px 0;
}

:deep(.el-upload--picture-card){
	width:160px;
	height: 120px;
	line-height: 120px;
}

.readonly {
	:deep(.el-upload--picture-card), :deep(.viewer-delete) {
		display: none !important;
	}
}


</style>
