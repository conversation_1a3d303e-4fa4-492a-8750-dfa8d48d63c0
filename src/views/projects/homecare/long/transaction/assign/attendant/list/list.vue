<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="80"
			:op-fixed="true"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			:show-search="false"
			table-name="long-customer-type"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button">
			</template>
			<template #op="{ row }">
				<!-- <el-button type="text" @click="handleEdit(row)">编辑</el-button> -->
				<el-button type="text" @click="handleDelete(row)">删除</el-button>
			</template>
			<template #cell="{ row, item }">
				<div v-if="item.prop === 'color'" class="color-box">
					<span class="color-preview" :style="{ backgroundColor: row.color }"></span>
					{{ row[item.prop] }}
				</div>
                <div v-else-if="item.prop === 'map'">
                    <span class="map-span" @click="attendantClick(row)">地图</span>
				</div>
                <div v-else-if="item.prop === 'sex'">
                    <span v-if="row.sex==1">男</span>
                    <span v-if="row.sex==2">女</span>
				</div>
				<div v-else-if="item.prop === 'distance'">
                    <span v-if="row.distance">{{row.distance}} km</span>
				</div>
				<div v-else-if="item.prop === 'centerDistance'">
                    <span v-if="row.centerDistance">{{row.centerDistance}} km</span>
				</div>
				<span v-else>{{ row[item.prop] }}</span>
			</template>
		</form-list>

		<!-- 地图显示 -->
		<ut-modal v-model="attendantMapFormVisible" width="1000px" title="客户位置显示" top="0">
			<map-customer-list :attendant-id="selectRow.attendantId" :new-customer="parentData"/>
		</ut-modal>

	</div>
</template>

<script>
import FormList from '@/views/components/form-list'
import { mapGetters } from 'vuex'
import Vue from 'vue'
import MapCustomerList from '../../../../basic/attendant/customer-list.vue'
export default {
	name: 'LongCustomerSource',
	components: {
		FormList,
		MapCustomerList,
	},
	props: {
		height: {
			type: Number,
			default: () => Vue.prototype.$baseTableHeight(1),
		},
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
        treeNode:{
            type:Object,
            default:()=>{},
        },
		parentData:{
            type:Object,
            default:()=>{},
        }
	},
	data() {
		return {
			listLoading: false,
			fullscreen: false,
			columns: [
				{
					label: '分组',
					align: 'left',
					prop: 'groupName',
					width:'120',
					show: true,
				},
				{
					label: '姓名',
					align: 'left',
					prop: 'name',
					width:'120',
					show: true,
				},
				{
					label: '距护理员',
					align: 'center',
					prop: 'distance',
					width:'100',
					show: true,
				},
				
				{
					label: '中心距离',
					align: 'center',
					prop: 'centerDistance',
					width:'100',
					show: true,
				},
				{
					label: '性别',
					align: 'center',
					prop: 'sex',
					width:'60',
					show: true,
				},
				{
					label: '电话',
					align: 'center',
					prop: 'phone',
					width:'120',
					show: true,
				},
				{
					label: '证件号码',
					align: 'center',
					prop: 'idcard',
					minWidth:'220',
					show: true,
				},
				
				{
					label: '民族',
					align: 'center',
					prop: 'nation',
					width:'120',
					show: true,
				},
				{
					label: '出生日期',
					align: 'center',
					prop: 'birthday',
					width:'120',
					show: true,
				},
				{
					label: '电子邮箱',
					align: 'center',
					prop: 'email',
					width:'180',
					show: true,
				},
				{
					label: '地图',
					align: 'center',
					prop: 'map',
					width:'80',
					fixed:'right',
					show: true,
				}				

			],
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 20,
				order: '',
			},
			selectRow: {},
			selectRows: [],
			attendantMapFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
    watch:{
        treeNode:{
            deep:true,
            handler(){
                this.fetchData()
            }
        }
    },
	created() {
		// this.fetchData()
	},
	methods: {
		async fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true
			const {data} = await this.$ut.api('homecarecustomer/group/attendant/listpg_distance', {
				communityId:this.comm.id,
				module:'long',
                groupId:this.treeNode.id,
				customerId:this.parentData.customerId,
                ...this.page,
            }).finally(()=>{this.listLoading=false})
            this.dataList=data
			//this.getEmployeeInfo()
		},
		async getEmployeeInfo() {
			const ids=this.dataList.info.map(o=>o.employeeId)
			if(!ids.length) return
			
			this.listLoading = true
			const {data} = await this.$ut.api('homecarepersonnel/employee/listByIds', { 
				communityId:this.comm.id,
				ids: ids 
			}).finally(()=>{this.listLoading=false})
			if(data){
				this.dataList.info.forEach(item=>{
					let obj=data.find(u=>u.id==item.employeeId)
					if(obj){
						this.$set(item,'name',obj.name)
						this.$set(item,'phone',obj.phone)
						this.$set(item,'sex',obj.sex)
						this.$set(item,'idcard',obj.idcard)
						this.$set(item,'nation',obj.nation)
						this.$set(item,'birthday',obj.birthday)
						this.$set(item,'email',obj.email)
						this.$set(item,'education',obj.education)
						this.$set(item,'address',obj.address)
					}
				})
			}
		},
		handleSelect(rows) {
			this.$emit('select', rows)
		},
		onFullscreen(v) {
			this.fullscreen = v
		},
		onSelectRows(rows) {
			this.selectRows = rows
		},
		attendantClick(row){
			this.selectRow=row
			this.attendantMapFormVisible=true
		}

	},
}
</script>

<style lang="scss" scoped>
.color-box {
	display: flex;
	align-items: center;
	justify-content: center;

	.color-preview {
		width: 20px;
		height: 20px;
		margin-right: 10px;
        border-radius: 2px;
	}
}

.map-span{
	color: $base-color-blue;
	cursor: pointer;
}
</style>
