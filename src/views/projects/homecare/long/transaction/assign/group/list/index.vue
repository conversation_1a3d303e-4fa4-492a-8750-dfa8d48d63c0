<template>
	<div>
        <tree @node-click="nodeClick" />

		<div class="ut-edit-footer">
			<el-button type="primary" :disabled="selNode.id==curNode.id || !selNode.id" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
import Tree from '../../../../customer/group/tree.vue'

export default {
	components:{
        Tree
	},
	props: {
		curNode: {
			type: Object,
			default: () => ({}),
		},
		parentData: {
			type: Object,
			default: () => ({}),
		},
	},
	
	data() {
		return {
            selNode:{}
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

	},
	mounted() {
	},
	methods: {
		lang,
        nodeClick(node){
            this.selNode=node
        },
		save(){
            this.$emit('select',[this.selNode])
        }
	},
}
</script>
<style lang="scss" scoped>
:deep(.el-tree){
    min-height: 400px;
    padding: 8px;
}
</style>
