<template>
	<div>
        <el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-form-item label="工作组：" prop="groupId">
                <el-input v-model.trim="form.groupName" placeholder="请选择工作组" @focus="groupFormVisible=true">
                    <template slot="append">
                        <i class="el-icon-search" @click="groupFormVisible=true"></i>
                    </template>
                </el-input>
			</el-form-item>
        </el-form>

		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>

        <ut-modal v-model="groupFormVisible" width="400px" title="选择分组">	    
			<group-list :select-window="true" :select-single="true" :height="600" @select="handleSelectGroup" @close="groupFormVisible=false"/>
        </ut-modal>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
import GroupList from './list'

export default {
	components:{
        GroupList,
	},
	props: {
		parentData: {
			type: Object,
			default: () => ({}),
		},
	},
	
	data() {
		return {
            selNode:{},
            form:{
                groupId:'',
            },
            rules: {
				groupName: [{ required: true, trigger: 'change', message: '请选择分组' }],
			},
            groupFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

	},
	mounted() {
	},
	methods: {
		lang,
        nodeClick(node){
            this.selNode=node
        },
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
		
				await this.$ut.api('homecarelong/transaction/subscribe/distribution/group', {
					communityId:this.comm.id,
                    personId:this.parentData.id,
					groupId:this.form.groupId,
				}).finally(()=>{this.loading = false})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
        handleSelectGroup(rows){
			this.groupFormVisible = false
			if (rows && rows[0]) {
				this.form.groupId = rows[0].id
				this.form.groupName = rows[0].name
			}
		},
	},
}
</script>
<style lang="scss" scoped>
.select-window{

	:deep(.ut-list){
		.el-card__body{
			height: auto !important;
		}
	}

	:deep(.ut-tree){
		.el-card{
			height: 100%;
		}
		.el-card__body{
			height: auto !important;
		}
	}

}
</style>
