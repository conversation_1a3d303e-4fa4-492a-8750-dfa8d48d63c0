<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="180"
			:op-fixed="true"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			table-name="long-customer-type"
			:show-checkbox="false"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button">

			</template>
			<template #op="{ row }">
				<el-button type="text" @click="handleGroup(row)">指派到组</el-button>
				<el-button type="text" @click="handleAttendant(row)">指派到护理员</el-button>
				<!-- <el-button type="text" @click="handleNoPass(row)">取消审核</el-button> -->
			</template>
			<template #cell="{ row, item }">
				<div v-if="item.prop === 'color'" class="color-box">
					<span class="color-preview" :style="{ backgroundColor: row.color }"></span>
					{{ row[item.prop] }}
				</div>
                <div v-else-if="item.prop === 'sex'">
                    <span v-if="row.sex==1">男</span>
                    <span v-if="row.sex==2">女</span>
				</div>
				<div v-else-if="item.prop==='isReceive'">
					<span v-if="row.isReceive">已接收</span>
				</div>
				<span v-else>{{ row[item.prop] }}</span>
			</template>
		</form-list>

		<!-- 指定到分组 -->
		<ut-modal v-model="groupFormVisible" width="500px" title="分组选择">	
			<group :parent-data="selectRow" @fetchData="fetchData" @close="groupFormVisible=false" />
		</ut-modal>

		<!-- 指定到护理员 -->
		<ut-modal v-model="attendantFormVisible" width="500px" title="护理员选择">
			<attendant :parent-data="selectRow" @fetchData="fetchData" @close="attendantFormVisible=false" />
		</ut-modal>


		<!-- 回退 -->
		<ut-modal v-model="nopassFormVisible" width="500px" title="回退">
			<nopass :init-data="selectRow" @fetchData="fetchData" @close="nopassFormVisible=false" />
		</ut-modal>

	</div>
</template>

<script>
import Group from './group'
import Attendant from './attendant'
import Nopass from './nopass'
import FormList from '@/views/components/form-list'
import { mapGetters } from 'vuex'
import Vue from 'vue'
export default {
	name: 'LongTransactionAssign',
	components: {
		FormList,
		Group,
		Attendant,
		Nopass,
	},
	props: {
		height: {
			type: Number,
			default: () => Vue.prototype.$baseTableHeight(1),
		},
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			listLoading: false,
			fullscreen: false,
			columns: [
				{
					label: '序',
					align: 'center',
					prop: 'index',
					width:'50',
					fixed:'left',
					show: true,
				},
				{
					label: '姓名',
					align: 'left',
					prop: 'name',
					width:'120',
					fixed:'left',
					show: true,
				},
				{
					label: '性别',
					align: 'center',
					prop: 'sex',
					width:'60',
					show: true,
				},
				{
					label: '电话',
					align: 'center',
					prop: 'phone',
					width:'120',
					show: true,
				},
				{
					label: '证件号码',
					align: 'center',
					prop: 'idcard',
					minWidth:'180',
					show: true,
				},{
					label: '分组名称',
					align: 'center',
					prop: 'groupName',
					width:'160',
					show: true,
				},{
					label: '护理员',
					align: 'center',
					prop: 'attendantName',
					width:'120',
					show: true,
				},
				{
					label: '民族',
					align: 'center',
					prop: 'nation',
					width:'120',
					show: true,
				},
				{
					label: '出生日期',
					align: 'center',
					prop: 'birthday',
					width:'120',
					show: true,
				},
				{
					label: '地址',
					align: 'center',
					prop: 'address',
					width:'320',
					show: true,
				},		
				{
					label: '备注',
					align: 'center',
					prop: 'remark',
					width:'280',
					show: true,
				},	
					
				{
					label: '接收',
					align: 'center',
					prop: 'isReceive',
					width:'80',
					show: true,
				},				

			],
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 20,
			},
			selectRow: {},
			selectRows: [],
			dialogFormVisible: false,
			groupFormVisible:false,
			attendantFormVisible:false,
			nopassFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	created() {
		this.fetchData()
	},
	methods: {
		async fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true
			const {data} = await this.$ut.api('homecarelong/transaction/subscribe/listpgGovPass', {
				communityId:this.comm.id,
                ...this.page,
            }).finally(()=>{this.listLoading=false})
            this.dataList=data
		},
		handleSelect(rows) {
			this.$emit('select', rows)
		},
		onFullscreen(v) {
			this.fullscreen = v
		},
		onSelectRows(rows) {
			this.selectRows = rows
		},
		handleAdd() {
			this.selectRow = {}
			this.dialogFormVisible=true
		},
		handleGroup(row) {
			this.selectRow = row
			this.groupFormVisible = true
		},
		handleNoPass(row){
			this.selectRow = row
			this.nopassFormVisible = true
		},
		handleAttendant(row) {
			this.selectRow = row
			this.attendantFormVisible = true
		},
		// handlePosition(row){
		// 	this.selectRow = row
		// 	this.positionFormVisible = true
		// },
		
	},
}
</script>

<style lang="scss" scoped>
.color-box {
	display: flex;
	align-items: center;
	justify-content: center;

	.color-preview {
		width: 20px;
		height: 20px;
		margin-right: 10px;
        border-radius: 2px;
	}
}

</style>
