<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-row>
                <el-col :span="24">							
                    <el-form-item label="回退原因：" prop="remark">
                        <el-input v-model.trim="form.remark" type="textarea" placeholder="请说明回退原因" />
                    </el-form-item>
                </el-col>
            </el-row>
		</el-form>


		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('回退') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
export default {
	components:{
	},
	mixins: [],
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
	},
	
	data() {
		return {
			loading: false,
			form: {
				remark: '',
			},
			rules: {
				remark: [{ required: true, trigger: 'blur', message: '请输入备注' }],
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},

	methods: {
		lang,
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
		
				await this.$ut.api('homecarelong/transaction/subscribe/gov/auditPassCanccel', {
					communityId:this.comm.id,
					id:this.initData.id,
					...this.form,
				}).finally(()=>{this.loading = false})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
	},
}
</script>
<style lang="scss" scoped>
</style>
