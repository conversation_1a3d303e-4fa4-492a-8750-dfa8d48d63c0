<template>
	<div v-loading="loading">
		
		<div src="https://apis.map.qq.com/tools/locpicker?search=1&type=1&key=SFABZ-WANWW-FISRY-3IGTF-HV7RE-YSFTI&referer=myapp"></div>
		<el-form ref="form" label-width="90px" :model="form" :rules="rules" class="ut-form">
			<el-row>
				<el-col :span="12">							
					<el-form-item label="姓名：" prop="name">
						<el-input v-model.trim="form.name" placeholder="请输入名称" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="性别：" prop="sex">
						<el-select v-model="form.sex">
							<el-option label="不详" :value="0" />
							<el-option label="男" :value="1" />
							<el-option label="女" :value="2" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">		
					<el-form-item label="电话：" prop="phone">
						<el-input v-model.trim="form.phone" placeholder="请输入电话" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="证件号：" prop="idcard">
						<el-input v-model.trim="form.idcard" placeholder="请输入证件号" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="出生日期：" prop="birthday">
						<el-date-picker v-model="form.birthday" type="date" />
					</el-form-item>
				</el-col>
				<el-col :span="12">		
					<el-form-item label="民族：" prop="nation">
						<el-input v-model.trim="form.nation" />
					</el-form-item>
				</el-col>
				<el-col :span="12">		
					<el-form-item label="学历：" prop="education">
						<el-select v-model="form.education">
							<el-option label="不详" value="" />
							<el-option label="无学历" value="无学历" />
							<el-option label="小学" value="小学" />
							<el-option label="初中" value="初中" />
							<el-option label="中专" value="中专" />
							<el-option label="大专" value="大专" />
							<el-option label="本科" value="本科" />
							<el-option label="硕士" value="硕士" />
							<el-option label="博士" value="博士" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">		
					<el-form-item label="婚姻：" prop="marriage">
						<el-select v-model="form.marriage">
							<el-option label="未知" value="" />
							<el-option label="已婚" value="已婚" />
							<el-option label="离异" value="离异" />
							<el-option label="丧偶" value="丧偶" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">		
					<el-form-item label="居住状态：" prop="liveState">
						<el-select v-model="form.liveState">
							<el-option label="未知" value="" />
							<el-option label="独居" value="独居" />
							<el-option label="与配偶/伴侣居住" value="与配偶/伴侣居住" />
							<el-option label="与子女居住" value="与子女居住" />
							<el-option label="与父母居住" value="与父母居住" />
							<el-option label="与兄弟姐妹居住" value="与兄弟姐妹居住" />
							<el-option label="与其他亲属居住" value="与其他亲属居住" />
							<el-option label="与非亲属关系居住" value="与非亲属关系居住" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">		
					<el-form-item label="失能等级：" prop="disabilityLevel">
						<el-select v-model="form.disabilityLevel">
							<el-option label="未知" value="" />
							<el-option label="能力完好" value="能力完好" />
							<el-option label="轻度失能" value="轻度失能" />
							<el-option label="中度失能" value="中度失能" />
							<el-option label="重度失能" value="重度失能" />
							<el-option label="完全失能" value="完全失能" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">		
					<el-form-item label="政治面貌：" prop="politics">
						<el-select v-model="form.politics">
							<el-option label="未知" value="" />
							<el-option label="群众" value="群众" />
							<el-option label="共青团员" value="共青团员" />
							<el-option label="中共党员" value="中共党员" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">		
					<el-form-item label="方言：" prop="dialect">
						<el-input v-model.trim="form.dialect" />
					</el-form-item>
				</el-col>
				
				<el-col :span="12">		
					<el-form-item label="宗教信仰：" prop="religion">
						<el-select v-model="form.religion">
							<el-option label="未知" value="" />
							<el-option label="佛教" value="佛教" />
							<el-option label="道教" value="道教" />
							<el-option label="天主教" value="天主教" />
							<el-option label="基督教" value="基督教" />
							<el-option label="伊斯兰教" value="伊斯兰教" />
							<el-option label="其他" value="其他" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="20">		
					<el-form-item label="地址：" prop="address">
						<el-input v-model.trim="form.address" />
					</el-form-item>
				</el-col>						
				<el-col :span="4">
					<el-button @click="qqmapDialog=true">坐标拾取</el-button>
				</el-col>
				<el-col :span="12">
					<el-form-item label="经度" prop="address">
						<el-input v-model.trim="form.longitude" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="纬度" prop="address">
						<el-input v-model.trim="form.latitude" />
					</el-form-item>
				</el-col>						
				<el-col :span="24">		
					<el-form-item label="备注：" prop="remark">
						<el-input v-model.trim="form.remark" type="textarea" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="业务员:" prop="salesmanId">
						<el-input v-model="form.salesmanName" placeholder="请选择业务员" @focus="salesmanFormvisiable=true">
							<template slot="append">
								<i class="el-icon-search" @click="salesmanFormvisiable=true"></i>
							</template>
						</el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>


		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>

		<el-dialog  v-if="qqmapDialog" v-dialog-drag  append-to-body :close-on-click-modal="true" title="坐标拾取" top="3vh" :visible.sync="qqmapDialog" width="1000px">
			<div style="width:100%;height:800px">
				<qq-map :map-key="qqmap.mapKey" :key-name="qqmap.keyName" :lng="form.longitude" :lat="form.latitude" @callback="callback"  @close="qqmapDialog = false"/>
			</div>
		</el-dialog>

		<ut-modal v-model="salesmanFormvisiable" title="选择业务员" width="500px" top="90px">
			<salesman-list :height="500" :select-single="true" :select-window="true" @select="handleSelectSalesman" />
		</ut-modal>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
import UploadMixins from '../../../components/upload.mixins.js'
import QqMap from '@/views/components/qq-map' 
import SalesmanList from '../../basic/salesman/index.vue'
export default {
	components:{
		QqMap,
		SalesmanList,
	},
	mixins: [UploadMixins],
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
	},
	
	data() {
		return {
			loading: false,
			form: {
				id: '',
				name: '',
				showIndex: '',
				longitude:'',
				valid:true,
				salesmanId:'',
			},
			faceUrl: require('../../../static/personface.jpg'),
			rules: {
				name: [{ required: true, trigger: 'blur', message: '请输入名称' }],
				// salesmanId: [{ required: true, trigger: 'blur', message: '请输入业务员' }],
			},
			qqmapDialog:false,
			qqmap:{
				mapKey:'SFABZ-WANWW-FISRY-3IGTF-HV7RE-YSFTI',
				keyName:'myapp',
			},
			salesmanFormvisiable:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarelong/transaction/subscribe/info', {
				communityId:this.comm.id,
				module:'long',
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
		
		},
		clearImage() {
			this.fileList = []
		},
		handleSelectType(rows){
			this.typeFormVisible = false
			if (rows && rows[0]) {
				this.form.typeId = rows[0].id
				this.form.typeName = rows[0].name
			}
		},
		handleSelectLevel(rows){
			this.levelFormVisible = false
			if (rows && rows[0]) {
				this.form.levelId = rows[0].id
				this.form.levelName = rows[0].name
			}
		},
		handleSelectSource(rows){
			this.sourceFormVisible = false
			if (rows && rows[0]) {
				this.form.sourceId = rows[0].id
				this.form.sourceName = rows[0].name
			}
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
		
				await this.$ut.api('homecarelong/transaction/subscribe/save', {
					communityId:this.comm.id,
					module:'long',
					...this.form,
				}).finally(()=>{this.loading = false})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
		callback(loc){
			this.form.latitude=loc.latlng.lat
			this.form.longitude=loc.latlng.lng
			this.form.address=loc.poiaddress
		},
		handleSelectSalesman(rows) {
			this.salesmanFormvisiable= false
			if (rows && rows[0]) {
				this.form.salesmanId = rows[0].id
				this.form.salesmanName = rows[0].name
			}
		},
	},
}
</script>
<style lang="scss" scoped>
.color-box {
	display: inline-block;
	height: 32px;
	width: 150px;
	border: 1px solid #dcdfe6;
	padding-left: 20px;

	.color-box-text {
		color:#fff;
		mix-blend-mode: difference;
	}
}
.placeholder {
	height: 70px;
}

.face-body {
	display: flex;
	justify-content: flex-end;

	.face-top {
		padding-right: 10px;
		flex: 1;
	}

	.face-ewm {
		margin: 0 auto;
		text-align: center;
	}

	.ewm-tips {
		margin-top: 10px;
		font-size: 12px;
		text-align: center;
	}

	.face-bottom {
		text-align: center;

		.img-body {
			height: 100%;
			width: 100%;
		}

		.face-img {
			border: 1px solid #aaa;
			height: 220px;
			width: 90%;
			margin: 0 auto;
			padding: 10px 0;

			&:hover {
				cursor: pointer;
			}

		}


	}
}

el-upload-list__item-thumbnail {
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.disable {
	pointer-events: none;
	color: #e5e5e5;

	:deep(.el-input) {
		color: #e5e5e5;
	}

	:deep(.el-form-item__label) {
		color: #e5e5e5;
	}
}

:deep(.el-upload-list--picture-card) {

	li {
		width: 96px;
		height: 96px;
	}
}

:deep(.el-upload--picture-card) {
	width: 170px;
	height: 210px;
	display: flex;
	align-items: center;
}


:deep(.face-bottom) {
	.el-upload {
		border: 1px solid rgb(170, 170, 170);
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}
}
</style>
