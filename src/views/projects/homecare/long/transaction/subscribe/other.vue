<template>
	<div v-loading="loading" class="ut-body">
        <div class="info-box">
            <el-row>
                <el-col :span="8">
                    <span>姓名：</span>
                    <span>{{ form.name }}</span>
                </el-col>
                <el-col :span="8">
                    <span>身份证：</span>
                    <span>{{ form.idcard }}</span>
                </el-col>
                <el-col :span="8">
                    <span>电话：</span>
                    <span>{{ form.phone }}</span>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <span>性别：</span>
                    <span v-if="form.sex==0">不详</span>
                    <span v-if="form.sex==1">男</span>
                    <span v-if="form.sex==2">女</span>
                </el-col>
                <el-col :span="8">
                    <span>出生日期：</span>
                    <span>{{ form.birthday }}</span>
                </el-col>
                <el-col :span="8">
                    <span>民族：</span>
                    <span>{{ form.nation }}</span>
                </el-col>
            </el-row>
        </div>
        <div >
            <data-set v-if="tabName=='dataSet'" :parent-data="initData" :height="500" />
           
        </div>
		



	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'

// import Relatives from './relatives'
// import Disease from './disease'
// import Drug from './drug'
// import Food from './food'
// import File from './file'
// import Symptom from './symptom'
// import Hobby from './hobby'
import DataSet from './data-set'

export default {
	components:{
        // Relatives,
        // Disease,
        // Drug,
        // Food,
        // File,
        // Symptom,
        // Hobby,
        DataSet,
	},
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
	},
	
	data() {
		return {
			loading: false,
            tabName:'dataSet',
			form: {
				id: '',
				name: '',
				code: '',
				color: '',
				showIndex: '',
				valid:true,
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){

		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarelong/transaction/subscribe/info', {
				communityId:this.comm.id,
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
		
		},

	},
}
</script>
<style lang="scss" scoped>
.info-box{
    line-height: 30px;
    padding: 0 10px 10px 10px;
}


</style>
