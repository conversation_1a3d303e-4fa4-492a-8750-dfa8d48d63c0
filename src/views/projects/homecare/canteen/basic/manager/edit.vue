<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="130px" :model="form" :rules="rules" class="ut-form">
			<el-form-item label="食堂：" prop="canteenName">
				<span>{{ form.canteenName }}</span>
			</el-form-item>
			<el-form-item label="姓名：" prop="name">
				<el-input v-model.trim="form.name" placeholder="管理员姓名" />
			</el-form-item>
			<el-form-item label="联系电话：" prop="phone">
				<el-input v-model.trim="form.phone" placeholder="管理员联系电话" />
			</el-form-item>
		</el-form>
		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
import ColorMixins from '../../../components/color.mixins'
export default {
	
	mixins:[ColorMixins],
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
		parentData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			loading: false,
			form: {
				id: '',
				name: '',
				code: '',
				showIndex: '',
				valid:true,
			},
			rules: {
				name: [{ required: true, trigger: 'blur', message: '请输入姓名' }],
				phone: [{ required: true, trigger: 'blur', message: '请输入联系电话' }],
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){
		if(this.parentData){
			this.form.canteenId = this.parentData.id
			this.form.canteenName = this.parentData.name
		}
		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarecanteen/basic/manager/info', {
				communityId: this.comm.id,
				//module:'long',
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
		
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
				this.$ut.api('homecarecanteen/basic/manager/save', {
					communityId : this.comm.id,
					...this.form,
				}).then(()=>{
					this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
					this.$emit('fetchData')
					this.$emit('close')
				}).finally(()=>{this.loading = false})
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.color-box {
	display: inline-block;
	height: 32px;
	width: 150px;
	border: 1px solid #dcdfe6;
	padding-left: 20px;

	.color-box-text {
		color:#fff;
		mix-blend-mode: difference;
	}
}
.placeholder {
	height: 70px;
}
</style>
