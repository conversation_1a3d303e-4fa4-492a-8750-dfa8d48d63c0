<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="130px" :model="form" :rules="rules" class="ut-form">
			<el-form-item label="食堂：" prop="canteenName">
				<span>{{ form.canteenName }}</span>
			</el-form-item>
			<el-form-item label="窗口：" prop="windowName">
				<span>{{ form.windowName }}</span>
			</el-form-item>
			<el-form-item label="序列号：" prop="sn">
				<el-input v-model.trim="form.sn" placeholder="请输入设备序列号" />
			</el-form-item>
			<el-form-item label="别名：" prop="otherName">
				<el-input v-model.trim="form.otherName" placeholder="" />
			</el-form-item>
			<el-form-item label="设备分类：" prop="deviceClassify">
				<el-input v-model.trim="form.deviceClassify" placeholder="" />
			</el-form-item>
			<el-form-item label="连接方式：" prop="connectType">
				<el-input v-model.trim="form.connectType" placeholder="" />
			</el-form-item>
			<el-form-item label="数据类型：" prop="dataType">
				<el-input v-model.trim="form.dataType" placeholder="" />
			</el-form-item>
			<el-form-item label="启用：" prop="valid">
				<el-switch v-model="form.valid" />
			</el-form-item>
		</el-form>
		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
import ColorMixins from '../../../components/color.mixins'
export default {
	
	mixins:[ColorMixins],
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
		parentData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			loading: false,
			form: {
				id: '',
				otherName: '',
				deviceClassify:'刷卡机',
				connectType:'internet',
				dataType:'json',
				valid:true,
			},
			rules: {
				sn: [{ required: true, trigger: 'blur', message: '请输入序列号' }],
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){
		if(this.parentData){
			this.form.windowId = this.parentData.id
			this.form.windowName = this.parentData.name
			if(this.parentData.parent){
				this.form.canteenId = this.parentData.parent.id
				this.form.canteenName = this.parentData.parent.label
			}
		}
		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarecanteen/basic/info/window/device/info', {
				communityId: this.comm.id,
				//module:'long',
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
		
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
				this.$ut.api('homecarecanteen/basic/info/window/device/save', {
					communityId : this.comm.id,
					...this.form,
				}).then(()=>{
					this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
					this.$emit('fetchData')
					this.$emit('close')
				}).finally(()=>{this.loading = false})
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.color-box {
	display: inline-block;
	height: 32px;
	width: 150px;
	border: 1px solid #dcdfe6;
	padding-left: 20px;

	.color-box-text {
		color:#fff;
		mix-blend-mode: difference;
	}
}
.placeholder {
	height: 70px;
}
</style>
