<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="140"
			:op-fixed="true"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			table-name="long-customer-type"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button">
				<el-button icon="el-icon-plus" type="primary" :disabled="treeNode.type!='window'" @click="handleAdd">增加</el-button>
				<el-button icon="el-icon-delete" type="danger" :disabled="!selectRows.length" @click="handleDelete($event)">删除</el-button>
			</template>
			<template #op="{ row }">
				<el-button type="text" @click="handleEdit(row)">编辑</el-button>
				<el-button type="text" @click="handleDelete(row)">删除</el-button>
			</template>
			<template #cell="{ row, item }">
				<div v-if="item.prop === 'color'" class="color-box">
					<span class="color-preview" :style="{ backgroundColor: row.color }"></span>
					{{ row[item.prop] }}
				</div>
				<div v-else-if="item.prop === 'valid'">
                    <span v-if="row.valid"><i class="el-icon-check" /></span>
                    <span v-else>未启用</span>
				</div>
				<span v-else>{{ row[item.prop] }}</span>
			</template>
		</form-list>

		<el-dialog	v-if="dialogFormVisible" v-dialog-drag :title="selectRow.id?'窗口编辑':'增加窗口'" :visible.sync="dialogFormVisible"	append-to-body width="500px" :destroy-on-close="true" :close-on-click-modal="false" :close-on-press-escape="false"	>
			<edit :init-data="selectRow" :parent-data="treeNode" @fetchData="fetchData" @close="dialogFormVisible=false" />
		</el-dialog>

	</div>
</template>

<script>
import Edit from './edit'
import FormList from '@/views/components/form-list'
import { mapGetters } from 'vuex'
import Vue from 'vue'
export default {
	name: 'CanteenWindowList',
	components: {
		Edit,
		FormList,
	},
	props: {
		height: {
			type: [String,Number],
			default: () => Vue.prototype.$baseTableHeight(1),
		},
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
        treeNode:{
            type:Object,
            default:()=>{},
        }
	},
	data() {
		return {
			listLoading: false,
			fullscreen: false,
			columns: [
				{
					label: '食堂',
					align: 'left',
					prop: 'canteenName',
					width:'260',
					show: true,
				},
				{
					label: '窗口',
					align: 'left',
					prop: 'windowName',
					width:'120',
					show: true,
				},
				{
					label: '设备分类',
					align: 'center',
					prop: 'deviceClassify',
					width:'100',
					show: true,
				},
				{
					label: '别名',
					align: 'left',
					prop: 'otherName',
					minWidth:'200',
					show: true,
				},
				{
					label: '序列号',
					align: 'center',
					prop: 'sn',
					width:'160',
					show: true,
				},
				{
					label: '连接方式',
					align: 'center',
					prop: 'connectType',
					width:'100',
					show: true,
				},
				{
					label: '数据类型',
					align: 'center',
					prop: 'dataType',
					width:'80',
					show: true,
				},
				{
					label: '启用',
					align: 'center',
					prop: 'valid',
					width: '80',
					show: true,
				}
			],
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 20,
				order: '',
			},
			selectRow: {},
			selectRows: [],
			dialogFormVisible: false,
			// otherFormVisible:false,
			// positionFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
    watch:{
        treeNode:{
            deep:true,
            handler(){
                this.fetchData()
            }
        }
    },
	created() {
		// this.fetchData()
	},
	methods: {
		async fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true

			let canteenId=''
			let windowId=''
			if(this.treeNode){
				if(this.treeNode.type=='canteen'){
					canteenId=this.treeNode.id
				}
				if(this.treeNode.type=='window'){
					windowId=this.treeNode.id
				}
			}

			const {data} = await this.$ut.api('homecarecanteen/basic/info/window/device/listpg', {
				communityId:this.comm.id,
                canteenId:canteenId,
				windowId:windowId,
                ...this.page,
            }).finally(()=>{this.listLoading=false})
            this.dataList=data
		},
		
		handleSelect(rows) {
			this.$emit('select', rows)
		},
		onFullscreen(v) {
			this.fullscreen = v
		},
		onSelectRows(rows) {
			this.selectRows = rows
		},
		handleAdd() {
			this.selectRow = {}
			this.dialogFormVisible = true
		},
		handleEdit(row) {
			this.selectRow = row
			this.dialogFormVisible = true
		},
		handleDelete(row) {
			let ids = []
			if (row.id) {
				ids = [row.id]
			} else {
				if (this.selectRows.length > 0) {
					ids = this.selectRows.map((item) => item.id)
				} else {
					this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
					return
				}
			}
			this.$baseConfirm('你确定要删除吗', null, async () => {
				this.$ut.api('homecarecanteen/basic/info/window/device/delete', {
					communityId: this.comm.id,
					module:'long',
                    ids: ids,
                }).then(() => {
                    this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
                    this.fetchData()
                })
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.color-box {
	display: flex;
	align-items: center;
	justify-content: center;

	.color-preview {
		width: 20px;
		height: 20px;
		margin-right: 10px;
        border-radius: 2px;
	}
}

</style>
