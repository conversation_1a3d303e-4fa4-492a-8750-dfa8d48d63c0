<template>
	<div class="tree-box" :style="{height:height}">
		<div v-if="showTool" style="margin-bottom:10px">
            <el-button type="primary" :disabled="!treeSelectNode.id" @click="treeAdd">增加</el-button>
            <el-button type="primary" :disabled="treeSelectNode.level==1 || !treeSelectNode.id" @click="treeEdit">修改</el-button>
            <el-button type="primary" :disabled="treeSelectNode.level==1 || !treeSelectNode.id" @click="treeDelete">删除</el-button>
		</div>
		<div v-loading="loading">
			<ut-tree :tree="treeData" :expanded-keys="expandedKeys" @nodeClick="treeNodeClick">
				<template #icon></template>
			</ut-tree>
		</div>
        <el-dialog v-if="dialogFormVisible" v-dialogDrag append-to-body :close-on-click-modal="false" :title="title" :visible.sync="dialogFormVisible" width="500px">
            <edit ref="parkingAreaEdit" :tree-data="treeSelectNode" :type="type" @fetchData="getTreeData" @close="dialogFormVisible=false" />
        </el-dialog>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import Edit from './edit.vue'

export default {
	components: {
		Edit,
	},
	props: {
		showTool:{
			type:Boolean,
			default:false,
		},
		height:{
			type:Number,
			default:0
		},
		module:{
			type:String,
			default:'',
		}
	},
	data() {
		return {
            type:'',
			treeData: [],
			loading: false,
			treeSelectNode: {},
			dialogFormVisible:false,
			expandedKeys:[],
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
		showEditAndDelete() {
			if (!this.treeSelectNode.id) return false
			if (this.treeSelectNode && this.treeSelectNode.id != undefined) return this.treeSelectNode.id !== this.comm.id
			return true
		},
		title(){
			if(this.type==='add') return '增加'
			else if(this.type==='edit') return '修改'
			return ''
		}
	},
	beforeMount(){
		this.getTreeData()
	},
	created() {
	},
	methods: {
		async getTreeData() {
			this.loading = true
			let arr = []
			arr.push({ label: this.comm.name, id: this.comm.id, pId: '' })
			
			const {data}= await this.$ut.api('homecarecanteen/basic/info/window/treeList', {
				communityId: this.comm.id,
				//module:this.module,
			}).finally(()=>{this.loading = false})
			if(data){
				data.forEach((item) => {
					arr.push({
						...item,
						label: item.name,
						id: item.id,
						pId: item.pId ? item.pId : this.comm.id,
					})
				})
			}
			this.treeData = arr
			if(this.treeData.length){
				this.$set(this.treeData[0],'select',true)
				this.$emit('node-click', {id:''})
			}

		},
		treeNodeClick(node) {
			this.treeSelectNode = node
			let temp = Object.assign({}, node)
			if (node.level==1) {
				temp.id = ''
				temp.isRoot = true
			}else{
				this.$set(this.treeData[0],'select',false)
			}
			this.$emit('node-click', temp)
		},
		treeAdd() {
            this.type='add'
            this.dialogFormVisible=true
		},
		treeEdit() {
			this.type='edit'
            this.dialogFormVisible=true
		},
		treeDelete() {

			this.$baseConfirm('你确定要删除吗', null, async () => {
				this.$ut.api('homecarecustomer/group/delete', {
						ids: [this.treeSelectNode.id],
						communityId: this.comm.id,
						module:this.module,
					})
					.then(() => {
						this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
						const treeDataIndex = this.treeData.findIndex((u) => u.id === this.treeSelectNode.id)
						this.treeData.splice(treeDataIndex, 1)
						this.treeSelectNode = {}
					})
			})
		},
		onTreeNodeEdit(node, parent) {
			const treeDataObj = this.treeData.find((u) => u.id == node.id)
			if (treeDataObj) {
				treeDataObj.label = node.label
			} else {
				this.treeData.push({ label: node.label, id: node.id, pId: parent.id })
			}
		},
	},
}
</script>
