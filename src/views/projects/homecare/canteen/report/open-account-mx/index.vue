<template>
	<div class="audit-body" :class="{ 'ut-fullscreen': fullscreen, 'table-container': true }">
		<div class="audit-left">
			<div class="top-body">
				<canteen-tree :open-node="true" :tree-data="treeData" @node-click="onClickNode" />
			</div>
			<!-- <div class="bottom-body">
				<div>查询统计</div>
				<table class="descriptions">
					<thead>
					<tr>
						<th style="width:80px"></th>
						<th>今日</th>
					</tr>
					</thead>
					<tbody>
						<tr>
							<td>总单据</td>
							<td>{{ tj_today.receiptCount }}</td>
						</tr>
						<tr>
							<td>应收</td>
							<td>{{ tj_today.shouldMoney }}</td>
						</tr>
						<tr>
							<td>实收</td>
							<td>{{ tj_today.trueMoney }}</td>
						</tr>
						<tr>
							<td>现金</td>
							<td>{{ tj_today.cashMoney }}</td>
						</tr>
						<tr>
							<td>刷卡</td>
							<td>{{ tj_today.cardMoney }}</td>
						</tr>
						<tr>
							<td>扫码</td>
							<td>{{ tj_today.codeMoney }}</td>
						</tr>
					</tbody>
				</table>
			</div> -->
		</div>
		<div class="audit-right">
			<form-list
				:loading="listLoading"
				:columns="columns"
				:height="height"
				:op-width="100"
				:op-fixed="true"
				:show-op="false"
				:data-list="dataList"
				:select-window="selectWindow"
				:select-single="selectSingle"
				:show-list="showList"
				@select="handleSelect"
				@fetchData="fetchData"
				@fullscreen="onFullscreen"
				@selectRows="onSelectRows"
			>
				<template slot="button">
					<el-button icon="el-icon-data-line" @click="queryFormVisible=true">筛选</el-button>
					<el-button v-if="isAll" icon="el-icon-files" @click="queryAll">全部</el-button>
				</template>
				<template slot="button2">
					<el-button icon="el-icon-download" :disabled="!dataList.record" @click="exportVisible=true">导出</el-button>
				</template>
				<template #cell="{ row, item }">
					<span v-if="item.prop === 'code'">
						<span>{{ row[item.prop] }}</span>
						<el-tag v-if="row['isUndo']" size="mini" class="refund">撤</el-tag>
					</span>
					<div v-else-if="item.prop === 'sex'">
						<span v-if="row.sex==1">男</span>
						<span v-if="row.sex==2">女</span>
					</div>
					<span v-else>{{ row[item.prop] }}</span>
				</template>
			</form-list>
		</div>
		<el-dialog	v-if="queryFormVisible" v-dialog-drag title="筛选条件"	append-to-body width="600px" :destroy-on-close="true"  :visible.sync="queryFormVisible">
			<query :init-data="query" @close="queryFormVisible=false" @query="queryHandle" />
		</el-dialog>
		<excel-report-export 
			v-model="exportVisible" 
			data-url="homecarecanteen/report/openAccount/mx"
			title="开户记录表"
			:arguments="argumentsData"
            :columns="columns"
            :simple="true"
			:count="dataList.record"
		/>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import FormList from '@/views/components/form-list'
import CanteenTree from '../../basic/info/tree.vue'
import Query from './query.vue'
import ExcelReportExport from '../../../components/excel-report-export'

export default {
	name:'BillsNoAudit',
	components: {
		FormList,
		CanteenTree,
		Query,
		ExcelReportExport,
	},
	props: {
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			fullscreen: false,
			listLoading: false,
			height: this.$baseTableHeight(1),
			columns: [
				{
					align: 'left',
					label: '食堂',
					width: '200',
					prop: 'canteenName',
					show: true,
				},
				{
					label: '开户时间',
					align: 'center',
					prop: 'createTime',
					width: '160',
					sortable: false,
					disableCheck: true,
					show: true,
				},
				{
					label: '开户人',
					align: 'left',
					prop: 'opUserName',
					width: '100',
					sortable: false,
					disableCheck: true,
					show: true,
				},
				{
					label: '客户',
					align: 'left',
					prop: 'name',
					width: '100',
					sortable: false,
					disableCheck: true,
					show: true,
				},
				{
					label: '性别',
					align: 'center',
					prop: 'sex',
					width: '80',
					show: true,
				},
				{
					label: '联系电话',
					align: 'center',
					prop: 'phone',
					width: '120',
					show: true,
				},
				{
					label: '证件号码',
					align: 'center',
					prop: 'idcard',
					width: '160',
					show: true,
				},
				{
					label: '出生日期',
					align: 'center',
					prop: 'birthday',
					width: '120',
					sortable: false,
					show: true,
				},
				{
					label: '年龄',
					align: 'center',
					prop: 'age',
					width: '80',
					sortable: false,
					show: true,
				}
			],
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			treeData: [],
			selectRow: {},
			selectRows: [],
			page: {
				key: '',
				pageindex: 1,
				pagesize: 20,
				order: '',
			},
			tj_today: {},
			tj_all: {},
			// 选中节点
			nodeObj: {},
			returnVisible: false,
			detailVisible: false,
			handVisible: false,
			ids: [],
			typeList: [],
			showUndo:false,
			queryFormVisible:false,
			query:{},
			isAll:false,

			handType:'',
			handTime:{},
			handTimeVisable:false,
			exportVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
		argumentsData(){
			let parmlst=[
				{
					name:'communityId',
					value:this.comm.id,
				},{
					name:'key',
					value:this.page.key,
				},{
					name:'canteenId',
					value: this.nodeObj.id ? this.nodeObj.id : '',
				},{
					name:'begin',
					value:this.query.begin,
				},{
					name:'end',
					value:this.query.end,
				}
			]
			return parmlst
		}
		// title() {
		// 	if (!this.selectRow) return ''
		// 	switch (this.selectRow.typeCode) {
		// 		case 'wyf':
		// 			return '物业费'
		// 		case 'dec':
		// 			return '装修费'
		// 		case 'meter':
		// 			return '三表费'
		// 		case 'place':
		// 			return '车位费'
		// 		case 'car':
		// 			return '车辆费'
		// 		case 'bike':
		// 			return '非机动车费'
		// 		case 'other':
		// 			return '其它收费'
		// 		case 'bill':
		// 			return '账单收费'
		// 	}
		// 	return ''
		// },
	},
	async created() {
		//await this.getTreeData()
		this.fetchData()
		// this.fetchTj_today()
		// this.fetchTj_all()
	},
	methods: {
		// // 得到左侧树
		// async getTreeData() {
		// 	try {
		// 		this.treeloading = true
		// 		const { data } = await this.$ut.api('enfinance/type/list')

		// 		let Arr = [{ name: '全部未交账', id: '0', pId: '' }]
		// 		this.typeList = data
		// 		data.forEach((e) => {
		// 			e.pId = '0'
		// 			Arr.push(e)
		// 		})
		// 		this.treeData = Arr
		// 	} finally {
		// 		this.treeloading = false
		// 	}

		// },
		// 得到树节点
		onClickNode(node) {
			this.nodeObj = node
			this.page.pageindex=1
			this.fetchData()
			// this.fetchTj_today()
			// this.fetchTj_all()
		},

		async fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			try {
				this.listLoading = true
				let param = {
					communityId: this.comm.id,
					canteenId: this.nodeObj.id ? this.nodeObj.id : '',
					...this.query,
					...this.page,
				}
				const { data } = await this.$ut.api('homecarecanteen/report/openAccount/mx', param)
				this.dataList = data
			} finally {
				this.listLoading = false
			}

		},
		// async fetchTj_today() {
		// 	let param = {
		// 		communityId: this.comm.id,
		// 		typeCode: this.nodeObj.code ? this.nodeObj.code : '',
		// 		...this.query,
		// 	}
		// 	const { data } = await this.$ut.api('enfinance/receipt/noAuditTJToday', param)
		// 	this.tj_today = data
		// },
		// async fetchTj_all() {
		// 	let param = {
		// 		communityId: this.comm.id,
		// 		typeCode: this.nodeObj.code ? this.nodeObj.code : '',
		// 		...this.query,
		// 	}
		// 	const { data } = await this.$ut.api('enfinance/receipt/noAuditTJ', param)
		// 	this.tj_all = data
		// },
		handleSelect(rows) {
			this.$emit('select', rows)
		},
		setSelectRows(val) {
			this.selectRows = val
		},
		onFullscreen(v) {
			this.fullscreen = v
		},
		onSelectRows(rows) {
			this.selectRows = rows
		},
		handleDetail(row) {
			this.selectRow = row
			this.detailVisible = true
		},
		closeDetail() {
			this.detailVisible = false
		},
		handleReturn(row) {
			this.ids = []
			this.ids = [row.id]
			this.returnVisible = true
		},
		handleRevoke(row){
			this.$baseConfirm('你确定要撤销本单据吗？', null, async () => {
				this.selectRow=row
				this.showUndo=true
			
			})
		},
		hand(){
			this.$baseConfirm('确定将勾选单据交账吗', null, async () => {
				this.handType='ids'
				this.handVisible=true
			})
		},
		queryHandle(qry){
			this.queryFormVisible=false
			this.isAll=true
			this.query=qry
			this.page.pageindex=1
			this.fetchData()
			// this.fetchTj_today()
			// this.fetchTj_all()
		},
		queryAll(){
			this.query={}
			this.isAll=false
			this.page.pageindex=1
			this.fetchData()
			// this.fetchTj_today()
			// this.fetchTj_all()
		},
		queryTimeHandle(qry){
			this.handType='time'
			this.handTime=qry
			this.handVisible=true
		}
	},
}
</script>

<style lang="scss" scoped>
.audit-body {
	display: flex;

	.audit-left {
		min-width: 240px;
		display: flex;
		flex-direction: column;

		.top-body {
			overflow-y: scroll;
			flex: 1;
		}

		.bottom-body {
			border-radius: 4px;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 10px;
			background-color: rgb(238, 238, 238);

			.pro-item {
				padding: 5px 0;

				.pro-el {
					font-weight: bolder;
				}
			}
		}
	}

	.audit-right {
		flex: 1;
		padding-left: 10px;
		overflow:auto;
	}
}

.refund {
	margin-left: 8px;
}

.descriptions {
	width: 100%;
	border-collapse: collapse;

	th {
		background: #fafafa;
	}

	td {
		background: #fff;
	}

	tr td:first-child {
		background: #fafafa;
		text-align: center;
	}

	th, td {
		border: 1px solid #EBEEF5;
		padding: 4px 2px;
		text-align: center;
	}

}
</style>
