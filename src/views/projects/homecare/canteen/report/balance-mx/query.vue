<template>
	<div>
		<el-form ref="form" class="ut-body ut-form" label-width="110px" :model="form" :rules="rules">
			<el-row :gutter="20">
				<el-col :span="12">
					<el-form-item label="开户时间（起）:" prop="begin">
						<el-date-picker	v-model="form.begin"	format="yyyy年MM月dd日"	value-format="yyyy-MM-dd"  type="date" placeholder="起日期"/>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="开户时间（止）:" prop="end">
						<el-date-picker	v-model="form.end"	format="yyyy年MM月dd日"	value-format="yyyy-MM-dd"  type="date" placeholder="止日期"/>
					</el-form-item>
				</el-col>
				<!-- <el-col :span="12">
					<el-form-item label="实收（起）:" prop="trueMoneyBegin">
						<el-input	v-model="form.trueMoneyBegin" placeholder="实际收款期间起"/>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="实收（止）:" prop="trueMoneyEnd">
						<el-input	v-model="form.trueMoneyEnd"	placeholder="实际收款期间起止"/>
					</el-form-item>
				</el-col> 
				<el-col :span="12">
					<el-form-item label="开户人:" prop="charger">
						<el-input	v-model="form.charger"	placeholder="开户人"/>
					</el-form-item>
				</el-col>-->
			</el-row>            
		</el-form>
		<div class="ut-edit-footer">
			<el-button type="primary" @click="$emit('query',form)">查询</el-button>
			<el-button @click="$emit('close')">取 消</el-button>
		</div>
	</div>
</template>

<script>
// import { mapGetters } from 'vuex'
export default {
	components:{
	},
	props: {
        initData:{
            type:Object,
            default:()=>{},
        }
	},
	data() {
		return {
			rules: {},
			selectCarTypeVisible:false,
			form:{
				endStart:'',
				endEnd:'',
				trueMoneyBegin:'',
				trueMoneyEnd:'',
				charger:'',
			}
		}
	},
	computed: {

	},
	beforeMount(){
        this.form=this.initData
	},
	mounted(){

	},
	methods: {
	},
}
</script>
