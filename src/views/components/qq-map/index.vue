<template>
    <div class="map">
        <div class="nav-bar">
            <el-button @click="$emit('close')">返回</el-button>
            <span>坐标拾取</span>
            <el-button type="primary" :disabled="!loc.module" @click="select">选择地址</el-button>
        </div>
        <iframe id="mapPage" style="margin-top:4px;height:calc(100% - 40px)" width="100%" frameborder=0 :src="getSrc"></iframe>
    </div>
</template>

<script>
export default {
    name: 'QqMap',
    props: {
        mapKey: {
            type: String,
            default: ''
        },
        keyName: {
            type: String,
            default: ''
        },
        lat: {
            type: [String, Number],
            default:''
        },
        lng: {
            type: [String, Number],
            default:''
        }
    },
    data() {
        return {
            loc:{},
        }
    },
    computed: {
        getSrc() {
            var baseUrl = 'https://apis.map.qq.com/tools/locpicker?search=1&type=1&key=' + this.mapKey + '&referer=' + this.keyName
            if (this.lat && this.lng) {
                baseUrl += `&coord=${this.lat},${this.lng}`
            }
            return baseUrl
        }
    },
    mounted() {
        var self = this
        window.addEventListener('message', function(event) {
            // 对于无法识别的地址，直接返回无法选择
            var loc = event.data
            if (loc.poiname === '我的位置' || loc.poiaddress === '') {
                self.loc={}
                self.$baseMessage(`无法识别该地址，请移动地图重新选择`, 'error', 'ut-hey-message-success')
                return false
            }
            if (loc && loc.module === 'locationPicker') { // 防止其他应用也会向该页面post信息，需判断module是否为'locationPicker'
                self.callBack(loc)
                // self.$emit('callback', loc)
            }
        }, false)
    },
    methods:{
        callBack(loc){
            this.loc=loc
            //console.log(loc)
        },
        select(){
            this.$emit('callback',this.loc)
            this.$emit('close')
        }
    }
}
</script>
<style lang="scss" scoped>
.nav-bar{
    display: flex;
    justify-content: space-between;
}
.map {
    width: 100%;
    height: 100%;
}
</style>