<template>
    <ut-query-form>
        <ut-query-form-left-panel v-if="!selectWindow">
            <el-form :inline="true" label-width="0" :model="queryForm" @submit.native.prevent>
                <el-form-item v-if="$slots.button">
                    <slot name="button"></slot>
                </el-form-item>
                <el-form-item v-if="showSearch">
                    <el-input v-model="queryData.key" v-focus :placeholder="searchTip" @input="handleInput" @keydown.native.enter="handleQuery">
                        <template slot="append">
                            <div class="search-button" @click="handleQuery">
                            <i class="el-icon-search" ></i>搜索
                            </div>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item v-if="$slots.button2">
                    <slot name="button2"></slot>
                </el-form-item>
            </el-form>
        </ut-query-form-left-panel>
        <div v-else class="search-box">
            <div class="search-box-key">
                <el-input ref="searchKey" v-model="queryData.key" :placeholder="searchTip" @keyup.enter.native="handleQuery">
                    <template slot="append">
                        <div class="search-button" @click="handleQuery">
                        <i class="el-icon-search" ></i>搜索
                        </div>
                    </template>
                </el-input>
            </div>
            <div v-if="selectWindow" > <el-button @click="handleSelect">设为空值</el-button></div>
            <div v-if="!selectSingle" class="search-box-button">
                    <slot name="select"></slot>
            </div>
        </div>
    </ut-query-form>
</template>
<script>
export default {
    name:'FormSearch',
    directives: {
        focus: {
            inserted(el) {
                el.querySelector('input').focus()
            },
        },
    },
    props: {
        searchTip: {
            type: String,
            default: '输入关键字进行查询',
        },
        queryForm: {
            type: Object,
            default: () => {},
        },
        selectWindow: {
            type: Boolean,
            default: false,
        },
        selectSingle: {
            type: Boolean,
            default: true,
        },
        showSearch:{
            type: Boolean,
            default: true,
        }
    },
    data() {
        return {
            queryData: this.queryForm,
        }
    },
    watch: {
       queryForm(v) {
            this.queryData = v
       },
       queryData(v) {
             this.$emit('update:queryForm', v)
       }
    },
    mounted(){
        if(this.selectWindow){
             this.$refs['searchKey'].focus()
        }
       
    },
    methods: {
        handleQuery(){
            this.$emit('search',this.queryData.key)
        },
        handleSelect(){
            this.$emit('select')
        },
        handleInput(v){
            if(v==='') {this.handleQuery()}
        }
    }


}
</script>

<style lang="scss" scoped>
    :deep(.el-input-group__append){
        padding: 0 !important;
    }
    .search-button{
        height: 30px;
        padding: 6px 8px;
        cursor: pointer;
    }

    .search-box{
        display: flex;
        justify-content: space-between;
        padding-bottom: 8px;
        .search-box-key{
            width:300px;
        }

        .search-box-button{
            padding:  0 20px;
        }
    }
</style>