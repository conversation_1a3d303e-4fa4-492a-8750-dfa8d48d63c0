<template>
    <ut-table-corner v-if="device!='mobile'">
        <div class="stripe-panel">
            <el-checkbox v-model="tableSetting.stripe">斑马纹</el-checkbox>
        </div>
        <div class="border-panel">
            <el-checkbox v-model="tableSetting.border">边框</el-checkbox>
        </div>
        <el-button plain type="primary" class="fullscreen-box" @click="clickFullScreen">
            <ut-icon :icon="tableSetting.isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'" />
        </el-button>
        <el-popover ref="popover" popper-class="table-checkbox" trigger="click">
            <el-radio-group v-model="tableSetting.lineHeight">
                <el-radio label="medium">大</el-radio>
                <el-radio label="small">中</el-radio>
                <el-radio label="mini">小</el-radio>
            </el-radio-group>
            <template #reference>
                <el-button plain type="primary">
                    <ut-icon icon="line-height" />
                </el-button>
            </template>
        </el-popover>
        <el-popover popper-class="table-checkbox" trigger="click">
            <ut-draggable v-bind="dragOptions" :list="pageColumns">
                <div v-for="(item, index) in pageColumns" :key="item + index">
                    <ut-icon icon="drag-drop-line" />
                    <el-checkbox v-model="item.show" :true-labe="'false'" :disabled="item.disableCheck === true" :label="item.label" @change="checkChange(item)">
                        {{ item.label }}
                    </el-checkbox>
                </div>
            </ut-draggable>
            <template #reference>
                <el-button icon="el-icon-setting" plain type="primary" />
            </template>
        </el-popover>
    </ut-table-corner>
</template>

<script>
import { mapGetters } from 'vuex'
import UtDraggable from 'vuedraggable'
export default {
    name:'FormTableCorner',
    components: {
			UtDraggable,
	},
    props: {
        columns: {
            type: Array,
            default: () => [],
        },
        setting: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            tableSetting: this.setting,
            pageColumns:this.columns,
        }
    },
    computed: {
        ...mapGetters({
				theme: 'settings/theme',
				device: 'settings/device',
			}),
        dragOptions() {
            return {
                animation: 600,
                group: 'description',
            }
        },
    },
    watch:{
        setting(v){
            this.tableSetting=v
        },
        tableSetting(v){
            this.$emit('update:setting', v)
        },
        columns:{
            deep:true,
            immediate:true,
            handler(v){
                this.pageColumns=v
            }
        },
        pageColumns:{
            deep:true,
            immediate:true,
            handler(v){
                this.$emit('update:columns',v)
            }
        },
    },
    methods: {
      clickFullScreen() {
            this.tableSetting.isFullscreen = !this.tableSetting.isFullscreen
            this.handleHeight()
            this.$emit('fullscreen',this.tableSetting.isFullscreen)
        },
        handleHeight() {
            if (this.tableSetting.isFullscreen) this.tableSetting.height = this.$baseTableHeight(1) + 166
            else this.tableSetting.height = this.$baseTableHeight(1)
        },
        checkChange(){
           this.$emit('change')
        },
    }


}
</script>

<style lang="scss" scoped>
	.table-container {
		:deep() {
			i {
				cursor: pointer;
			}
		}

		.stripe-panel,
		.border-panel {
			margin: #{math.div($base-margin, 4)} 10px #{math.div($base-margin, 4)} 2px !important;
		}
	}

    .ut-table-corner { 
        .el-button {
            padding: 4px;
        }
        .el-checkbox__labe{
            font-size: 12px;
            padding-left: 4px;
            
        }
        div{
            opacity :.75;
        }
        
    }
    
</style>
<style lang="scss">
	.table-checkbox {
		[class*='ri'] {
			vertical-align: -2.5px;
			cursor: pointer;
		}

		.el-checkbox {
			margin: 5px 0 5px 8px;
		}
	}
</style>