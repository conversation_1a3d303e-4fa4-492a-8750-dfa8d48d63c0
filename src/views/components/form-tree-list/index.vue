<template>
	<div class="ut-tree-list">
        <div v-if="showTree" class="ut-tree" :style="{width:treeWidth+'px' }">
            <el-card :body-style="{height:height+'px',overflow:'auto'}">
                <slot name="tree"/>
            </el-card>
        </div>
        <div :class="showTree?'ut-list':'ut-list-no-tree'" :style="{width:listWidth+'px' }">
            <el-card :body-style="{height:height+'px'}">
                <slot name="list" />
            </el-card>
        </div>
		
	</div>
</template>

<script>

	export default {
        name: 'FormTreeList',
		components: {
		},
		props: {
            treeWidth:{
                type:Number,
                default:200,
            },
            showTree:{
                type:Boolean,
                default:true,
            }
		},
		data() {
			return {
                height: this.$baseTableHeight(1),
                
			}
		},
		computed: {
            listWidth(){
                return 200;
            },
		},
        mounted(){
            this.handleResize()
            window.addEventListener('resize',this.handleResize)
        },
        destroyed(){
            window.removeEventListener('resize',this.handleResize)
        },
		created() {
		
		},
		methods: {
            handleResize(){
                this.height=this.$baseTableHeight(-1)
            },
		},
	}
</script>

<style lang="scss" scoped>
.ut-tree-list{
    padding: 0 0 #{math.div($base-padding, 2)};
    display: flex;

    .ut-tree{
        padding-right: #{math.div($base-padding, 4)};
    }

    .ut-list{
        padding-left: #{math.div($base-padding, 4)};
        flex: 1;
    }

    .ut-list-no-tree{
        flex: 1;
    }

    .el-card{
        margin-bottom: 0;
    }
}

.ut-tree-list.ut-fullscreen{
    padding: #{math.div($base-padding, 4)};
    position: fixed ; 
    top: 0 ;
    left: 0 ;
    z-index: 2002 ;
    box-sizing: border-box ;
    width: 100vw ;
    height: 100vh !important;
    overflow: auto ;
    background-color: #fff;

    :deep(.ut-table-corner){
        top:11px !important;
        right:11px !important;
    }

    .ut-tree, .ut-list{
        height: 100%;
        :deep(.el-card){
            height: 100%;
            .el-card__body{
                height: 100% !important;
            }
        }
    }
    :deep(.ut-list){
        .el-card__body{
            padding: 0;
        }
        .ut-fullscreen{
            position: inherit !important; 
            width:100% !important;
        }
    }
}
</style>