<template>
	<div class="form-list">	
		<form-search v-if="!showList" :query-form="queryForm" :select-window="selectWindow" :show-search="showSearch" :select-single="selectSingle" @select="$emit('select',[{}])" @search="handleQuery" @fetchData="fetchData">
			<template slot="button">
				<slot name="button"></slot>
			</template>
			<template slot="select">
				<el-button icon="el-icon-check" type="primary" :disabled="!selectRows.length" @click="$emit('select', selectRows)">选择</el-button>
			</template>
			<template slot="button2">
				<slot name="button2"></slot>
			</template>
		</form-search>
		<div class="form-list-table flex-sub">
			<form-table-corner v-if="!selectWindow && showCorner" :setting="tableSetting" :columns.sync="pageColumns" @fullscreen="onFullscreen" @change="columnChange" />
			<el-table
				ref="myTable"
				v-loading="loading"
				:border="tableSetting.border"
				:data="dataList.info"
				:height="tableSetting.height"
				:size="tableSetting.lineHeight"
				:stripe="tableSetting.stripe"
				@selection-change="setSelectRows"
			>
				<el-table-column v-if="!selectSingle && !showList && showCheckbox" align="center" type="selection" width="47" />
				<el-table-column
					v-for="(item, index) in finallyColumns"
					:key="index"
					:align="item.align"
					:fixed="item.fixed"
					:label="item.label"
					:prop="item.prop"
					:sortable="item.sortable"
					:show-overflow-tooltip="item.showTooltip"
					:width="item.width"
					:min-width="item.minWidth"
				>
					<template v-for="(item2, index2) in item.sub">
						<el-table-column :key="index2" :align="item2.align ? item2.align : 'center'" :label="item2.label" :prop="item2.prop" :sortable="item2.sortable" :width="item2.width">
							<template #default="{ row }">
								{{ row[item2.prop] }}
							</template>
						</el-table-column>
					</template>
					<template #default="{ row, $index }">
						<span v-if="item.prop === 'index'">{{ queryForm.pagesize * (queryForm.pageindex - 1) + $index + 1 }}</span>
						<slot name="cell" :row="row" :index="index" :item="item"></slot>
						<!-- <span v-else>{{ row[item.prop] }}</span> -->
					</template>
				</el-table-column>

				<el-table-column v-if="selectSingle" fixed="right" label="选择" align="center" width="100">
					<template slot-scope="scope">
						<el-button type="primary" icon="el-icon-circle-check" size="mini" @click="$emit('select', [scope.row])">选择</el-button>
					</template>
				</el-table-column>
				<el-table-column v-if="!selectWindow && showOp" align="center" label="操作" show-overflow-tooltip :width="opWidth" :fixed="opFixed ? 'right' : false">
					<template #default="{ row }">
						<slot name="op" :row="row" />
					</template>
				</el-table-column>
				<el-table-column v-if="!selectWindow && showDetail" align="center" label="详细" show-overflow-tooltip width="80">
					<template #default="{ row }">
						<slot name="detail" :row="row" />
					</template>
				</el-table-column>
				<template #empty>
					<el-image class="ut-data-empty" :src="require('@/assets/empty_images/data_empty.png')" />
				</template>
			</el-table>
		</div>
		<el-pagination
			v-if="showPagination"
			background
			:current-page="queryForm.pageindex"
			layout="total, sizes, prev, pager, next, jumper"
			:page-size="queryForm.pagesize"
			:total="dataList.record"
			@current-change="handleCurrentChange"
			@size-change="handleSizeChange"
		/>
		<div v-if="$slots.footer"  class="list-footer"><slot name="footer"></slot></div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import FormSearch from '@/views/components/form-search'
import FormTableCorner from '@/views/components/form-table-corner/index'

export default {
	name: 'CustomTable',
	components: {
		// CourseEdit,
		FormSearch,
		FormTableCorner,
	},
	props: {
		loading: {
			type: Boolean,
			default: false,
		},
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showIndex:{
			type: Boolean,
			default: true,
		},
		showList: {
			type: Boolean,
			default: false,
		},
		showSearch:{
			type: Boolean,
			default: true,
		},
		showPagination:{
			type:Boolean,
			default: true,
		},
		showCheckbox: {
			type: Boolean,
			default: true,
		},
		showDetail: {
			type: Boolean,
			default: false,
		},
		showOp:{
			type: Boolean,
			default: true,
		},
		showCorner:{
			type: Boolean,
			default: true,
		},
		fullscreen: {
			type: Boolean,
			default: false,
		},
		height: {
			type: [Number, String],
			default: 100,
		},
		opWidth: {
			type: Number,
			default: 100,
		},
		opFixed: {
			type: Boolean,
			default: false,
		},
		columns: {
			type: Array,
			default: () => [],
		},
		dataList: {
			type: Object,
			default: () => {},
		},
		tableName:{
			type: String,
			default: '',
		},
		pageSize:{
			type:Number,
			default:20,
		},
	},
	data() {
		return {
			pageColumns:this.columns,
			tableSetting: {
				isFullscreen: this.fullscreen,
				border: true,
				listLoading: false,
				height: this.height,
				lineHeight: 'small',
				stripe: false,
			},
			selectRows: '',
			queryForm: {
				pageindex: 1,
				pagesize: this.showPagination?this.pageSize:10000,
				key: '',
			},
		}
	},
	computed: {
		...mapGetters({
			userId: 'user/id',
		}),
		finallyColumns() {
			if (this.showList) return this.pageColumns.filter((item) => (item.show||item.show==undefined) && item.list)
			return this.pageColumns.filter((item) => (item.show||item.show==undefined))
		},
	},
	watch: {
		dataList: {
			deep: true,
			immediate: true,
			handler(newVal) {
				this.$nextTick(() => {
					let myTable = this.$refs.myTable
					if (myTable) myTable.doLayout()
					if(newVal.info){
							newVal.info.forEach((row) => {
							if(row._select) this.$refs.myTable.toggleRowSelection(row, true)
						})
					}
					
				})
			},
		},
		height(v) {
			this.tableSetting.height = v
		},
		columns:{
            deep:true,
            immediate:true,
            handler(v){
                this.pageColumns=v
            }
        },
        pageColumns:{
            deep:true,
            immediate:true,
            handler(v){
                this.$emit('update:columns',v)
				this.$nextTick(() => {
					let myTable = this.$refs.myTable
					if (myTable) myTable.doLayout()
				})
            }
        },
	},
	mounted() {
		this.handleResize()
		window.addEventListener('resize', this.handleResize)
	},
	destroyed() {
		window.removeEventListener('resize', this.handleResize)
	},
	created() {
		//this.fetchData()
		
		this.loadUserColumns()
	},
	methods: {
		handleResize() {
			this.$emit('resize')
		},
		setSelectRows(val) {
			this.selectRows = val
			this.$emit('selectRows', val)
		},
		handleSelect(row) {
			if (row) {
				this.$emit('select', [row])
			} else {
				this.$emit('select', this.selectRows)
			}
		},
		onFullscreen(v) {
			this.$emit('fullscreen', v)
		},
		handleSizeChange(val) {
			this.queryForm.pagesize = val
			this.fetchData()
		},
		handleCurrentChange(val) {
			this.queryForm.pageindex = val
			this.fetchData()
		},
		handleQuery() {
			this.queryForm.pageindex = 1
			this.fetchData()
		},

		fetchData() {
			this.$emit('fetchData', this.queryForm)
		},
		columnChange(){
			let tt=''
			if(this.userId) tt+='_'+this.userId
			tt+='_'+this.$route.path
			if(this.tableName) tt+='_'+this.tableName
			try {
				localStorage.setItem(tt, JSON.stringify(this.pageColumns))
			} catch(e) {
				console.log(e)
			}
		},
		loadUserColumns(){
			let tt=''
			if(this.userId) tt+='_'+this.userId
			tt+='_'+this.$route.path
			if(this.tableName) tt+='_'+this.tableName
			let str=localStorage.getItem(tt)
			if(str){
				let arr=JSON.parse(str)
				this.pageColumns=arr
			}
		},
	},
}
</script>
<style lang="scss" scoped>
.list-footer{
	position: absolute;
    bottom: 15px;
    right: 15px;
}
</style>