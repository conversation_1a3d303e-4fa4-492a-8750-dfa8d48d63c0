<template>
	<div class="image-text" @click.stop="$emit('itemClick', text)">
		<el-card v-if="text" shadow="hover" class="image ut-curr-color">
			<div>{{ getFirstText() }}</div>
		</el-card>
		<el-card v-else shadow="hover" class="image no ut-curr-color ut-curr-color-border">
			<div>+</div>
		</el-card>
		<div class="icon-text">{{ text }}</div>
	</div>
</template>

<script>
	export default {
		name: 'ImageText',
		props: {
			text: {
				type: String,
				default: '',
			},
		},
		data() {
			return {}
		},
		methods: {
			getFirstText() {
				if (!this.text) return
				return this.text.substr(0, 1)
			},
			itemClick() {
				this.$emit('itemClick', this.text)
			},
		},
	}
</script>

<style lang="scss" scoped>
	.image-text {
		width: 90px;
		height: 100px;
		text-align: center;
		> .image {
			height: 70px;
			width: 70px;
			border-radius: 4px;
			font-size: 35px;
			line-height: 1.25;
			font-weight: bold;
			opacity: 0.9;
			// color: $base-main-color;
			margin: 4px 10px;
			user-select: none;

			&.no {
				border: 1px dashed;
			}
		}

		> .icon-text {
			text-align: center;
			width: 100%;
			font-weight: bold;
		}
	}
</style>
