<template>
	<div v-loading="loading" class="tj-table">
		<div class="tj-table__button">
			<slot name="button" />
			<el-input v-if="showSearch" ref="searchKey" v-model="queryForm.key" :disabled="disabled" class="tj-table__key-input" :placeholder="searchTip"  @input="handleInput" @keyup.enter.native="fetchData">
				<template slot="append">
					<div v-if="!disabled" class="search-button" @click="fetchData">
						<i class="el-icon-search"></i>搜索
					</div>
					<div v-else>
						<i class="el-icon-search"></i>搜索
					</div>
				</template>
			</el-input>
			<div class="export-box"><el-button icon="el-icon-download" :disabled="disabled" @click="handleExport">导出</el-button></div>
			
		</div>
		<div class="el-table el-table--border el-table--small">
			<div class="el-table__body-wrapper is-scrolling-middle">
				<table v-if="dataList.info?.data?.length>0" cellspacing="0" cellpadding="0" border="0" class="el-table__body">
					<thead class="el-table__body__head">
					<tr v-for="(tr,i) in dataList.info?.head || []" :key="i">
						<th
							v-for="(th,j) in tr" :key="j"
							:colspan="th.colspan" :rowspan="th.rowspan" :data-field="th.field"
							class="hide-th" :class="[`is-${th.align}`]">
							<div class="cell" :style="{width: th.width?th.width+'px':'',textAlign:th.align?th.align:''}">
								{{ th.title }}
							</div>
						</th>
					</tr>
					</thead>
					<tbody>
					<tr v-for="(tr,i) in dataList.info?.data || []" :key="i">
						<td
							v-for="(tdValue,tdKey) in tr" :key="tdKey" class="el-table__cell is-leaf"
							:data-field="tdKey" :colspan="dataRowStyle(tdKey,i)?.colspan" :rowspan="dataRowStyle(tdKey,i)?.rowspan">
							<div
								class="cell" :class="[`is-${dataRowStyle(tdKey,i)?.align}`]"
								:style="`${dataRowStyle(tdKey,i)?.style}`"
								v-html="tdValue"></div>
						</td>
					</tr>
					</tbody>
				</table>
				<el-image v-else class="ut-data-empty" :src="require('@/assets/empty_images/data_empty.png')" />
			</div>
		</div>
		<el-pagination
			v-if="showPagination"
			background
			:current-page="queryForm.pageindex"
			layout="total, sizes, prev, pager, next, jumper"
			:page-size="queryForm.pagesize"
			:total="dataList.record"
			@current-change="handleCurrentChange"
			@size-change="handleSizeChange"
		/>
	</div>
</template>

<script>
export default {
	props: {
		loading: {
			type: Boolean,
			default: false,
		},
		showPagination: {
			type: Boolean,
			default: true,
		},
		searchTip: {
			type: String,
			default: '输入关键字进行查询',
		},
		showSearch:{
			type: Boolean,
			default: true,
		},
		dataList: {
			type: Object,
			default: () => {},
		},
		pageSize: {
			type: Number,
			default: 20,
		},
		disabled:{
			type: Boolean,
			default: false,
		}
	},
	emits: ['fetchData'],
	data() {
		return {
			queryForm: {
				pageindex: 1,
				pagesize: this.showPagination ? this.pageSize : 10000,
				key: '',
			},
		}
	},
	computed: {
		heads() {
			return this.dataList.info?.head || []
		},
		dataStyles() {
			return this.dataList.info?.style || []
		},
		dataStyleMap() {
			const styleArr = []
			console.log(this.dataStyles)
			for (const s of this.dataStyles) {
				for (const styles of s) {
					if (!styles.width) {
						styles.width = 80
					}
					styleArr.push([styles.field, styles])
				}
			}
			return Object.fromEntries(styleArr)
		},
	},
	methods: {
		dataRowStyle(key,i){
			return this.dataStyles[i].find(u=>u.field==key)
		},
		fetchData() {
			this.$emit('fetchData', this.queryForm)
		},
		handleCurrentChange(val) {
			this.queryForm.pageindex = val
			this.fetchData()
		},
		handleSizeChange(val) {
			this.queryForm.pagesize = val
			this.fetchData()
		},
		handleExport() {
			this.$emit('export')
		},
		handleInput(v){
            if(v==='') {this.fetchData()}
        }
	},
}
</script>

<style scoped lang="scss">
.search-button{
	cursor: pointer;
}
// .export-box{
// 	flex: 1;
// 	text-align: right;
// }
.tj-table {
	height: 100%;

	.tj-table__button {
		padding-bottom: 6px;
		display: flex;
		align-items: center;
		gap: 6px;

		:deep(.el-button) {
			margin: 0;
		}

		.tj-table__key-input {
			width: 220px;
		}
	}

	.el-table {
		height: calc(100% - 80px);
		position: relative;
		overflow: auto;
		border: 1px solid #ebeef5;

		&::after, &::before {
			display: none !important;
		}

		.el-table__header-wrapper, .el-table__body-wrapper {
			overflow: visible;
		}

		.el-table__body-wrapper {
			.el-table__body__head {
				position: sticky;
				top: 0;
				z-index: 1;

				th {
					height: 40px;
				}
			}
		}
	}
}
</style>
