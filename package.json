{"name": "ut-admin", "private": true, "scripts": {"start": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "lint:eslint": "eslint {src,mock}/**/*.{vue,js} --fix", "lint:prettier": "prettier {src,mock}/**/*.{html,vue,css,sass,scss,js,md} --write", "lint:stylelint": "stylelint {src,mock}/**/*.{html,vue,css,sass,scss} --fix --cache --cache-location node_modules/.cache/stylelint/", "template": "plop", "build:report": "vue-cli-service build --report", "build:deploy": "start ./deploy.sh", "build:docker": "vue-cli-service build&&docker build --pull --rm -f \"dockerfile\" -t vueadminbeautifulpro:latest \".\"&&docker run --rm -d  -p 80:80/tcp vueadminbeautifulpro:latest", "global:install": "npm install -g nrm cnpm npm-check-updates", "globle:update": "ncu -g", "module:install": "npm install -g cnpm --registry=https://registry.npmmirror.com&&cnpm i image-webpack-loader -D", "module:update": "ncu -u --reject screenfull,@vue/eslint-config-prettier,compression-webpack-plugin,eslint,eslint-plugin-prettier,filemanager-webpack-plugin,sass,sass-loader,webpack --registry https://registry.npm.taobao.org&&npm run module:install", "module:reinstall": "rimraf node_modules&&npm run module:install", "nrm:npm": "nrm use npm", "nrm:taobao": "nrm use taobao"}, "dependencies": {"@babel/parser": "7.19.3", "@ckeditor/ckeditor5-build-classic": "^36.0.1", "@ckeditor/ckeditor5-vue": "^4.0.1", "@claviska/jquery-minicolors": "2.3.6", "@logicflow/core": "1.1.2", "@logicflow/extension": "1.1.2", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "1.0.2", "ant-design-vue": "^1.7.8", "axios": "^0.24.0", "beautifier": "^0.1.7", "body-parser": "1.19.1", "bwip-js": "^4.1.1", "chokidar": "^3.5.3", "clipboard": "^2.0.10", "default-passive-events": "2.0.0", "echarts": "^5.3.0", "element-ui": "^2.15.7", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jquery": "^3.7.1", "js-cookie": "^3.0.1", "jsbarcode": "^3.11.5", "jsencrypt": "^3.2.1", "jspdf": "^2.5.1", "jsplumb": "^2.15.6", "lodash": "^4.17.21", "nprogress": "^0.2.0", "nzh": "^1.0.11", "resize-detector": "^0.3.0", "screenfull": "^5.2.0", "socket.io-client": "^4.7.2", "sortablejs": "1.15.3", "util": "^0.12.4", "video.js": "^8.0.4", "videojs-contrib-hls": "^5.15.0", "vue": "2.7.7", "vue-barcode": "^1.3.0", "vue-codemirror": "^4.0.6", "vue-i18n": "^8.27.0", "vue-json-viewer": "^2.2.21", "vue-quill-editor": "^3.0.6", "vue-router": "^3.5.3", "vue2-editor": "^2.10.3", "vuex": "^3.6.2", "xlsx": "^0.17.5"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.19", "@vue/cli-plugin-eslint": "^4.5.19", "@vue/cli-plugin-pwa": "^4.5.19", "@vue/cli-plugin-router": "^4.5.19", "@vue/cli-plugin-vuex": "^4.5.19", "@vue/cli-service": "^4.5.19", "@vue/eslint-config-prettier": "^6.0.0", "ace-builds": "^1.4.12", "canvg": "^3.0.10", "compression-webpack-plugin": "^6.1.1", "core-js": "^3.38.1", "dayjs": "^1.10.7", "eslint": "^7.12.1", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.4.1", "filemanager-webpack-plugin": "3.1.1", "less-loader": "^6.2.0", "lint-staged": "^12.3.4", "mockjs": "^1.1.0", "mux.js": "^6.3.0", "plop": "^3.0.5", "postcss": "^8.4.14", "postcss-html": "^1.3.0", "postcss-jsx": "^0.36.4", "postcss-scss": "^4.0.3", "postcss-syntax": "^0.36.2", "prettier": "^2.5.1", "qs": "^6.10.3", "raw-loader": "4.0.2", "register-service-worker": "^1.7.2", "sass": "1.77.6", "sass-loader": "^10.2.0", "stylelint": "^14.5.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recess-order": "^3.0.0", "svg-sprite-loader": "^6.0.11", "vue-eslint-parser": "^8.2.0", "vue-template-compiler": "^2.6.14", "vuedraggable": "^2.24.3", "webpack": "4.46.0", "webpackbar": "^5.0.2"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,vue}": ["vue-cli-service lint", "git add"]}}